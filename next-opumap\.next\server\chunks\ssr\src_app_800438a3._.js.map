{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/FloatingBackground.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst FloatingBackground = () => {\r\n  return (\r\n    <StyledWrapper>\r\n      <svg id=\"background_svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 800\" fill=\"none\" preserveAspectRatio=\"xMidYMid slice\">\r\n        <defs>\r\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--primary-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--primary-light)\" />\r\n          </linearGradient>\r\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"100%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--accent-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--accent-light)\" />\r\n          </linearGradient>\r\n        </defs>\r\n\r\n        {/* Dynamic Shapes inspired by loader.tsx geometric patterns */}\r\n        <path\r\n          className=\"shape float-animation\"\r\n          d=\"M 500 0 C 400 150, 600 250, 500 400 S 700 550, 800 400 S 900 250, 800 100 S 600 0, 500 0 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <circle\r\n          className=\"shape float-animation-alt\"\r\n          cx=\"1000\"\r\n          cy=\"200\"\r\n          r=\"150\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <path\r\n          className=\"shape float-animation-delay\"\r\n          d=\"M 200 600 Q 300 750, 500 700 T 700 650 Q 600 500, 400 550 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.15\"\r\n        />\r\n        <ellipse\r\n          className=\"shape float-animation-longer\"\r\n          cx=\"300\"\r\n          cy=\"100\"\r\n          rx=\"100\"\r\n          ry=\"50\"\r\n          transform=\"rotate(45 300 100)\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n\r\n        {/* Pulsating dots representing connection points */}\r\n        <circle className=\"dot pulse-animation-1\" cx=\"400\" cy=\"200\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-2\" cx=\"700\" cy=\"500\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-3\" cx=\"1100\" cy=\"100\" r=\"8\" fill=\"var(--primary)\" />\r\n\r\n        {/* Connecting lines between dots */}\r\n        <line\r\n          className=\"connecting-line draw-animation-1\"\r\n          x1=\"400\"\r\n          y1=\"200\"\r\n          x2=\"700\"\r\n          y2=\"500\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n        <line\r\n          className=\"connecting-line draw-animation-2\"\r\n          x1=\"700\"\r\n          y1=\"500\"\r\n          x2=\"1100\"\r\n          y2=\"100\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n      </svg>\r\n    </StyledWrapper>\r\n  );\r\n};\r\n\r\nconst StyledWrapper = styled.div`\r\n  position: fixed;\r\n  inset: 0;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n  width: 100vw;\r\n  height: 100vh;\r\n\r\n  #background_svg {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .shape {\r\n    transform-origin: center center;\r\n  }\r\n\r\n  /* Custom CSS Variables for gradients and colors, assuming these exist in the theme */\r\n  /* If not, these would need to be defined or mapped to existing Tailwind colors */\r\n  --primary-dark: hsl(var(--primary));\r\n  --primary-light: hsl(var(--primary) / 0.7);\r\n  --accent-dark: hsl(var(--accent));\r\n  --accent-light: hsl(var(--accent) / 0.7);\r\n\r\n\r\n  @keyframes floatAndBounce {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(10px, -20px) rotate(5deg); }\r\n    50% { transform: translate(-10px, 20px) rotate(-5deg); }\r\n    75% { transform: translate(5px, -10px) rotate(2deg); }\r\n  }\r\n\r\n  @keyframes floatAndBounceAlt {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(-15px, 10px) rotate(-3deg); }\r\n    50% { transform: translate(15px, -10px) rotate(3deg); }\r\n    75% { transform: translate(-5px, 5px) rotate(-1deg); }\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); opacity: 0.8; }\r\n    50% { transform: scale(1.2); opacity: 1; }\r\n  }\r\n\r\n  @keyframes drawLine {\r\n    to {\r\n      stroke-dashoffset: 0;\r\n    }\r\n  }\r\n\r\n  .float-animation {\r\n    animation: floatAndBounce 20s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-alt {\r\n    animation: floatAndBounceAlt 25s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-delay {\r\n    animation: floatAndBounce 22s infinite ease-in-out 2s; /* Add a delay */\r\n  }\r\n\r\n  .float-animation-longer {\r\n    animation: floatAndBounceAlt 28s infinite ease-in-out 4s; /* Longer duration, more delay */\r\n  }\r\n\r\n  .pulse-animation-1 {\r\n    animation: pulse 2s infinite ease-in-out;\r\n  }\r\n\r\n  .pulse-animation-2 {\r\n    animation: pulse 2s infinite ease-in-out 0.5s;\r\n  }\r\n\r\n  .pulse-animation-3 {\r\n    animation: pulse 2s infinite ease-in-out 1s;\r\n  }\r\n\r\n  .draw-animation-1 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 1s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n\r\n  .draw-animation-2 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 3s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n`;\r\n\r\nexport default FloatingBackground; "], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAAqB;IACzB,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,IAAG;YAAiB,OAAM;YAA6B,SAAQ;YAAe,MAAK;YAAO,qBAAoB;;8BACjH,8OAAC;;sCACC,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC1D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;sCAEhC,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAO,IAAG;4BAAO,IAAG;;8CAC5D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;;;;;;;8BAKlC,8OAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,aAAY;;;;;;8BAId,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAO,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BAGxE,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;8BAEnB,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;;;;;;;;;;;;AAK3B;AAEA,MAAM,gBAAgB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FjC,CAAC;uCAEc", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/Login.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useContext, FormEvent, useEffect } from 'react';\r\nimport { AuthContext } from '@/contexts/AuthContext';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Mail, Lock, User, ArrowRight, Check, AlertCircle, Loader2 } from 'lucide-react';\r\nimport FloatingBackground from '../Landingpage/components/shared/FloatingBackground';\r\nconst Login: React.FC = () => {\r\n  const authContext = useContext(AuthContext);\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [isRegister, setIsRegister] = useState<boolean>(false);\r\n  const [name, setName] = useState<string>(\"\");\r\n  const [email, setEmail] = useState<string>(\"\");\r\n  const [password, setPassword] = useState<string>(\"\");\r\n  const [message, setMessage] = useState<string | null>(null);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isAnimating, setIsAnimating] = useState<boolean>(false);\r\n  const [formVariant, setFormVariant] = useState<'login' | 'register'>('login');\r\n\r\n  // Toggle between login and register forms\r\n  const _toggleForm = () => {\r\n    setFormVariant(prev => prev === 'login' ? 'register' : 'login');\r\n    setError(null);\r\n    setMessage(null);\r\n  };\r\n\r\n  // Update isRegister when formVariant changes\r\n  useEffect(() => {\r\n    setIsRegister(formVariant === 'register');\r\n  }, [formVariant]);\r\n\r\n  // Check for message parameter in URL (e.g., from password reset)\r\n  useEffect(() => {\r\n    const messageParam = searchParams.get('message');\r\n    if (messageParam === 'password-updated') {\r\n      setMessage('Ihr Passwort wurde erfolgreich aktualisiert. Sie können sich jetzt mit Ihrem neuen Passwort anmelden.');\r\n\r\n      // Clear any existing errors\r\n      setError(null);\r\n\r\n      // Focus the email field after a short delay to improve UX\r\n      setTimeout(() => {\r\n        const emailInput = document.getElementById('email');\r\n        if (emailInput) {\r\n          emailInput.focus();\r\n        }\r\n      }, 500);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  if (!authContext) {\r\n    console.error(\"AuthContext not found. Ensure Login component is wrapped in AuthProvider.\");\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-[var(--color-background)]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto\"></div>\r\n          <p className=\"mt-4 text-[var(--color-foreground)]\">Laden...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const { signIn, signUp, signInWithGoogle, isLoading: isAuthLoading } = authContext; // Add isLoading\r\n\r\n  // Show loading indicator if auth is loading\r\n  if (isAuthLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-[var(--color-background)]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto\"></div>\r\n          <p className=\"mt-4 text-[var(--color-foreground)]\">Authentifizierung wird geladen...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n    setError(null);\r\n    setMessage(null);\r\n\r\n    try {\r\n      if (isRegister) {\r\n        const { error } = await signUp(email, password);\r\n        if (error) {\r\n          setError(error.message || \"Registrierung fehlgeschlagen\");\r\n        } else {\r\n          setMessage(\"Registrierung erfolgreich. Bitte bestätigen Sie Ihre E-Mail-Adresse und melden Sie sich dann an.\");\r\n          setIsRegister(false);\r\n          setName(\"\");\r\n          setEmail(\"\");\r\n          setPassword(\"\");\r\n        }\r\n      } else {\r\n        const { error } = await signIn(email, password);\r\n        if (error) {\r\n          setError(error.message || \"Login fehlgeschlagen\");\r\n        } else {\r\n          router.push('/');\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error(`${isRegister ? 'Registration' : 'Login'} error:`, err);\r\n      setError(\"Netzwerkfehler oder Server nicht erreichbar.\");\r\n    }\r\n  };\r\n\r\n  const handleGoogleSignIn = async () => {\r\n    setError(null);\r\n    setMessage(null);\r\n    if (isAuthLoading) { // Prevent action if auth is still loading\r\n        setError(\"Authentifizierung wird noch geladen. Bitte warten Sie einen Moment.\");\r\n        return;\r\n    }\r\n    if (signInWithGoogle) {\r\n      const { error } = await signInWithGoogle();\r\n      if (error) {\r\n        setError(error.message || \"Google Login fehlgeschlagen\");\r\n      } else {\r\n        // Redirect will be handled by Supabase and the callback route\r\n        // router.push('/'); // No need to push here\r\n      }\r\n    } else {\r\n      setError(\"Google Login Funktion nicht verfügbar.\");\r\n    }\r\n  };\r\n\r\n  // Function to handle back to landing page with animation\r\n  const handleBackToLanding = () => {\r\n    if (isAnimating) return; // Prevent multiple clicks during animation\r\n\r\n    console.log(\"Transitioning back to landing page\");\r\n    setIsAnimating(true);\r\n\r\n    // Add class to prevent scrollbars during transition\r\n    document.body.classList.add('page-transition-active');\r\n\r\n    // Navigate after a very short delay\r\n    setTimeout(() => {\r\n      router.push('/');\r\n      // Remove class after navigation\r\n      setTimeout(() => {\r\n        document.body.classList.remove('page-transition-active');\r\n      }, 50);\r\n    }, 100); // Short delay for smoother transition\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative min-h-screen bg-gradient-to-br from-background to-muted/20 overflow-hidden\">\r\n      <FloatingBackground />\r\n      \r\n      <div className=\"relative z-10 min-h-screen flex items-center justify-center p-4\">\r\n        <motion.div \r\n          key={formVariant}\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -20 }}\r\n          transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}\r\n          className=\"w-full max-w-md\"\r\n        >\r\n          <motion.div \r\n            className=\"bg-card/80 backdrop-blur-lg rounded-2xl shadow-2xl overflow-hidden border border-border/30\"\r\n            initial={{ scale: 0.98, opacity: 0.9 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}\r\n          >\r\n            <div className=\"p-1 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10\">\r\n              <div className=\"bg-card/80 rounded-xl p-8\">\r\n                <div className=\"text-center mb-8\">\r\n                  <motion.h1 \r\n                    className=\"text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80 mb-2\"\r\n                    initial={{ opacity: 0, y: -10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.1 }}\r\n                  >\r\n                    {formVariant === 'login' ? 'Willkommen zurück' : 'Konto erstellen'}\r\n                  </motion.h1>\r\n                  <motion.p \r\n                    className=\"text-muted-foreground\"\r\n                    initial={{ opacity: 0, y: -5 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.2 }}\r\n                  >\r\n                    {formVariant === 'login' \r\n                      ? 'Melden Sie sich an, um fortzufahren' \r\n                      : 'Erstellen Sie Ihr Konto, um loszulegen'}\r\n                  </motion.p>\r\n                </div>\r\n\r\n                <AnimatePresence mode=\"wait\">\r\n                  {message && (\r\n                    <motion.div \r\n                      initial={{ opacity: 0, height: 0 }}\r\n                      animate={{ opacity: 1, height: 'auto' }}\r\n                      exit={{ opacity: 0, height: 0 }}\r\n                      className=\"mb-6 p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-600 dark:text-green-400 text-sm\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <Check className=\"w-4 h-4 mr-2 flex-shrink-0\" />\r\n                        <span>{message}</span>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                  {error && (\r\n                    <motion.div \r\n                      initial={{ opacity: 0, height: 0 }}\r\n                      animate={{ opacity: 1, height: 'auto' }}\r\n                      exit={{ opacity: 0, height: 0 }}\r\n                      className=\"mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 text-sm\"\r\n                    >\r\n                      <div className=\"flex items-start\">\r\n                        <AlertCircle className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0\" />\r\n                        <span>{error}</span>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n                </AnimatePresence>\r\n\r\n                <div className=\"mb-6 text-center\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={_toggleForm}\r\n                    className=\"text-sm font-medium text-primary hover:text-primary/80 transition-colors mb-4\"\r\n                    disabled={isAuthLoading}\r\n                  >\r\n                    {formVariant === 'login' \r\n                      ? 'Noch kein Konto? Jetzt registrieren' \r\n                      : 'Bereits registriert? Zum Login'}\r\n                  </button>\r\n                </div>\r\n\r\n                <form onSubmit={handleSubmit} className=\"space-y-5\">\r\n                  <AnimatePresence mode=\"wait\">\r\n                    {formVariant === 'register' && (\r\n                      <motion.div\r\n                        key=\"name-field\"\r\n                        initial={{ opacity: 0, x: -10 }}\r\n                        animate={{ opacity: 1, x: 0 }}\r\n                        exit={{ opacity: 0, x: 10 }}\r\n                        transition={{ duration: 0.2 }}\r\n                        className=\"relative\"\r\n                      >\r\n                        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                          <User className=\"h-5 w-5 text-muted-foreground\" />\r\n                        </div>\r\n                        <input\r\n                          id=\"name\"\r\n                          type=\"text\"\r\n                          value={name}\r\n                          onChange={(e) => setName(e.target.value)}\r\n                          className=\"w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60\"\r\n                          placeholder=\"Vollständiger Name\"\r\n                          required\r\n                        />\r\n                      </motion.div>\r\n                    )}\r\n                  </AnimatePresence>\r\n\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.1 }}\r\n                    className=\"relative\"\r\n                  >\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Mail className=\"h-5 w-5 text-muted-foreground\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"email\"\r\n                      type=\"email\"\r\n                      value={email}\r\n                      onChange={(e) => setEmail(e.target.value)}\r\n                      className=\"w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60\"\r\n                      placeholder=\"E-Mail-Adresse\"\r\n                      required\r\n                    />\r\n                  </motion.div>\r\n\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.2 }}\r\n                    className=\"relative\"\r\n                  >\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Lock className=\"h-5 w-5 text-muted-foreground\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"password\"\r\n                      type=\"password\"\r\n                      value={password}\r\n                      onChange={(e) => setPassword(e.target.value)}\r\n                      className=\"w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60\"\r\n                      placeholder=\"Passwort\"\r\n                      minLength={formVariant === 'register' ? 6 : undefined}\r\n                      required\r\n                    />\r\n                  </motion.div>\r\n\r\n                  {formVariant === 'login' && (\r\n                    <motion.div \r\n                      initial={{ opacity: 0, y: 5 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.3 }}\r\n                      className=\"flex items-center justify-end -mt-3\"\r\n                    >\r\n                      <Link \r\n                        href=\"/forgot-password\" \r\n                        className=\"text-sm font-medium text-primary hover:underline hover:text-primary/80 transition-colors\"\r\n                      >\r\n                        Passwort vergessen?\r\n                      </Link>\r\n                    </motion.div>\r\n                  )}\r\n\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    disabled={isAuthLoading}\r\n                    initial={{ opacity: 0, y: 10 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.3 }}\r\n                    whileHover={{ scale: 1.02 }}\r\n                    whileTap={{ scale: 0.98 }}\r\n                    className={`w-full py-3 px-6 rounded-xl bg-gradient-to-r from-primary to-primary/80 text-white font-medium shadow-lg hover:shadow-primary/20 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 flex items-center justify-center space-x-2 ${\r\n                      isAuthLoading ? 'opacity-80 cursor-not-allowed' : ''\r\n                    }`}\r\n                  >\r\n                    {isAuthLoading ? (\r\n                      <>\r\n                        <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                        <span>Bitte warten...</span>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <span>{formVariant === 'login' ? 'Anmelden' : 'Konto erstellen'}</span>\r\n                        <ArrowRight className=\"h-4 w-4\" />\r\n                      </>\r\n                    )}\r\n                  </motion.button>\r\n                </form>\r\n\r\n                <div className=\"relative my-6\">\r\n                  <div className=\"absolute inset-0 flex items-center\">\r\n                    <div className=\"w-full border-t border-border/30\"></div>\r\n                  </div>\r\n                  <div className=\"relative flex justify-center\">\r\n                    <span className=\"px-3 bg-card text-muted-foreground text-sm\">\r\n                      Oder\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={handleGoogleSignIn}\r\n                  disabled={isAuthLoading}\r\n                  className=\"w-full flex items-center justify-center py-3 px-6 rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors duration-200 mb-6\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <svg \r\n                    className=\"w-5 h-5 mr-2\" \r\n                    viewBox=\"0 0 24 24\" \r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    role=\"img\"\r\n                    aria-label=\"Google logo\"\r\n                    focusable=\"false\"\r\n                  >\r\n                    <g transform=\"matrix(1, 0, 0, 1, 27.009001, -39.238998)\">\r\n                      <path fill=\"#4285F4\" d=\"M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z\"/>\r\n                      <path fill=\"#34A853\" d=\"M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z\"/>\r\n                      <path fill=\"#FBBC05\" d=\"M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z\"/>\r\n                      <path fill=\"#EA4335\" d=\"M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z\"/>\r\n                    </g>\r\n                  </svg>\r\n                  <span className=\"font-medium\">Mit Google anmelden</span>\r\n                </motion.button>\r\n\r\n                <div className=\"text-center\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleBackToLanding}\r\n                    className=\"text-sm font-medium text-primary hover:text-primary/80 transition-colors\"\r\n                    disabled={isAuthLoading}\r\n                  >\r\n                    Zurück zur Startseite\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AASA,MAAM,QAAkB;IACtB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,+HAAA,CAAA,cAAW;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAErE,0CAA0C;IAC1C,MAAM,cAAc;QAClB,eAAe,CAAA,OAAQ,SAAS,UAAU,aAAa;QACvD,SAAS;QACT,WAAW;IACb;IAEA,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,gBAAgB;IAChC,GAAG;QAAC;KAAY;IAEhB,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,aAAa,GAAG,CAAC;QACtC,IAAI,iBAAiB,oBAAoB;YACvC,WAAW;YAEX,4BAA4B;YAC5B,SAAS;YAET,0DAA0D;YAC1D,WAAW;gBACT,MAAM,aAAa,SAAS,cAAc,CAAC;gBAC3C,IAAI,YAAY;oBACd,WAAW,KAAK;gBAClB;YACF,GAAG;QACL;IACF,GAAG;QAAC;KAAa;IAEjB,IAAI,CAAC,aAAa;QAChB,QAAQ,KAAK,CAAC;QACd,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAI3D;IAEA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,WAAW,aAAa,EAAE,GAAG,aAAa,gBAAgB;IAEpG,4CAA4C;IAC5C,IAAI,eAAe;QACjB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAI3D;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,IAAI,YAAY;gBACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO,IAAI;gBAC5B,OAAO;oBACL,WAAW;oBACX,cAAc;oBACd,QAAQ;oBACR,SAAS;oBACT,YAAY;gBACd;YACF,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO;oBACT,SAAS,MAAM,OAAO,IAAI;gBAC5B,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,GAAG,aAAa,iBAAiB,QAAQ,OAAO,CAAC,EAAE;YACjE,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,SAAS;QACT,WAAW;QACX,IAAI,eAAe;YACf,SAAS;YACT;QACJ;QACA,IAAI,kBAAkB;YACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;YACxB,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO,IAAI;YAC5B,OAAO;YACL,8DAA8D;YAC9D,4CAA4C;YAC9C;QACF,OAAO;YACL,SAAS;QACX;IACF;IAEA,yDAAyD;IACzD,MAAM,sBAAsB;QAC1B,IAAI,aAAa,QAAQ,2CAA2C;QAEpE,QAAQ,GAAG,CAAC;QACZ,eAAe;QAEf,oDAAoD;QACpD,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAE5B,oCAAoC;QACpC,WAAW;YACT,OAAO,IAAI,CAAC;YACZ,gCAAgC;YAChC,WAAW;gBACT,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YACjC,GAAG;QACL,GAAG,MAAM,sCAAsC;IACjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wKAAA,CAAA,UAAkB;;;;;0BAEnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;4BAAC;4BAAK;4BAAG;4BAAK;yBAAE;oBAAC;oBACpD,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;4BAAM,SAAS;wBAAI;wBACrC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,MAAM;gCAAC;gCAAK;gCAAG;gCAAK;6BAAE;wBAAC;kCAEpD,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAExB,gBAAgB,UAAU,sBAAsB;;;;;;0DAEnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAE;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAExB,gBAAgB,UACb,wCACA;;;;;;;;;;;;kDAIR,8OAAC,yLAAA,CAAA,kBAAe;wCAAC,MAAK;;4CACnB,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDACjC,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAO;gDACtC,MAAM;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDAC9B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAM;;;;;;;;;;;;;;;;;4CAIZ,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDACjC,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAO;gDACtC,MAAM;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDAC9B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;sEAAM;;;;;;;;;;;;;;;;;;;;;;;kDAMf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDAET,gBAAgB,UACb,wCACA;;;;;;;;;;;kDAIR,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC,yLAAA,CAAA,kBAAe;gDAAC,MAAK;0DACnB,gBAAgB,4BACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,MAAM;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;mDAjBN;;;;;;;;;;0DAuBV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,aAAY;wDACZ,WAAW,gBAAgB,aAAa,IAAI;wDAC5C,QAAQ;;;;;;;;;;;;4CAIX,gBAAgB,yBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;0DAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,MAAK;gDACL,UAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAW,CAAC,kPAAkP,EAC5P,gBAAgB,kCAAkC,IAClD;0DAED,8BACC;;sEACE,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;sEAAK;;;;;;;iFAGR;;sEACE,8OAAC;sEAAM,gBAAgB,UAAU,aAAa;;;;;;sEAC9C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;kDAM9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA6C;;;;;;;;;;;;;;;;;kDAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC;gDACC,WAAU;gDACV,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,cAAW;gDACX,WAAU;0DAEV,cAAA,8OAAC;oDAAE,WAAU;;sEACX,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;;;;;;;;;;;;0DAG3B,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAvOJ;;;;;;;;;;;;;;;;AAkPf;uCAEe", "debugId": null}}]}