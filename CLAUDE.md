# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OpuMap is a Next.js cooperative strategy platform that connects companies via innovative solutions. The project consists of:
- **Frontend**: Next.js 15.3.0 with React 19, TypeScript, and Tailwind CSS
- **Backend**: Next.js API routes with Supabase for database operations
- **Maps Integration**: Google Maps via @react-google-maps/api
- **AI Integration**: OpenAI API for strategy recommendations and business analysis

## Development Commands

### Core Commands
```bash
# Development (recommended)
npm run dev              # Start with turbopack
npm run dev:fast         # Start with turbo mode
npm run start            # Same as dev (aliased)

# Building
npm run build            # Production build
npm run build:analyze    # Build with bundle analysis

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix linting issues automatically

# Optimization
npm run optimize         # Clean and optimize development environment
npm run optimize:db      # Analyze and optimize database

# Cleaning
npm run clean            # Clear .next cache and node_modules cache

# Deployment
npm run deploy           # Deploy locally with PM2
npm run deploy:prod      # Production deployment
npm run pm2:start        # Start PM2 process
npm run pm2:stop         # Stop PM2 process
npm run pm2:logs         # View PM2 logs
```

### Starting Development
The recommended way to start development is using the Python startup script from project root:
```bash
python start_terminals.py
```
This automatically optimizes the environment and starts the development server.

### Working Directory
All development commands should be run from `/next-opumap/` directory, not the project root.

## Architecture

### Hybrid Folder Structure
The project uses a hybrid organizational approach that separates global reusable elements from feature-specific code:

**Global Elements** (in `/src/`):
- `components/` - Reusable UI components
- `hooks/` - Global React hooks
- `utils/` - Utility functions and helpers
- `styles/` - Global styles and theme definitions
- `contexts/` - React contexts (e.g., AuthContext)
- `lib/` - Core libraries and clients (Supabase, API clients)
- `types/` - TypeScript type definitions

**Feature-Specific Elements** (in `/src/app/`):
- `Homepage/` - Main dashboard for authenticated users with Google Maps integration
- `Landingpage/` - Public landing page with 3D floating background
- `opulab/` - Strategy laboratory feature for analysis workflows
- `opuscanner/` - Company scanning and analysis tools
- `profile/` - User profile management and settings
- `login/` - Authentication flows with Google OAuth

Each feature folder contains its own:
- `components/` - Feature-specific components
- `hooks/` - Feature-specific hooks
- `styles/` - Feature-specific styles
- `README.md` - Feature documentation

### Import Conventions
- Global elements: Use absolute paths with TypeScript aliases
  - `import { formatDate } from "@/utils/formatters"`
  - `import { Button } from "@/components/ui/button"`
- Feature-specific: Use relative paths
  - `import { BusinessCard } from "./components/BusinessCard"`

### Key TypeScript Path Aliases
```json
{
  "@/*": ["./src/*"],
  "@components/*": ["./src/components/*"],
  "@hooks/*": ["./src/hooks/*"],
  "@utils/*": ["./src/utils/*"],
  "@styles/*": ["./src/styles/*"],
  "@contexts/*": ["./src/contexts/*"]
}
```

## Database & Authentication

### Database Setup
- **Primary Database**: Supabase (PostgreSQL)
- **Client Libraries**: `@supabase/supabase-js`, `@supabase/ssr`
- **Database Utils**: Located in `src/lib/db.ts` and `src/lib/db-utils.ts`

### Authentication System
- **Provider**: Supabase Auth with Google OAuth
- **Context**: `AuthContext` in `src/contexts/AuthContext.tsx`
- **Key Hook**: `useAuth()` for accessing auth state
- **User Types**: Defined in `src/contexts/AuthContext.tsx`

### API Architecture
- **API Routes**: Located in `src/app/api/`
- **Auth Helper**: `fetchWithAuth()` in `src/lib/supabaseClient.ts`
- **Server Utils**: `src/utils/supabase/server.ts`

## Key Technologies & Libraries

### UI & Styling
- **Component Library**: Radix UI primitives
- **Icons**: Lucide React, React Icons
- **Styling**: Tailwind CSS with custom theme extensions
- **Dark Mode**: `next-themes` package

### Development Tools
- **TypeScript**: Strict mode enabled
- **ESLint**: Next.js + TypeScript configuration
- **Build Tool**: Next.js with Turbopack for development

### External Integrations
- **Maps**: Google Maps JavaScript API via `@react-google-maps/api`
- **AI**: OpenAI API for business analysis and strategy recommendations
- **3D Graphics**: Three.js with React Three Fiber for landing page animations
- **Markdown**: `@uiw/react-md-editor` for rich text editing

## Development Guidelines

### Code Organization Rules
1. **Global vs Feature-Specific**: If used in multiple features or is fundamental functionality → global. If only used within one feature → feature-specific.
2. **Component Structure**: Follow the established pattern with components, hooks, and styles folders.
3. **Documentation**: Each feature should have a README.md explaining its functionality.

### Performance Considerations
- Next.js images must use the `Image` component with proper remote patterns configured
- Database queries should use appropriate indexes (run `npm run optimize:db` for analysis)
- Use React Server Components where appropriate
- Implement proper caching strategies for API routes
- Turbopack is enabled by default for faster development builds
- Bundle analysis available via `npm run build:analyze`

### Environment Configuration
- Environment variables are stored in `.env.local`
- Required variables include Supabase URL/keys and Google Maps API key
- Use `cross-env` for cross-platform environment variable handling

### Error Handling Patterns
- API routes should return consistent error responses
- Client-side errors are handled through auth context and component error boundaries
- Database errors are logged and handled gracefully

## Testing & Quality Assurance

### Code Quality
- ESLint is configured with Next.js and TypeScript rules
- Unused variables/parameters are configured with underscore prefixes (`^_` pattern)
- Strict TypeScript configuration is enforced

### Testing
- **Current Status**: No testing framework is currently configured
- **Manual Testing**: Verification is done through running `npm run build` and checking for TypeScript/ESLint errors
- **Recommendation**: Consider adding Jest or Vitest for unit testing and Playwright for E2E testing

### Pre-deployment Checks
**CRITICAL**: Always run these commands before committing or deploying:
```bash
npm run lint:fix    # Fix linting issues automatically
npm run build       # Ensure TypeScript compilation succeeds
```
These commands must pass without errors to ensure code quality and deployment readiness.

### Performance Monitoring
- Use `npm run build:analyze` to analyze bundle size
- Monitor database performance with the optimization script
- PM2 configuration includes memory limits and automatic restarts

## Deployment

### Environment Options
1. **Local Development**: PM2 with ecosystem.config.js
2. **Docker**: Dockerfile provided for containerization  
3. **Production Server**: Support for Nginx reverse proxy configuration

### Key Files
- `deploy.sh` - Deployment script
- `ecosystem.config.js` - PM2 configuration
- `Dockerfile` - Container configuration
- `DEPLOYMENT.md` - Detailed deployment instructions

## Git Workflow & Development Process

### Branch Strategy
- **Main Branch**: Not explicitly set (check with `git branch -r` for remote main/master)
- **Feature Branches**: Use descriptive names (e.g., `Linter-fixing`, `feature/new-component`)
- **Current Practice**: Working on feature branches with comprehensive changes

### Commit Guidelines
- Run pre-deployment checks (`npm run lint:fix && npm run build`) before committing
- Ensure all TypeScript errors are resolved
- Test the application locally before pushing changes

## Important Notes for AI Development

### File Modification Guidelines
- Always check existing component patterns before creating new ones
- Follow the hybrid folder structure when adding new features
- Respect the import conventions (global vs feature-specific)
- Update feature README.md files when making significant changes

### Common Patterns
- Authentication checks using `useAuth()` hook and `ProtectedRoute` component
- API calls using `fetchWithAuth()` wrapper for automatic token handling
- UI components follow Radix UI + Tailwind CSS patterns
- State management is typically handled through custom hooks within features
- Client-side components use the `ClientOnly` wrapper for SSR compatibility
- Feature-specific API services are co-located (e.g., `opuscanner/apiService.ts`)

### TypeScript Configuration
- Strict mode enabled with performance optimizations
- Path aliases configured for cleaner imports
- Unused variables/parameters allowed in development (set to false)
- Type definitions split between global (`src/types/`) and feature-specific types

### Security Considerations
- All API routes require authentication
- Supabase RLS (Row Level Security) policies are in place
- User session handling is managed through AuthContext
- Never commit API keys or sensitive environment variables