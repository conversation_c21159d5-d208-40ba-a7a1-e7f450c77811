'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMapDemo, Company } from '../../hooks';

export const InteractiveMapDemo: React.FC = () => {
  const { activeStep, hoveredCompany, setHoveredCompany, companies, steps } = useMapDemo();

  return (
    <>
      <div className="relative w-full h-64 sm:h-80 lg:h-96 bg-gradient-to-br from-muted/30 to-muted/10 rounded-2xl sm:rounded-3xl border border-border/20 backdrop-blur-sm">
        {/* Map Grid */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 100 100">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>

        {/* Strategy Area */}
        <motion.div 
          className={`absolute top-2 left-2 sm:top-4 sm:left-4 bg-primary/20 backdrop-blur-md rounded-lg sm:rounded-xl p-2 sm:p-3 border min-w-[140px] sm:min-w-[200px] ${
            activeStep === 0 ? 'border-primary' : 'border-primary/30'
          }`}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <div className="text-xs font-medium text-primary mb-1">Ihre Strategie</div>
          <motion.div 
            className={`text-xs sm:text-sm font-bold mb-1 sm:mb-2 ${
              activeStep === 0 ? 'text-primary' : 'text-foreground'
            }`}
          >
            Lokale Partnerschaften
          </motion.div>        
          
          {/* Strategy Details */}
          <AnimatePresence>
            {activeStep === 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-1 border-t border-primary/20 pt-1 sm:pt-2"
              >
                {[
                  { text: "Ziel: Neue Kunden gewinnen", shortText: "Neue Kunden" },
                  { text: "Branche: Tech & Innovation", shortText: "Tech" },
                  { text: "Radius: 25km", shortText: "Radius: 25km" }
                ].map((item, index) => (
                  <motion.div 
                    key={index}
                    className="text-xs text-primary/80 flex items-center gap-1 sm:gap-2"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * (index + 1) }}
                  >
                    <div className="w-1 h-1 rounded-full bg-primary animate-pulse"></div>
                    <span className="hidden sm:inline">{item.text}</span>
                    <span className="sm:hidden">{item.shortText}</span>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Pulsing Effect */}
          {activeStep === 0 && (
            <motion.div
              className="absolute inset-0 rounded-lg sm:rounded-xl border-2 border-primary/40"
              animate={{ scale: [1, 1.05, 1], opacity: [0.3, 0.6, 0.3] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          )}
        </motion.div>

        {/* Success Badge */}
        <motion.div
          className="absolute -bottom-1 -left-2 sm:-bottom-2 sm:-left-3 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg z-20"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: 1, 
            scale: 1, 
            y: [0, 10, 0] 
          }}
          transition={{
            opacity: { delay: 0.7, duration: 0.7, ease: "circOut" },
            scale: { delay: 0.7, duration: 0.7, ease: "circOut" },
            y: {
              delay: 0.7,
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }
          }}
        >
          <div className="text-lg sm:text-2xl font-bold text-green-500">2.3x</div>
          <div className="text-xs text-muted-foreground">
            <span className="hidden sm:inline">Mehr Partnerschaften</span>
            <span className="sm:hidden">Partner</span>
          </div>
        </motion.div>

        {/* Companies on Map */}
        {companies.map((company, index) => (
          <CompanyMarker
            key={company.id}
            company={company}
            index={index}
            activeStep={activeStep}
            hoveredCompany={hoveredCompany}
            onHover={setHoveredCompany}
          />
        ))}

        {/* Connection Lines */}
        {activeStep >= 2 && (
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            {companies.filter(c => c.match > 75).map((company, index) => (
              <g key={company.id}>
                <motion.line
                  x1="20%"
                  y1="20%"
                  x2={`${company.x}%`}
                  y2={`${company.y}%`}
                  stroke="#3b82f6"
                  strokeWidth="2"
                  strokeDasharray="4,4"
                  opacity={0.6}
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ delay: index * 0.3 + 2, duration: 1 }}
                />
                
                <motion.circle
                  r="2"
                  fill="#3b82f6"
                  opacity={0.8}
                  initial={{ cx: "20%", cy: "20%" }}
                  animate={{ cx: `${company.x}%`, cy: `${company.y}%` }}
                  transition={{ 
                    delay: index * 0.3 + 2.5, 
                    duration: 1.5,
                    repeat: Infinity,
                    repeatType: "loop",
                    repeatDelay: 3
                  }}
                />
              </g>
            ))}
          </svg>
        )}
      </div>

      {/* Info Panel */}
      <InfoPanel activeStep={activeStep} steps={steps} companies={companies} />
    </>
  );
};

interface CompanyMarkerProps {
  company: Company;
  index: number;
  activeStep: number;
  hoveredCompany: number | null;
  onHover: (id: number | null) => void;
}

const CompanyMarker: React.FC<CompanyMarkerProps> = ({
  company,
  index,
  activeStep,
  hoveredCompany,
  onHover
}) => (
  <motion.div
    className={`absolute cursor-pointer transition-all duration-300 ${
      hoveredCompany === company.id 
        ? 'scale-125 sm:scale-150 z-20' 
        : activeStep >= 1 ? 'scale-100' : 'scale-0'
    }`}
    style={{ left: `${company.x}%`, top: `${company.y}%` }}
    initial={{ scale: 0, opacity: 0 }}
    animate={{ 
      scale: activeStep >= 1 ? 1 : 0, 
      opacity: activeStep >= 1 ? 1 : 0 
    }}
    transition={{ delay: index * 0.2 + 1 }}
    onMouseEnter={() => onHover(company.id)}
    onMouseLeave={() => onHover(null)}
  >
    <div 
      className="w-3 h-3 sm:w-4 sm:h-4 rounded-full relative"
      style={{ 
        background: `radial-gradient(circle, hsl(${company.match * 1.2}, 80%, 65%), hsl(${company.match * 1.2}, 60%, 45%))`
      }}
    >
      <div 
        className="absolute inset-0.5 sm:inset-1 rounded-full"
        style={{ backgroundColor: `hsl(${company.match * 1.2}, 90%, 80%)` }}
      />
    </div>

    {/* Pulse Animations */}
    <motion.div 
      className="absolute -inset-1 rounded-full border-2"
      animate={{ scale: [1, 2.2, 1], opacity: [0.4, 0, 0.4] }}
      transition={{ 
        duration: 3, 
        repeat: Infinity, 
        delay: index * 0.5,
        ease: [0.4, 0, 0.6, 1],
        repeatType: "loop"
      }}
      style={{ borderColor: `hsl(${company.match * 1.2}, 70%, 60%)` }}
    />

    {/* Tooltip */}
    <AnimatePresence>
      {hoveredCompany === company.id && (
        <motion.div
          initial={{ opacity: 0, y: 5, scale: 0.9 }}
          animate={{ opacity: 1, y: -8, scale: 1 }}
          exit={{ opacity: 0, y: 5, scale: 0.9 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 bg-background/95 backdrop-blur-sm rounded-lg p-2 border border-border/50 shadow-lg z-30 hidden sm:block"
          style={{
            minWidth: 'max-content',
            maxWidth: '140px',
            left: company.x > 80 ? 'auto' : '50%',
            right: company.x > 80 ? '0' : 'auto',
            transform: company.x > 80 ? 'translateX(0)' : 'translateX(-50%)'
          }}
        >
          <div className="text-xs font-medium mb-1 truncate">{company.name}</div>
          <div className="flex items-center justify-between gap-2">
            <span className="text-xs text-muted-foreground">Match:</span>
            <div className="flex items-center gap-1">
              <div className="w-8 h-1 rounded-full bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 relative overflow-hidden">
                <motion.div
                  className="absolute inset-y-0 left-0 bg-white/40 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${company.match}%` }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                />
              </div>
              <span 
                className="text-xs font-bold tabular-nums" 
                style={{ color: `hsl(${company.match * 1.2}, 70%, 60%)` }}
              >
                {company.match}%
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  </motion.div>
);

interface InfoPanelProps {
  activeStep: number;
  steps: string[];
  companies: Company[];
}

const InfoPanel: React.FC<InfoPanelProps> = ({ activeStep, steps, companies }) => (
  <div className="mt-8 sm:mt-10">
    <motion.div 
      className="bg-background/95 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 p-4 sm:p-6 shadow-lg h-[320px] sm:h-[300px]"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3, duration: 0.6 }}
    >
      {/* Step Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <motion.div 
            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary flex items-center justify-center text-white text-sm sm:text-base font-bold"
            key={activeStep}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {activeStep + 1}
          </motion.div>
          <div>
            <motion.h3 
              className="text-sm sm:text-base font-semibold"
              key={activeStep}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {steps[activeStep]}
            </motion.h3>
            <div className="text-xs text-muted-foreground">
              Schritt {activeStep + 1} von {steps.length}
            </div>
          </div>
        </div>
        
        {/* Progress Dots */}
        <div className="flex gap-1.5">
          {steps.map((_, index) => (
            <motion.div
              key={index}
              className={`w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-colors duration-300 ${
                index <= activeStep ? 'bg-primary' : 'bg-muted-foreground/30'
              }`}
              animate={{ scale: index === activeStep ? 1.2 : 1 }}
            />
          ))}
        </div>
      </div>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        <StepContent 
          activeStep={activeStep} 
          companies={companies} 
        />
      </AnimatePresence>
    </motion.div>
  </div>
);

interface StepContentProps {
  activeStep: number;
  companies: Company[];
}

const StepContent: React.FC<StepContentProps> = ({ activeStep, companies }) => {
  const stepVariants = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 }
  };

  const contentByStep = [
    // Step 0: Strategy Definition
    <motion.div
      key="step-0"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.3 }}
      className="flex flex-col h-full py-4 px-3"
    >
      <h3 className="text-sm font-medium mb-0.5">Strategiedefinition</h3>
      <p className="text-xs text-muted-foreground mb-2.5">Ihre Partnersuche konfigurieren</p>
      
      <div className="grid gap-2 mx-0.5">
        {[
          "Neue Kunden gewinnen",
          "Tech & Innovation", 
          "Radius: 25km"
        ].map((item, index) => (
          <div key={index} className="flex items-center gap-1.5 p-1.5 bg-primary/5 rounded-md border border-primary/20">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <div className="w-1.5 h-1.5 rounded-full bg-primary animate-pulse"></div>
            </div>
            <div className="font-medium text-xs">{item}</div>
          </div>
        ))}
      </div>
    </motion.div>,

    // Step 1: Company Scan
    <motion.div
      key="step-1"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.3 }}
      className="flex flex-col h-full py-5 px-4"
    >
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-base font-medium">Unternehmensscan</h3>
          <p className="text-xs text-muted-foreground">Partner identifizieren</p>
        </div>
        
        <div className="flex items-center gap-1.5 bg-blue-500/10 px-2 py-1 rounded-full">
          <motion.div
            className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <span className="text-xs font-medium text-blue-500">
            {companies.length}
          </span>
        </div>
      </div>
      
      <div>
        <div className="text-xs text-muted-foreground mb-2 ml-1">Gefundene Unternehmen:</div>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2.5 mx-1">
          {companies.slice(0, 4).map((company, index) => (
            <motion.div
              key={company.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="flex flex-col p-1.5 bg-blue-500/10 rounded-lg border border-blue-500/20"
            >
              <div className="font-medium text-xs truncate">{company.name}</div>
              <div className="text-xs text-blue-500/80 mt-0.5">{company.type}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>,

    // Step 2: Analysis
    <motion.div
      key="step-2"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.3 }}
      className="space-y-3 flex flex-col h-full justify-start"
    >
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Matching-Scores werden berechnet...
        </div>
        <div className="text-sm font-semibold text-purple-500">
          {companies.filter(c => c.match > 75).length} potenzielle Partner
        </div>
      </div>
      
      <div className="w-full bg-purple-500/20 rounded-full h-2">
        <motion.div
          className="bg-purple-400 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ delay: 0.5, duration: 2 }}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-1.5 overflow-hidden">
        {companies.filter(c => c.match > 75).map((company, index) => (
          <motion.div
            key={company.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 + index * 0.1 }}
            className="p-2 bg-purple-500/10 rounded-md border border-purple-500/20 min-w-0"
          >
            <div className="flex flex-col min-w-0">
              <div className="text-xs font-medium truncate mb-1.5" title={company.name}>
                {company.name}
              </div>
              <div className="flex items-center justify-between mb-1">
                <div className="w-5 h-0.5 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-white/40 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${company.match}%` }}
                    transition={{ delay: 1.2 + index * 0.1, duration: 0.4 }}
                  />
                </div>
                <span className="text-xs font-bold text-purple-500 ml-1.5">{company.match}%</span>
              </div>
              <div className="text-xs text-muted-foreground/80">Potenzial</div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>,

    // Step 3: Partnerships
    <motion.div
      key="step-3"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.3 }}
      className="space-y-3 flex flex-col h-full justify-start"
    >
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Premium-Partner bereit für Kontakt:
        </div>
        <div className="text-sm font-semibold text-green-500">
          {companies.filter(c => c.match > 85).length} Premium-Partner
        </div>
      </div>
      
      <div className="flex justify-center gap-2 py-2">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="w-3 h-3 rounded-full bg-green-400"
            animate={{ 
              scale: [0.8, 1.3, 0.8], 
              opacity: [0.4, 1, 0.4] 
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              delay: i * 0.3,
              repeatType: "loop"
            }}
          />
        ))}
      </div>
      
      <div className="space-y-2">
        {companies.filter(c => c.match > 85).map((company, index) => (
          <motion.div
            key={company.id}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.2 }}
            className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 flex items-center justify-between"
          >
            <div>
              <div className="text-sm font-medium">{company.name}</div>
              <div className="text-xs text-green-600">Bereit für Partnerschaft</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-bold text-green-500">{company.match}%</div>
              <div className="text-xs text-muted-foreground">Match</div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  ];

  return contentByStep[activeStep] || null;
};