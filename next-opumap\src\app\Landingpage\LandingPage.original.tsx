'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Target, Handshake, MapPin, Network, Compass, Route, Eye, Brain, Zap, Check, LucideIcon } from 'lucide-react';
import FloatingBackground from './components/FloatingBackground';

// Interactive Map Component für die Hero Section
const InteractiveMapDemo = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [hoveredCompany, setHoveredCompany] = useState<number | null>(null);
  
  const companies = [
    { id: 1, name: "TechStart GmbH", x: 25, y: 35, type: "tech", match: 95 },
    { id: 2, name: "Regional Handel", x: 60, y: 55, type: "retail", match: 78 },
    { id: 3, name: "Innovation Hub", x: 40, y: 25, type: "innovation", match: 88 },
    { id: 4, name: "Local Services", x: 75, y: 70, type: "service", match: 65 },
    { id: 5, name: "Manufaktur Nord", x: 15, y: 65, type: "production", match: 82 }
  ];

  const steps = React.useMemo(() => [
    "Ihre Strategie definieren",
    "Lokale Unternehmen scannen", 
    "Potenziale analysieren",
    "Partnerschaften initiieren"
  ], []);
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [steps.length]);

  return (
    <>
      <div className="relative w-full h-64 sm:h-80 lg:h-96 bg-gradient-to-br from-muted/30 to-muted/10 rounded-2xl sm:rounded-3xl border border-border/20 backdrop-blur-sm">
        {/* Map Grid */}
      <div className="absolute inset-0 opacity-20">
        <svg className="w-full h-full" viewBox="0 0 100 100">
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
        </svg>
      </div>

      {/* Strategiebereich - Erweitert mit Animation */}
      <motion.div 
        className={`absolute top-2 left-2 sm:top-4 sm:left-4 bg-primary/20 backdrop-blur-md rounded-lg sm:rounded-xl p-2 sm:p-3 border min-w-[140px] sm:min-w-[200px] ${
          activeStep === 0 ? 'border-primary' : 'border-primary/30'
        }`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: 1, 
          scale: 1
        }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <div className="text-xs font-medium text-primary mb-1">Ihre Strategie</div>
        <motion.div 
          className={`text-xs sm:text-sm font-bold mb-1 sm:mb-2 ${
            activeStep === 0 ? 'text-primary' : 'text-foreground'
          }`}
        >
          Lokale Partnerschaften
        </motion.div>        
        {/* Strategiedetails - nur sichtbar bei Schritt 0 */}
        <AnimatePresence>
          {activeStep === 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-1 border-t border-primary/20 pt-1 sm:pt-2"
            >
              <motion.div 
                className="text-xs text-primary/80 flex items-center gap-1 sm:gap-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <div className="w-1 h-1 rounded-full bg-primary animate-pulse"></div>
                <span className="hidden sm:inline">Ziel: Neue Kunden gewinnen</span>
                <span className="sm:hidden">Neue Kunden</span>
              </motion.div>
              <motion.div 
                className="text-xs text-primary/80 flex items-center gap-1 sm:gap-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="w-1 h-1 rounded-full bg-primary animate-pulse"></div>
                <span className="hidden sm:inline">Branche: Tech & Innovation</span>
                <span className="sm:hidden">Tech</span>
              </motion.div>
              <motion.div 
                className="text-xs text-primary/80 flex items-center gap-1 sm:gap-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="w-1 h-1 rounded-full bg-primary animate-pulse"></div>
                <span>Radius: 25km</span>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Pulsierender Effekt für aktiven Schritt */}
        {activeStep === 0 && (
          <motion.div
            className="absolute inset-0 rounded-lg sm:rounded-xl border-2 border-primary/40"
            animate={{ scale: [1, 1.05, 1], opacity: [0.3, 0.6, 0.3] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        )}
      </motion.div>      {/* ENTFERNT: Scan-Bereich Indikator */}

      {/* NEU & VERSCHOBEN: "2.3x Mehr Partnerschaften" Badge - Unten Links auf der Karte */}
      <motion.div
        className="absolute -bottom-1 -left-2 sm:-bottom-2 sm:-left-3 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg z-20"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: 1, 
          scale: 1, 
          y: [0, 10, 0] 
        }}
        transition={{
          opacity: { delay: 0.7, duration: 0.7, ease: "circOut" },
          scale: { delay: 0.7, duration: 0.7, ease: "circOut" },
          y: {
            delay: 0.7, // Start y-bobbing with opacity/scale animation
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }
        }}
      >
        <div className="text-lg sm:text-2xl font-bold text-green-500">2.3x</div>
        <div className="text-xs text-muted-foreground">
          <span className="hidden sm:inline">Mehr Partnerschaften</span>
          <span className="sm:hidden">Partner</span>
        </div>
      </motion.div>

      {/* Unternehmen auf der Karte - erweitert */}
      {companies.map((company, index) => (
        <motion.div
          key={company.id}
          className={`absolute cursor-pointer transition-all duration-300 ${
            hoveredCompany === company.id 
              ? 'scale-125 sm:scale-150 z-20' 
              : activeStep >= 1 ? 'scale-100' : 'scale-0'
          }`}
          style={{ 
            left: `${company.x}%`, 
            top: `${company.y}%`,
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ 
            scale: activeStep >= 1 ? 1 : 0, 
            opacity: activeStep >= 1 ? 1 : 0 
          }}
          transition={{ delay: index * 0.2 + 1 }}
          onMouseEnter={() => setHoveredCompany(company.id)}
          onMouseLeave={() => setHoveredCompany(null)}
        >
          {/* Hauptpunkt mit Farbverlauf basierend auf Match */}
          <div 
            className="w-3 h-3 sm:w-4 sm:h-4 rounded-full relative"
            style={{ 
              background: `radial-gradient(circle, hsl(${company.match * 1.2}, 80%, 65%), hsl(${company.match * 1.2}, 60%, 45%))`
            }}
          >
            {/* Innerer Punkt */}
            <div 
              className="absolute inset-0.5 sm:inset-1 rounded-full"
              style={{ 
                backgroundColor: `hsl(${company.match * 1.2}, 90%, 80%)`
              }}
            />
            
            {/* Match-Score Ring */}
            <svg className="absolute -inset-0.5 sm:-inset-1 w-4 h-4 sm:w-6 sm:h-6" viewBox="0 0 24 24">
              <circle
                cx="12"
                cy="12"
                r="10"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
                strokeDasharray={`${company.match * 0.628} ${62.8 - company.match * 0.628}`}
                strokeDashoffset="-15.7"
                className="opacity-60"
                style={{ color: `hsl(${company.match * 1.2}, 70%, 60%)` }}
              />
            </svg>
          </div>          <motion.div 
            className="absolute -inset-1 rounded-full border-2"
            animate={{ 
              scale: [1, 2.2, 1], 
              opacity: [0.4, 0, 0.4]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              delay: index * 0.5,
              ease: [0.4, 0, 0.6, 1],
              repeatType: "loop"
            }}
            style={{ borderColor: `hsl(${company.match * 1.2}, 70%, 60%)` }}
          />
          
          {/* Zweite Schicht für mehr Intensität */}
          <motion.div 
            className="absolute -inset-0.5 rounded-full border"
            animate={{ 
              scale: [1, 1.6, 1], 
              opacity: [0.3, 0.1, 0.3] 
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              delay: index * 0.5 + 1,
              ease: [0.4, 0, 0.6, 1], 
              repeatType: "loop"
            }}
            style={{ borderColor: `hsl(${company.match * 1.2}, 60%, 50%)` }}
          />{/* Kompaktes, intelligentes Tooltip */}
          <AnimatePresence>
            {hoveredCompany === company.id && (
              <motion.div
                initial={{ opacity: 0, y: 5, scale: 0.9 }}
                animate={{ opacity: 1, y: -8, scale: 1 }}
                exit={{ opacity: 0, y: 5, scale: 0.9 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
                className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 bg-background/95 backdrop-blur-sm rounded-lg p-2 border border-border/50 shadow-lg z-30 hidden sm:block"
                style={{
                  minWidth: 'max-content',
                  maxWidth: '140px',
                  // Intelligente Positionierung basierend auf Position
                  left: company.x > 80 ? 'auto' : '50%',
                  right: company.x > 80 ? '0' : 'auto',
                  transform: company.x > 80 
                    ? 'translateX(0)' 
                    : 'translateX(-50%)'
                }}
              >
                <div className="text-xs font-medium mb-1 truncate">{company.name}</div>
                <div className="flex items-center justify-between gap-2">
                  <span className="text-xs text-muted-foreground">Match:</span>
                  <div className="flex items-center gap-1">
                    <div 
                      className="w-8 h-1 rounded-full bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 relative overflow-hidden"
                    >
                      <motion.div
                        className="absolute inset-y-0 left-0 bg-white/40 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${company.match}%` }}
                        transition={{ duration: 0.6, delay: 0.1 }}
                      />
                    </div>
                    <span 
                      className="text-xs font-bold tabular-nums" 
                      style={{ color: `hsl(${company.match * 1.2}, 70%, 60%)` }}
                    >
                      {company.match}%
                    </span>
                  </div>                </div>
                
                {/* Kleiner Pfeil nach unten */}
                <div 
                  className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[4px] border-r-[4px] border-t-[4px] border-transparent border-t-border/50"
                  style={{
                    left: company.x > 80 ? '80%' : '50%'
                  }}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      ))}

      {/* Verbindungslinien - erweitert */}
      {activeStep >= 2 && (
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          {companies.filter(c => c.match > 75).map((company, index) => (
            <g key={company.id}>
              {/* Hauptverbindungslinie */}
              <motion.line
                x1="20%"
                y1="20%"
                x2={`${company.x}%`}
                y2={`${company.y}%`}
                stroke="#3b82f6"
                strokeWidth="2"
                strokeDasharray="4,4"
                opacity={0.6}
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ delay: index * 0.3 + 2, duration: 1 }}
              />
              
              {/* Animierte Datenpunkte entlang der Linie */}
              <motion.circle
                r="2"
                fill="#3b82f6"
                opacity={0.8}
                initial={{ 
                  cx: "20%", 
                  cy: "20%" 
                }}
                animate={{ 
                  cx: `${company.x}%`, 
                  cy: `${company.y}%` 
                }}
                transition={{ 
                  delay: index * 0.3 + 2.5, 
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "loop",
                  repeatDelay: 3
                }}
              />
            </g>
          ))}
        </svg>
      )}      {/* Analyse-Bereich - REMOVED */}      {/* ENTFERNT: "2.3x mehr Partnerschaften" Badge mit Zap-Icon */}

      </div> {/* Correctly closing the main map container div that started on line 37 */}

      {/* Mobile/Desktop Info Panel - Dauerhaft unter der Karte */}
      <div className="mt-8 sm:mt-10">
        <motion.div 
          className="bg-background/95 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 p-4 sm:p-6 shadow-lg h-[320px] sm:h-[300px]"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          {/* Schritt Überschrift */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <motion.div 
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary flex items-center justify-center text-white text-sm sm:text-base font-bold"
                key={activeStep}
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {activeStep + 1}
              </motion.div>
              <div>
                <motion.h3 
                  className="text-sm sm:text-base font-semibold"
                  key={activeStep}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  {steps[activeStep]}
                </motion.h3>
                <div className="text-xs text-muted-foreground">
                  Schritt {activeStep + 1} von {steps.length}
                </div>
              </div>
            </div>
            
            {/* Progress Dots */}
            <div className="flex gap-1.5">
              {steps.map((_, index) => (
                <motion.div
                  key={index}
                  className={`w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-colors duration-300 ${
                    index <= activeStep ? 'bg-primary' : 'bg-muted-foreground/30'
                  }`}
                  animate={{ 
                    scale: index === activeStep ? 1.2 : 1 
                  }}
                />
              ))}
            </div>
          </div>

          {/* Schritt-spezifischer Content */}
          <AnimatePresence mode="wait">
            {activeStep === 0 && (
              <motion.div
                key="step-0"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col h-full py-4 px-3"
              >
                {/* Weiter optimierter Header */}
                <h3 className="text-sm font-medium mb-0.5">Strategiedefinition</h3>
                <p className="text-xs text-muted-foreground mb-2.5">Ihre Partnersuche konfigurieren</p>
                
                {/* Kompaktere Strategie-Items */}
                <div className="grid gap-2 mx-0.5">
                  <div className="flex items-center gap-1.5 p-1.5 bg-primary/5 rounded-md border border-primary/20">
                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary animate-pulse"></div>
                    </div>
                    <div className="font-medium text-xs">Neue Kunden gewinnen</div>
                  </div>
                  
                  <div className="flex items-center gap-1.5 p-1.5 bg-primary/5 rounded-md border border-primary/20">
                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary animate-pulse"></div>
                    </div>
                    <div className="font-medium text-xs">Tech & Innovation</div>
                  </div>
                  
                  <div className="flex items-center gap-1.5 p-1.5 bg-primary/5 rounded-md border border-primary/20">
                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary animate-pulse"></div>
                    </div>
                    <div className="font-medium text-xs">Radius: 25km</div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeStep === 1 && (
              <motion.div
                key="step-1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}  
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col h-full py-5 px-4"
              >
                {/* Kompakter Header mit Count und ausreichendem Abstand */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-base font-medium">Unternehmensscan</h3>
                    <p className="text-xs text-muted-foreground">Partner identifizieren</p>
                  </div>
                  
                  <div className="flex items-center gap-1.5 bg-blue-500/10 px-2 py-1 rounded-full">
                    <motion.div
                      className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    <span className="text-xs font-medium text-blue-500">
                      {companies.length}
                    </span>
                  </div>
                </div>
                
                {/* Kompaktere Unternehmensübersicht mit ausreichendem Randabstand */}
                <div>
                  <div className="text-xs text-muted-foreground mb-2 ml-1">Gefundene Unternehmen:</div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2.5 mx-1">
                    {companies.slice(0, 4).map((company, index) => (
                      <motion.div
                        key={company.id}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex flex-col p-1.5 bg-blue-500/10 rounded-lg border border-blue-500/20"
                      >
                        <div className="font-medium text-xs truncate">{company.name}</div>
                        <div className="text-xs text-blue-500/80 mt-0.5">{company.type}</div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}

            {activeStep === 2 && (
              <motion.div
                key="step-2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-3 flex flex-col h-full justify-start"
              >
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Matching-Scores werden berechnet...
                  </div>
                  <div className="text-sm font-semibold text-purple-500">
                    {companies.filter(c => c.match > 75).length} potenzielle Partner
                  </div>
                </div>
                
                {/* Progress Bar */}
                <div className="w-full bg-purple-500/20 rounded-full h-2">
                  <motion.div
                    className="bg-purple-400 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ delay: 0.5, duration: 2 }}
                  />
                </div>
                
                {/* Top Matches */}
                <div className="grid grid-cols-2 gap-1.5 overflow-hidden">
                  {companies.filter(c => c.match > 75).map((company, index) => (
                    <motion.div
                      key={company.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 1 + index * 0.1 }}
                      className="p-2 bg-purple-500/10 rounded-md border border-purple-500/20 min-w-0"
                    >
                      <div className="flex flex-col min-w-0">
                        <div className="text-xs font-medium truncate mb-1.5" title={company.name}>
                          {company.name}
                        </div>
                        <div className="flex items-center justify-between mb-1">
                          <div className="w-5 h-0.5 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full overflow-hidden">
                            <motion.div
                              className="h-full bg-white/40 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${company.match}%` }}
                              transition={{ delay: 1.2 + index * 0.1, duration: 0.4 }}
                            />
                          </div>
                          <span className="text-xs font-bold text-purple-500 ml-1.5">{company.match}%</span>
                        </div>
                        <div className="text-xs text-muted-foreground/80">
                          Potenzial
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeStep === 3 && (
              <motion.div
                key="step-3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-3 flex flex-col h-full justify-start"
              >
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Premium-Partner bereit für Kontakt:
                  </div>
                  <div className="text-sm font-semibold text-green-500">
                    {companies.filter(c => c.match > 85).length} Premium-Partner
                  </div>
                </div>
                
                {/* Success Animation */}
                <div className="flex justify-center gap-2 py-2">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="w-3 h-3 rounded-full bg-green-400"
                      animate={{ 
                        scale: [0.8, 1.3, 0.8], 
                        opacity: [0.4, 1, 0.4] 
                      }}
                      transition={{ 
                        duration: 2, 
                        repeat: Infinity, 
                        delay: i * 0.3,
                        repeatType: "loop"
                      }}
                    />
                  ))}
                </div>
                
                {/* Premium Partners */}
                <div className="space-y-2">
                  {companies.filter(c => c.match > 85).map((company, index) => (
                    <motion.div
                      key={company.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.2 }}
                      className="p-3 bg-green-500/10 rounded-lg border border-green-500/20 flex items-center justify-between"
                    >
                      <div>
                        <div className="text-sm font-medium">{company.name}</div>
                        <div className="text-xs text-green-600">Bereit für Partnerschaft</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-bold text-green-500">{company.match}%</div>
                        <div className="text-xs text-muted-foreground">Match</div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </>
  );
};

// Einzigartiger Feature Card mit Morphing Effekt
interface MorphingFeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  details: string[];
  delay: number;
  accentColor: string;
}

const MorphingFeatureCard: React.FC<MorphingFeatureCardProps> = ({ 
  icon: Icon, title, description, details, delay, accentColor 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    // Check if the device supports touch events
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    setIsTouchDevice(isTouch);
  }, []);

  const handleClick = () => {
    // Only toggle on touch devices, as hover handles desktop
    if (isTouchDevice) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateY: -15 }}
      whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, delay, ease: [0.68, -0.55, 0.265, 1.55] }}
      className="group relative overflow-hidden"
      onMouseEnter={() => !isTouchDevice && setIsExpanded(true)}
      onMouseLeave={() => !isTouchDevice && setIsExpanded(false)}
      onClick={handleClick}
    >        <div className="relative bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60 h-full min-h-[320px] sm:min-h-[350px] md:min-h-[380px] flex flex-col">
        {/* Accent Border */}
        <div 
          className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl sm:rounded-2xl"
          style={{ background: `linear-gradient(135deg, ${accentColor}20, transparent)` }}
        />
        
        <div className="relative p-3 sm:p-4 md:p-6 lg:p-8 h-full flex flex-col overflow-hidden">
          {/* Icon mit spezieller Animation */}
          <motion.div 
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-lg sm:rounded-xl md:rounded-2xl flex items-center justify-center mb-3 sm:mb-4 md:mb-6 relative overflow-hidden flex-shrink-0"
            style={{ backgroundColor: `${accentColor}15` }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon 
              className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 transition-colors duration-300" 
              style={{ color: accentColor }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: ['-100%', '200%'] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            />
          </motion.div>

          <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 md:mb-4 text-foreground group-hover:text-opacity-90 transition-colors flex-shrink-0">
            {title}
          </h3>
          
          <p className="text-sm sm:text-base text-muted-foreground mb-3 sm:mb-4 md:mb-6 leading-relaxed flex-shrink-0">
            {description}
          </p>          {/* Expandierbare Details */}
          <div className="flex-1 min-h-0">
            <motion.div
              initial={false}
              animate={{ 
                height: isExpanded ? 'auto' : 0, 
                opacity: isExpanded ? 1 : 0 
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="space-y-1.5 sm:space-y-2 md:space-y-3 pt-2 border-t border-border/20">
                {details.map((detail, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: isExpanded ? 1 : 0, x: isExpanded ? 0 : -20 }}
                    transition={{ delay: i * 0.1 }}
                    className="flex items-start space-x-2 sm:space-x-3"
                  >
                    <div 
                      className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: accentColor }}
                    />
                    <span className="text-xs sm:text-sm text-muted-foreground leading-relaxed">{detail}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>          {/* Hover Indicator */}
          <motion.div 
            className="pt-2 sm:pt-3 md:pt-4 text-xs font-medium opacity-0 group-hover:opacity-60 transition-opacity flex-shrink-0"
            style={{ color: accentColor }}
          >
            <span className="hidden sm:inline">Mehr Details anzeigen</span>
            <span className="sm:hidden">Details anzeigen</span>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

const LandingPage = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/login');
  };

  const uniqueFeatures = [
    {
      icon: Compass,
      title: 'Strategische Navigation',
      description: 'Verwandeln Sie Ihre Geschäftsziele in eine präzise Roadmap für lokale Partnerschaften und Marktexpansion',
      details: [
        'KI-gestützte Strategieentwicklung basierend auf Ihren spezifischen Zielen',
        'Automatische Generierung von Strategie-Papieren für verschiedene Szenarien',
        'Dynamische Anpassung an Marktveränderungen und neue Opportunities'
      ],
      delay: 0.1,
      accentColor: '#3b82f6'
    },
    {
      icon: MapPin,
      title: 'Lokale Intelligenz',
      description: 'Entdecken Sie versteckte Potenziale in Ihrer direkten Nachbarschaft durch unsere einzigartige Kartenansicht',
      details: [
        'Geografische Visualisierung aller relevanten Unternehmen in Ihrer Region',
        'Echtzeit-Updates zu neuen Geschäftsmöglichkeiten in Ihrem Umkreis',
        'Standortbasierte Matching-Algorithmen für optimale Partnerschaften'
      ],
      delay: 0.2,
      accentColor: '#10b981'
    },
    {
      icon: Brain,
      title: 'Intelligente Analyse',
      description: 'Lassen Sie unsere KI für jedes ausgewählte Unternehmen maßgeschneiderte Kooperationsmöglichkeiten identifizieren',
      details: [
        'Deep-Learning-Analyse von Geschäftsmodellen und Marktpositionierung',
        'Automatische Erkennung von Synergiepotenzialen und Win-Win-Situationen',
        'Priorisierung nach Erfolgswahrscheinlichkeit und strategischem Wert'
      ],
      delay: 0.3,
      accentColor: '#8b5cf6'
    },
    {
      icon: Network,
      title: 'Strategische Vernetzung',
      description: 'Bauen Sie systematisch Ihr lokales Unternehmensnetzwerk auf und erschließen Sie neue Geschäftsfelder',
      details: [
        'Strukturierte Kontaktaufnahme mit vorbereiteten Strategieanalysen',
        'Tracking und Management Ihrer Partnerschaftsinitiativen',
        'Langfristige Beziehungspflege durch kontinuierliche Opportunity-Updates'
      ],
      delay: 0.4,
      accentColor: '#f59e0b'
    }
  ];
  return (
    <div className="min-h-screen relative overflow-hidden">
      <FloatingBackground />
        {/* Revolutionary Hero Section */}
      <section className="relative py-6 sm:py-8 md:py-10 lg:py-16 xl:py-20 overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
            {/* Links: Content */}
            <motion.div 
              className="space-y-6 sm:space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div 
                className="inline-flex items-center space-x-2 sm:space-x-3 px-3 sm:px-5 py-2 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 border border-primary/30 backdrop-blur-sm"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                <div className="relative">
                  <Zap className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                  <motion.div
                    className="absolute inset-0 bg-primary/20 rounded-full"
                    animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </div>
                <span className="text-xs sm:text-sm font-semibold text-primary">
                  <span className="hidden sm:inline">Die neue Dimension strategischer Geschäftsentwicklung</span>
                  <span className="sm:hidden">Strategische Geschäftsentwicklung</span>
                </span>
              </motion.div>

              <motion.h1 
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                Lokale <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">Geschäftspotenziale</span> systematisch <span className="text-primary">erschließen</span>
              </motion.h1>

              <motion.p 
                className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              >
                OpuMap revolutioniert die Art, wie Unternehmen strategische Partnerschaften entwickeln. Definieren Sie Ihre Ziele, visualisieren Sie Ihr lokales Geschäftsumfeld und lassen Sie KI maßgeschneiderte Kooperationsmöglichkeiten für Sie identifizieren.
              </motion.p>

              <motion.div 
                className="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8, duration: 0.8 }}
              >
                <button 
                  onClick={handleGetStarted}
                  className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-primary text-primary-foreground rounded-xl font-semibold overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary/25 hover:scale-105 text-sm sm:text-base"
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Strategie entwickeln
                    <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform" />
                  </span>
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary"
                    animate={{ x: ['-100%', '100%'] }}
                    transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
                    style={{ background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)' }}
                  />
                </button>
                
                <button className="px-6 sm:px-8 py-3 sm:py-4 border-2 border-border/50 bg-background/50 backdrop-blur-sm rounded-xl font-semibold hover:bg-accent hover:border-primary/30 transition-all duration-300 hover:scale-105 text-sm sm:text-base">
                  <span className="hidden sm:inline">Live Demo anzeigen</span>
                  <span className="sm:hidden">Live Demo</span>
                </button>
              </motion.div>

              <motion.div 
                className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 pt-4 sm:pt-6 text-xs sm:text-sm text-muted-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  <span>Kostenfrei starten</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span>Sofort einsatzbereit</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                  <span>DSGVO-konform</span>
                </div>
              </motion.div>
            </motion.div>            {/* Rechts: Interaktive Demo */}
            <motion.div
              initial={{ opacity: 0, x: 50, rotateY: 15 }}
              animate={{ opacity: 1, x: 0, rotateY: 0 }}
              transition={{ delay: 0.6, duration: 1 }}
              className="relative order-first lg:order-last"
            >
              <InteractiveMapDemo />
              
              {/* Floating Stats */}
              <motion.div
                className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                <div className="text-lg sm:text-2xl font-bold text-primary">94%</div>
                <div className="text-xs text-muted-foreground">Erfolgsrate</div>
              </motion.div>

            </motion.div>
          </div>
        </div>
      </section>

      {/* Unique Features Section */}
      <section id="features" className="py-8 sm:py-12 md:py-16 lg:py-20 xl:py-32 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto mb-12 sm:mb-16 lg:mb-20">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 mb-4 sm:mb-6"
            >
              <Eye className="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3" />
              <span className="font-semibold text-primary text-sm sm:text-base">
                Einzigartige Technologie
              </span>
            </motion.div>
            
            <motion.h2 
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 sm:mb-8 leading-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Warum OpuMap anders ist als <br className="hidden sm:block" />
              <span className="text-primary">alles andere auf dem Markt</span>
            </motion.h2>
            
            <motion.p 
              className="text-base sm:text-lg text-muted-foreground leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Während andere Tools oberflächliche Kontaktlisten bieten, schafft OpuMap echte strategische Intelligenz. 
              Unsere KI versteht Ihr Geschäftsmodell und findet nicht nur Kontakte, sondern echte Geschäftschancen.
            </motion.p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto">
            {uniqueFeatures.map((feature, index) => (
              <MorphingFeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                details={feature.details}
                delay={feature.delay}
                accentColor={feature.accentColor}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Process Visualization */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-32 relative">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
            <motion.h2 
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              Der OpuMap-Prozess: <span className="text-primary">Von der Strategie zur Partnerschaft</span>
            </motion.h2>
          </div>
          
          <div className="relative max-w-6xl mx-auto">
            {/* Verbindungslinie */}
            <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 transform -translate-y-1/2 hidden lg:block"></div>
            
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
              {[
                {
                  step: '01',
                  icon: <Target className="w-6 h-6 sm:w-8 sm:h-8" />,
                  title: 'Strategie definieren',
                  description: 'Beschreiben Sie Ihre Ziele - ob neue Partnerschaften, Salesexpansion oder regionale Kooperationen'
                },
                {
                  step: '02', 
                  icon: <MapPin className="w-6 h-6 sm:w-8 sm:h-8" />,
                  title: 'Umgebung scannen',
                  description: 'Unsere KI analysiert Ihr lokales Geschäftsumfeld und identifiziert relevante Unternehmen'
                },
                {
                  step: '03',
                  icon: <Brain className="w-6 h-6 sm:w-8 sm:h-8" />,
                  title: 'Potenziale analysieren',
                  description: 'Für jedes Unternehmen werden spezifische Kooperationsmöglichkeiten und Synergien bewertet'
                },
                {
                  step: '04',
                  icon: <Handshake className="w-6 h-6 sm:w-8 sm:h-8" />,
                  title: 'Partnerschaften initiieren',
                  description: 'Mit maßgeschneiderten Strategieanalysen gehen Sie professionell in den Kontakt'
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="relative text-center group"
                >
                  {/* Step Circle */}
                  <div className="relative mx-auto w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center mb-4 sm:mb-6 shadow-lg group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-300 transform group-hover:scale-110">
                    <div className="text-white font-bold text-base sm:text-lg">{item.step}</div>
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-primary/30"
                      animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                      transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}
                    />
                  </div>
                  
                  {/* Icon */}
                  <div className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-primary">
                    {item.icon}
                  </div>
                  
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">{item.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">{item.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-32 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10"></div>
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl p-8 sm:p-12 md:p-16 rounded-2xl sm:rounded-3xl border border-border/30 shadow-2xl"
          >
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-6 sm:mb-8 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center"
            >
              <Route className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
            </motion.div>
            
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
              Revolutionieren Sie Ihre <span className="text-primary">Geschäftsentwicklung</span>
            </h2>
            
            <p className="text-base sm:text-lg lg:text-xl text-muted-foreground mb-8 sm:mb-10 max-w-2xl mx-auto leading-relaxed">
              Schließen Sie sich den Unternehmen an, die bereits mit OpuMap ihre lokalen Geschäftspotenziale systematisch erschließen und nachhaltige Partnerschaften aufbauen.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
              <button
                onClick={handleGetStarted}
                className="group relative px-8 sm:px-10 py-4 sm:py-5 bg-primary text-primary-foreground rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-primary/30 hover:scale-105 w-full sm:w-auto"
              >
                <span className="relative z-10 flex items-center justify-center gap-2 sm:gap-3">
                  <span className="hidden sm:inline">Jetzt starten - kostenfrei</span>
                  <span className="sm:hidden">Jetzt starten</span>
                  <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform" />
                </span>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary opacity-0 group-hover:opacity-100"
                  transition={{ duration: 0.3 }}
                />
              </button>
              
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-1">Oder erst mal</div>
                <button className="text-primary font-semibold hover:underline text-sm sm:text-base">
                  Live Demo ansehen →
                </button>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-8 mt-8 sm:mt-10 text-xs sm:text-sm text-muted-foreground">
              <div className="flex items-center justify-center space-x-2">
                <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>Keine Vertragsbindung</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>Sofort einsatzbereit</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>Premium Support</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
