'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Zap } from 'lucide-react';
import { AnimatedButton } from '../shared';
import { HERO_BADGES } from '../../utils';

interface HeroContentProps {
  onGetStarted: () => void;
}

export const HeroContent: React.FC<HeroContentProps> = ({ onGetStarted }) => {
  return (
    <motion.div 
      className="space-y-6 sm:space-y-8"
      initial={{ opacity: 0, x: -50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8 }}
    >
      {/* Badge */}
      <motion.div 
        className="inline-flex items-center space-x-2 sm:space-x-3 px-3 sm:px-5 py-2 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 border border-primary/30 backdrop-blur-sm"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <div className="relative">
          <Zap className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
          <motion.div
            className="absolute inset-0 bg-primary/20 rounded-full"
            animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
        <span className="text-xs sm:text-sm font-semibold text-primary">
          <span className="hidden sm:inline">Die neue Dimension strategischer Geschäftsentwicklung</span>
          <span className="sm:hidden">Strategische Geschäftsentwicklung</span>
        </span>
      </motion.div>

      {/* Main Heading */}
      <motion.h1 
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.8 }}
      >
        Lokale{' '}
        <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Geschäftspotenziale
        </span>{' '}
        systematisch{' '}
        <span className="text-primary">erschließen</span>
      </motion.h1>

      {/* Description */}
      <motion.p 
        className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-2xl"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.8 }}
      >
        OpuMap revolutioniert die Art, wie Unternehmen strategische Partnerschaften entwickeln. 
        Definieren Sie Ihre Ziele, visualisieren Sie Ihr lokales Geschäftsumfeld und lassen Sie 
        KI maßgeschneiderte Kooperationsmöglichkeiten für Sie identifizieren.
      </motion.p>

      {/* CTA Buttons */}
      <motion.div 
        className="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.8 }}
      >
        <AnimatedButton
          onClick={onGetStarted}
          variant="primary"
          size="medium"
          showArrow
          className="w-full sm:w-auto"
        >
          Strategie entwickeln
        </AnimatedButton>
        
        <AnimatedButton
          onClick={() => {}}
          variant="secondary"
          size="medium"
          className="w-full sm:w-auto"
        >
          <span className="hidden sm:inline">Live Demo anzeigen</span>
          <span className="sm:hidden">Live Demo</span>
        </AnimatedButton>
      </motion.div>

      {/* Feature Badges */}
      <motion.div 
        className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 pt-4 sm:pt-6 text-xs sm:text-sm text-muted-foreground"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        {HERO_BADGES.map((badge, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full bg-${badge.color}-500`}></div>
            <span>{badge.text}</span>
          </div>
        ))}
      </motion.div>
    </motion.div>
  );
};