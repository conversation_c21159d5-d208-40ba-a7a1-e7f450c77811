'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LabStatusIndicatorProps {
  status: {
    currentStep: string;
    progress: number;
    steps: string[];
  };
  variant?: 'dna' | 'molecular' | 'progress';
}

const LabStatusIndicator: React.FC<LabStatusIndicatorProps> = ({ 
  status, 
  variant = 'dna' 
}) => {

  if (variant === 'dna') {
    return (
      <div className="flex flex-col items-center space-y-4 p-4">
        {/* DNA Helix Style Progress */}
        <div className="relative w-24 h-32">
          <svg 
            viewBox="0 0 100 120" 
            className="w-full h-full"
            role="img"
            aria-label={`DNA helix visualization showing ${Math.round(status.progress)}% progress`}
          >
            <defs>
              <linearGradient id="dna-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#4A90E2" />
                <stop offset="50%" stopColor="#8b5cf6" />
                <stop offset="100%" stopColor="#10b981" />
              </linearGradient>
            </defs>
            
            {/* DNA Strands */}
            <motion.path
              d="M 30 10 Q 50 30 30 50 Q 10 70 30 90 Q 50 110 30 130"
              stroke="url(#dna-gradient)"
              strokeWidth="3"
              fill="none"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: status.progress / 100 }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            />
            <motion.path
              d="M 70 10 Q 50 30 70 50 Q 90 70 70 90 Q 50 110 70 130"
              stroke="url(#dna-gradient)"
              strokeWidth="3"
              fill="none"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: status.progress / 100 }}
              transition={{ duration: 0.8, ease: "easeInOut", delay: 0.2 }}
            />
            
            {/* Connection Lines */}
            {Array.from({ length: 6 }, (_, i) => (
              <motion.line
                key={i}
                x1="30"
                y1={20 + i * 20}
                x2="70"
                y2={20 + i * 20}
                stroke="#4A90E2"
                strokeWidth="2"
                opacity="0.6"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: status.progress > (i * 20) ? 1 : 0 }}
                transition={{ duration: 0.3, delay: i * 0.1 }}
              />
            ))}
            
            {/* Animated particles */}
            <motion.circle
              r="3"
              fill="#10b981"
              opacity="0.8"
              animate={{
                offsetDistance: `${status.progress}%`,
              }}
              transition={{ duration: 1 }}
            >
              <animateMotion
                dur="2s"
                repeatCount="indefinite"
                path="M 30 10 Q 50 30 30 50 Q 10 70 30 90 Q 50 110 30 130"
              />
            </motion.circle>
          </svg>
        </div>
        
        <div className="text-center">
          <div className="text-sm font-medium text-[#4A90E2] mb-1">
            {status.currentStep}
          </div>
          <div className="text-xs text-muted-foreground">
            {Math.round(status.progress)}% abgeschlossen
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'molecular') {
    return (
      <div className="flex flex-col items-center space-y-4 p-4">
        {/* Molecular Structure Progress */}
        <div className="relative w-32 h-32">
          <svg 
            viewBox="0 0 120 120" 
            className="w-full h-full"
            role="img"
            aria-label={`Molecular structure visualization showing ${Math.ceil((status.progress / 100) * status.steps.length)} of ${status.steps.length} analysis steps completed`}
          >
            <defs>
              <radialGradient id="molecule-gradient">
                <stop offset="0%" stopColor="#4A90E2" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#4A90E2" stopOpacity="0.2" />
              </radialGradient>
            </defs>
            
            {/* Central molecule */}
            <motion.circle
              cx="60" cy="60" r="8"
              fill="#4A90E2"
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [0.8, 1, 0.8]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            
            {/* Orbital electrons */}
            {Array.from({ length: status.steps.length }, (_, i) => {
              const angle = (i * 360) / status.steps.length;
              const radius = 35;
              const x = 60 + radius * Math.cos((angle * Math.PI) / 180);
              const y = 60 + radius * Math.sin((angle * Math.PI) / 180);
              const isActive = i < (status.progress / 100) * status.steps.length;
              
              return (
                <motion.g key={i}>
                  {/* Orbital path */}
                  <motion.circle
                    cx="60" cy="60" r={radius}
                    fill="none"
                    stroke="#4A90E2"
                    strokeWidth="1"
                    opacity="0.3"
                    strokeDasharray="2,2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 10 + i * 2, repeat: Infinity, ease: "linear" }}
                  />
                  
                  {/* Electron */}
                  <motion.circle
                    cx={x} cy={y} r="4"
                    fill={isActive ? "#10b981" : "#4A90E2"}
                    opacity={isActive ? 1 : 0.5}
                    animate={isActive ? {
                      scale: [1, 1.3, 1],
                      opacity: [0.8, 1, 0.8]
                    } : {}}
                    transition={{ duration: 1.5, repeat: Infinity, delay: i * 0.2 }}
                  />
                  
                  {/* Connection line */}
                  <motion.line
                    x1="60" y1="60" x2={x} y2={y}
                    stroke={isActive ? "#10b981" : "#4A90E2"}
                    strokeWidth="1"
                    opacity={isActive ? 0.6 : 0.3}
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: isActive ? 1 : 0.3 }}
                    transition={{ duration: 0.5 }}
                  />
                </motion.g>
              );
            })}
          </svg>
        </div>
        
        <div className="text-center">
          <div className="text-sm font-medium text-[#4A90E2] mb-1">
            {status.currentStep}
          </div>
          <div className="text-xs text-muted-foreground">
            Analyseschritt {Math.ceil((status.progress / 100) * status.steps.length)} von {status.steps.length}
          </div>
        </div>
      </div>
    );
  }

  // Default progress variant
  return (
    <div className="flex flex-col items-center space-y-4 p-4">
      {/* Reagenzglas-Style Progress */}
      <div className="relative">
        <div className="w-8 h-24 bg-gradient-to-b from-transparent to-[rgba(26,32,44,0.4)] rounded-b-full border-2 border-[rgba(74,144,226,0.3)] relative overflow-hidden">
          {/* Liquid fill */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-[#10b981] via-[#4A90E2] to-[#8b5cf6] rounded-b-full"
            initial={{ height: 0 }}
            animate={{ height: `${status.progress}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
          
          {/* Bubbles animation */}
          {Array.from({ length: 3 }, (_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/60 rounded-full"
              style={{
                left: `${20 + i * 20}%`,
                bottom: `${10 + (status.progress / 2)}%`
              }}
              animate={{
                y: [-10, -30, -10],
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
        
        {/* Reagenzglas opening */}
        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-3 bg-[rgba(26,32,44,0.6)] border border-[rgba(74,144,226,0.3)] rounded-t-md" />
      </div>
      
      <div className="text-center">
        <div className="text-sm font-medium text-[#4A90E2] mb-1">
          {status.currentStep}
        </div>
        <div className="text-xs text-muted-foreground">
          Labor-Analyse läuft...
        </div>
      </div>
    </div>
  );
};

export default LabStatusIndicator;