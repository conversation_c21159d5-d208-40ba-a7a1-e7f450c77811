{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;IACf,MAAM,IAAI,MACR,0EACE;AAEN", "ignoreList": [0], "debugId": null}}]}