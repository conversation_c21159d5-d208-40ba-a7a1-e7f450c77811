'use client';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { motion } from 'framer-motion';

import CompanyInfoEditor from './components/CompanyInfoEditor';
import SelectedCompaniesList from './components/SelectedCompaniesList';
import { FooterScan } from './components/FooterScan';
import { RecognizedChancesSection } from './components/RecognizedChancesSection';
import { ScanResultModal } from './components/ScanResultModal';
import { StrategySelection } from './components/StrategySelection';
import { useCompanyInfo } from './hooks/useCompanyInfo';
import { useSelectedCompanies } from './hooks/useSelectedCompanies';
import { useStrategies } from './hooks/useStrategies';
import { useScanProcess } from './hooks/useScanProcess';
import { Notification } from '@/components/ui/notification';
import Loader from '@/components/ui/Loader';
import ScannerStatusIndicator from './components/ui/ScannerStatusIndicator';
import { SelectedCompany, StrategyOption } from './types';
import FloatingBackground from '../Landingpage/components/shared/FloatingBackground';

// Define standard strategy options outside the component
const standardStrategyOptions: StrategyOption[] = [
    { value: 'kooperation', label: 'Kooperationsanfrage', info: 'Info zu Kooperationsanfrage' },
    { value: 'marketing', label: 'Marketingideen', info: 'Info zu Marketingideen' },
    { value: 'sales', label: 'Sales Maßnahmen', info: 'Info zu Sales Maßnahmen' },
    { value: 'material', label: 'Materialzulieferungen', info: 'Info zu Materialzulieferungen' },
    { value: 'fortbildung', label: 'Fortbildungen', info: 'Info zu Fortbildungen' }
  ];

const OpuscannerPage: React.FC = () => {
  const {} = useAuth();
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true when component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);

  // --- State Hooks ---
  const [notification, setNotification] = useState<{ type: 'success' | 'error', message: string, details?: string } | null>(null);
  const [notificationVisible, setNotificationVisible] = useState<boolean>(false);
  const [showAllCompanies, setShowAllCompanies] = useState<boolean>(false);

  // --- Custom Hooks ---
  const { companyInfo, isSaving, saveError, saveSuccessMessage, handleMarkdownChange, handleSaveCompanyInfo, setSaveError, setSaveSuccessMessage } = useCompanyInfo({
    onSaveSuccess: () => showNotification('success', 'Unternehmensinformationen erfolgreich gespeichert.'),
    onSaveError: (msg) => showNotification('error', msg)
  });

  const {
    opulabStrategies,
    isLoadingOpulabStrategies,
    opulabLoadError,
    selectedOption,
    displayedScanResults,
    isLoadingDisplayedScanResults,
    handleRadioChange: originalHandleRadioChange,
  } = useStrategies();

  // Wrap handleRadioChange to include showAllCompanies filter
  const handleRadioChange = useCallback((value: string) => {
    originalHandleRadioChange(value, showAllCompanies);
  }, [originalHandleRadioChange, showAllCompanies]);

  // Callback zum Aktualisieren der Scanergebnisse nach dem Entfernen eines Unternehmens
  const updateScanResultsAfterRemoval = useCallback(() => {
    // Wenn eine Strategie ausgewählt ist, aktualisiere die Scanergebnisse
    if (selectedOption.startsWith('opulab_')) {
      handleRadioChange(selectedOption);
    }
  }, [selectedOption, handleRadioChange]);

  const { companies, isLoading: isLoadingCompanies, error: companiesError, handleRemoveCompany, setError: setCompaniesError } = useSelectedCompanies({
    onCompanyRemoved: updateScanResultsAfterRemoval
  });

  // Prepare company data for useScanProcess with full objects including place_id and company_id
  const companyIds = useMemo(() => companies.map(c => ({
    id: c.id,
    place_id: c.place_id,
    company_id: c.company_id
  })), [companies]);

  // Callback for useScanProcess to reload displayed results
  const reloadDisplayedResultsCallback = useCallback(async (strategyId: number) => {
    // Find the opulab strategy value string
    const strategyValue = `opulab_${strategyId}`;
    // Call the handleRadioChange from useStrategies hook to reload
    // This implicitly re-fetches and updates displayedScanResults
    handleRadioChange(strategyValue);
    console.log(`Reloaded displayed results for strategy ${strategyId} after scan completion.`);
  }, [handleRadioChange]);

  const {
    isScanning,
    selectedScanResult,
    isModalOpen,
    scanStatus,
    processedCount,
    totalCompanies,
    startScan,
    handleOpenModal,
    handleCloseModal,
    clearPollingState
  } = useScanProcess({
    selectedOption: selectedOption,
    companyIds: companyIds,
    onScanStartError: (msg) => showNotification('error', 'Scan konnte nicht gestartet werden', msg),
    onPollUpdate: () => { /* Optional: Zeige Fortschritt als Notification oder ignoriere hier */ },
    onPollComplete: () => showNotification('success', 'Scan abgeschlossen'),
    onPollError: (msg) => showNotification('error', 'Scan fehlgeschlagen', msg),
    onResultsLoadError: (msg) => showNotification('error', 'Ergebnisse konnten nicht geladen werden', msg),
    reloadDisplayedResults: reloadDisplayedResultsCallback, // Pass the callback
  });

  const handleToggleChange = useCallback((checked: boolean) => {
    setShowAllCompanies(checked);
    if (selectedOption.startsWith('opulab_')) {
      originalHandleRadioChange(selectedOption, checked);
    }
  }, [selectedOption, originalHandleRadioChange]);

  // --- Derived State ---
  const isOpulabStrategySelected = selectedOption.startsWith('opulab_');

  // --- Effects ---
  // --- Notification Logic ---
  const showNotification = useCallback((type: 'success' | 'error', message: string, details?: string) => {
    setNotification({ type, message, details });
    setNotificationVisible(true);
  }, []);

  useEffect(() => {
    if (!notificationVisible) return;
    const timer = setTimeout(() => {
      setNotificationVisible(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, [notificationVisible]);

  // Clear notification data after fade out
  useEffect(() => {
    if (notificationVisible) return;
    const timer = setTimeout(() => {
      setNotification(null);
      if (scanStatus === 'error') {
          clearPollingState();
      }
      if(saveError) setSaveError(null);
      if(saveSuccessMessage) setSaveSuccessMessage(null);
      if(companiesError) setCompaniesError(null);
    }, 300); // Match CSS transition duration
    return () => clearTimeout(timer);
  }, [notificationVisible, scanStatus, clearPollingState, saveError, saveSuccessMessage, companiesError, setSaveError, setSaveSuccessMessage, setCompaniesError]);

  if (!isClient) {
    return (
      <div className="flex justify-center items-center h-screen bg-background">
        <Loader />
      </div>
    );
  }

  // --- Render Logic ---
  return (
    <>
      <FloatingBackground />
      {/* Notification Display */}
      {notification && (
         <Notification
           type={notification.type}
           message={notification.message}
           details={notification.details}
           isVisible={notificationVisible}
           onDismiss={() => setNotificationVisible(false)}
         />
       )}

      <div className="relative min-h-screen p-4 sm:p-6 lg:p-8 text-white">
        <main className="grid grid-cols-1 lg:grid-cols-5 gap-6 max-w-7xl mx-auto mt-16">
          {/* Left Column: Setup */}
          <motion.div 
            className="lg:col-span-2 space-y-6"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className={`bg-card/60 backdrop-blur-md border border-border/30 rounded-2xl p-5 shadow-lg transition-opacity duration-300 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
              <CompanyInfoEditor
                companyInfo={companyInfo}
                isSaving={isSaving}
                onChange={handleMarkdownChange}
                onSave={handleSaveCompanyInfo}
                useMarkdown={true}
              />
            </div>
            <div className={`bg-card/60 backdrop-blur-md border border-border/30 rounded-2xl p-5 shadow-lg transition-opacity duration-300 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
               <StrategySelection
                   selectedOption={selectedOption}
                   onValueChange={handleRadioChange}
                   opulabStrategies={opulabStrategies}
                   standardOptions={standardStrategyOptions}
                   isLoadingStrategies={isLoadingOpulabStrategies}
                   loadingError={opulabLoadError}
                   isSaving={isSaving || isScanning}
                />
            </div>
          </motion.div>

          {/* Right Column: Action & Results */}
          <motion.div 
            className="lg:col-span-3 space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            <div className={`bg-card/60 backdrop-blur-md border border-border/30 rounded-2xl p-5 shadow-lg flex flex-col h-full transition-opacity duration-300 ${isScanning ? 'opacity-50 pointer-events-none' : ''}`}>
              <h2 className="text-xl font-bold mb-4 text-white border-b border-border/50 pb-3">
                Ausgewählte Unternehmen
              </h2>
              <div className="flex-grow overflow-y-auto custom-scrollbar pr-2 -mr-2">
                {isLoadingCompanies && <div className="flex justify-center items-center h-full"><Loader /></div>}
                {companiesError && <p className="text-red-400 p-4">{companiesError}</p>}
                {!isLoadingCompanies && !companiesError && (
                  <SelectedCompaniesList
                    companies={companies}
                    isLoading={false}
                    onRemoveCompany={(company: SelectedCompany) => {
                        if (isScanning) return;
                        handleRemoveCompany(company)
                            .then(() => showNotification('success', 'Unternehmen entfernt.'))
                            .catch(() => showNotification('error', 'Fehler beim Entfernen'));
                    }}
                  />
                )}
              </div>
            </div>
          </motion.div>
        </main>

        {/* Footer & Scan Area */}
        <motion.footer 
          className="sticky bottom-0 mt-8 py-4 z-20"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.4 }}
        >
            <div className="max-w-4xl mx-auto bg-card/70 backdrop-blur-xl p-4 rounded-2xl border border-border/30 shadow-2xl flex flex-col items-center">
                <FooterScan
                    isScanning={isScanning || isSaving}
                    onStartScan={startScan}
                    hasSelectedStrategy={isOpulabStrategySelected}
                    hasSelectedCompanies={companies.length > 0}
                    scanProgressStatus={scanStatus}
                />
                {isScanning && (
                    <div className="w-full mt-4">
                        <ScannerStatusIndicator
                            status={scanStatus}
                            processedCount={processedCount}
                            totalCompanies={totalCompanies}
                        />
                    </div>
                )}
            </div>
        </motion.footer>

        {/* Recognized Chances Section */}
        {isClient && (displayedScanResults.length > 0 || isLoadingDisplayedScanResults) && (
          <motion.div 
            className="mt-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
             <RecognizedChancesSection
                scanResults={displayedScanResults}
                isLoading={isLoadingDisplayedScanResults}
                onResultClick={handleOpenModal}
                showAllCompanies={showAllCompanies}
                onToggleChange={handleToggleChange}
             />
          </motion.div>
        )}
      </div>

      {/* Scan Result Modal */}
      <ScanResultModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        result={selectedScanResult}
      />
    </>
  );
};

const OpuscannerPageWithProtection = () => {
  return (
    <ProtectedRoute>
      <OpuscannerPage />
    </ProtectedRoute>
  );
};

export default OpuscannerPageWithProtection;
