import { Target, MapPin, Brain, Handshake, Compass, Network } from 'lucide-react';

export const UNIQUE_FEATURES = [
  {
    icon: Compass,
    title: 'Strategische Navigation',
    description: 'Verwandeln Sie Ihre Geschäftsziele in eine präzise Roadmap für lokale Partnerschaften und Marktexpansion',
    details: [
      'KI-gestützte Strategieentwicklung basierend auf Ihren spezifischen Zielen',
      'Automatische Generierung von Strategie-Papieren für verschiedene Szenarien',
      'Dynamische Anpassung an Marktveränderungen und neue Opportunities'
    ],
    delay: 0.1,
    accentColor: '#3b82f6'
  },
  {
    icon: MapPin,
    title: 'Lokale Intelligenz',
    description: 'Entdecken Sie versteckte Potenziale in Ihrer direkten Nachbarschaft durch unsere einzigartige Kartenansicht',
    details: [
      'Geografische Visualisierung aller relevanten Unternehmen in Ihrer Region',
      'Echtzeit-Updates zu neuen Geschäftsmöglichkeiten in Ihrem Umkreis',
      'Standortbasierte Matching-Algorithmen für optimale Partnerschaften'
    ],
    delay: 0.2,
    accentColor: '#10b981'
  },
  {
    icon: Brain,
    title: 'Intelligente Analyse',
    description: 'Lassen Sie unsere KI für jedes ausgewählte Unternehmen maßgeschneiderte Kooperationsmöglichkeiten identifizieren',
    details: [
      'Deep-Learning-Analyse von Geschäftsmodellen und Marktpositionierung',
      'Automatische Erkennung von Synergiepotenzialen und Win-Win-Situationen',
      'Priorisierung nach Erfolgswahrscheinlichkeit und strategischem Wert'
    ],
    delay: 0.3,
    accentColor: '#8b5cf6'
  },
  {
    icon: Network,
    title: 'Strategische Vernetzung',
    description: 'Bauen Sie systematisch Ihr lokales Unternehmensnetzwerk auf und erschließen Sie neue Geschäftsfelder',
    details: [
      'Strukturierte Kontaktaufnahme mit vorbereiteten Strategieanalysen',
      'Tracking und Management Ihrer Partnerschaftsinitiativen',
      'Langfristige Beziehungspflege durch kontinuierliche Opportunity-Updates'
    ],
    delay: 0.4,
    accentColor: '#f59e0b'
  }
];

export const PROCESS_STEPS = [
  {
    step: '01',
    icon: Target,
    title: 'Strategie definieren',
    description: 'Beschreiben Sie Ihre Ziele - ob neue Partnerschaften, Salesexpansion oder regionale Kooperationen'
  },
  {
    step: '02', 
    icon: MapPin,
    title: 'Umgebung scannen',
    description: 'Unsere KI analysiert Ihr lokales Geschäftsumfeld und identifiziert relevante Unternehmen'
  },
  {
    step: '03',
    icon: Brain,
    title: 'Potenziale analysieren',
    description: 'Für jedes Unternehmen werden spezifische Kooperationsmöglichkeiten und Synergien bewertet'
  },
  {
    step: '04',
    icon: Handshake,
    title: 'Partnerschaften initiieren',
    description: 'Mit maßgeschneiderten Strategieanalysen gehen Sie professionell in den Kontakt'
  }
];

export const HERO_BADGES = [
  { text: "Kostenfrei starten", color: "green" },
  { text: "Sofort einsatzbereit", color: "blue" },
  { text: "DSGVO-konform", color: "purple" }
];

export const CTA_FEATURES = [
  "Keine Vertragsbindung",
  "Sofort einsatzbereit", 
  "Premium Support"
];