'use client';

import React, { useState, useContext, FormEvent, useEffect } from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Mail, Lock, User, ArrowRight, Check, AlertCircle, Loader2 } from 'lucide-react';
import FloatingBackground from '../Landingpage/components/shared/FloatingBackground';
const Login: React.FC = () => {
  const authContext = useContext(AuthContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isRegister, setIsRegister] = useState<boolean>(false);
  const [name, setName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [formVariant, setFormVariant] = useState<'login' | 'register'>('login');

  // Toggle between login and register forms
  const _toggleForm = () => {
    setFormVariant(prev => prev === 'login' ? 'register' : 'login');
    setError(null);
    setMessage(null);
  };

  // Update isRegister when formVariant changes
  useEffect(() => {
    setIsRegister(formVariant === 'register');
  }, [formVariant]);

  // Check for message parameter in URL (e.g., from password reset)
  useEffect(() => {
    const messageParam = searchParams.get('message');
    if (messageParam === 'password-updated') {
      setMessage('Ihr Passwort wurde erfolgreich aktualisiert. Sie können sich jetzt mit Ihrem neuen Passwort anmelden.');

      // Clear any existing errors
      setError(null);

      // Focus the email field after a short delay to improve UX
      setTimeout(() => {
        const emailInput = document.getElementById('email');
        if (emailInput) {
          emailInput.focus();
        }
      }, 500);
    }
  }, [searchParams]);

  if (!authContext) {
    console.error("AuthContext not found. Ensure Login component is wrapped in AuthProvider.");
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Laden...</p>
        </div>
      </div>
    );
  }

  const { signIn, signUp, signInWithGoogle, isLoading: isAuthLoading } = authContext; // Add isLoading

  // Show loading indicator if auth is loading
  if (isAuthLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[var(--color-background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto"></div>
          <p className="mt-4 text-[var(--color-foreground)]">Authentifizierung wird geladen...</p>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setMessage(null);

    try {
      if (isRegister) {
        const { error } = await signUp(email, password);
        if (error) {
          setError(error.message || "Registrierung fehlgeschlagen");
        } else {
          setMessage("Registrierung erfolgreich. Bitte bestätigen Sie Ihre E-Mail-Adresse und melden Sie sich dann an.");
          setIsRegister(false);
          setName("");
          setEmail("");
          setPassword("");
        }
      } else {
        const { error } = await signIn(email, password);
        if (error) {
          setError(error.message || "Login fehlgeschlagen");
        } else {
          router.push('/');
        }
      }
    } catch (err) {
      console.error(`${isRegister ? 'Registration' : 'Login'} error:`, err);
      setError("Netzwerkfehler oder Server nicht erreichbar.");
    }
  };

  const handleGoogleSignIn = async () => {
    setError(null);
    setMessage(null);
    if (isAuthLoading) { // Prevent action if auth is still loading
        setError("Authentifizierung wird noch geladen. Bitte warten Sie einen Moment.");
        return;
    }
    if (signInWithGoogle) {
      const { error } = await signInWithGoogle();
      if (error) {
        setError(error.message || "Google Login fehlgeschlagen");
      } else {
        // Redirect will be handled by Supabase and the callback route
        // router.push('/'); // No need to push here
      }
    } else {
      setError("Google Login Funktion nicht verfügbar.");
    }
  };

  // Function to handle back to landing page with animation
  const handleBackToLanding = () => {
    if (isAnimating) return; // Prevent multiple clicks during animation

    console.log("Transitioning back to landing page");
    setIsAnimating(true);

    // Add class to prevent scrollbars during transition
    document.body.classList.add('page-transition-active');

    // Navigate after a very short delay
    setTimeout(() => {
      router.push('/');
      // Remove class after navigation
      setTimeout(() => {
        document.body.classList.remove('page-transition-active');
      }, 50);
    }, 100); // Short delay for smoother transition
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-background to-muted/20 overflow-hidden">
      <FloatingBackground />
      
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div 
          key={formVariant}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
          className="w-full max-w-md"
        >
          <motion.div 
            className="bg-card/80 backdrop-blur-lg rounded-2xl shadow-2xl overflow-hidden border border-border/30"
            initial={{ scale: 0.98, opacity: 0.9 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}
          >
            <div className="p-1 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10">
              <div className="bg-card/80 rounded-xl p-8">
                <div className="text-center mb-8">
                  <motion.h1 
                    className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80 mb-2"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    {formVariant === 'login' ? 'Willkommen zurück' : 'Konto erstellen'}
                  </motion.h1>
                  <motion.p 
                    className="text-muted-foreground"
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {formVariant === 'login' 
                      ? 'Melden Sie sich an, um fortzufahren' 
                      : 'Erstellen Sie Ihr Konto, um loszulegen'}
                  </motion.p>
                </div>

                <AnimatePresence mode="wait">
                  {message && (
                    <motion.div 
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mb-6 p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-600 dark:text-green-400 text-sm"
                    >
                      <div className="flex items-center">
                        <Check className="w-4 h-4 mr-2 flex-shrink-0" />
                        <span>{message}</span>
                      </div>
                    </motion.div>
                  )}
                  {error && (
                    <motion.div 
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-600 dark:text-red-400 text-sm"
                    >
                      <div className="flex items-start">
                        <AlertCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                        <span>{error}</span>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="mb-6 text-center">
                  <button
                    type="button"
                    onClick={_toggleForm}
                    className="text-sm font-medium text-primary hover:text-primary/80 transition-colors mb-4"
                    disabled={isAuthLoading}
                  >
                    {formVariant === 'login' 
                      ? 'Noch kein Konto? Jetzt registrieren' 
                      : 'Bereits registriert? Zum Login'}
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-5">
                  <AnimatePresence mode="wait">
                    {formVariant === 'register' && (
                      <motion.div
                        key="name-field"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 10 }}
                        transition={{ duration: 0.2 }}
                        className="relative"
                      >
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <input
                          id="name"
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          className="w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60"
                          placeholder="Vollständiger Name"
                          required
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="relative"
                  >
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60"
                      placeholder="E-Mail-Adresse"
                      required
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="relative"
                  >
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl bg-background/50 border border-border/50 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 transition-all duration-200 text-foreground placeholder-muted-foreground/60"
                      placeholder="Passwort"
                      minLength={formVariant === 'register' ? 6 : undefined}
                      required
                    />
                  </motion.div>

                  {formVariant === 'login' && (
                    <motion.div 
                      initial={{ opacity: 0, y: 5 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="flex items-center justify-end -mt-3"
                    >
                      <Link 
                        href="/forgot-password" 
                        className="text-sm font-medium text-primary hover:underline hover:text-primary/80 transition-colors"
                      >
                        Passwort vergessen?
                      </Link>
                    </motion.div>
                  )}

                  <motion.button
                    type="submit"
                    disabled={isAuthLoading}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full py-3 px-6 rounded-xl bg-gradient-to-r from-primary to-primary/80 text-white font-medium shadow-lg hover:shadow-primary/20 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 flex items-center justify-center space-x-2 ${
                      isAuthLoading ? 'opacity-80 cursor-not-allowed' : ''
                    }`}
                  >
                    {isAuthLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        <span>Bitte warten...</span>
                      </>
                    ) : (
                      <>
                        <span>{formVariant === 'login' ? 'Anmelden' : 'Konto erstellen'}</span>
                        <ArrowRight className="h-4 w-4" />
                      </>
                    )}
                  </motion.button>
                </form>

                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-border/30"></div>
                  </div>
                  <div className="relative flex justify-center">
                    <span className="px-3 bg-card text-muted-foreground text-sm">
                      Oder
                    </span>
                  </div>
                </div>

                <motion.button
                  type="button"
                  onClick={handleGoogleSignIn}
                  disabled={isAuthLoading}
                  className="w-full flex items-center justify-center py-3 px-6 rounded-xl border border-border bg-card hover:bg-accent/50 transition-colors duration-200 mb-6"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <svg 
                    className="w-5 h-5 mr-2" 
                    viewBox="0 0 24 24" 
                    xmlns="http://www.w3.org/2000/svg"
                    role="img"
                    aria-label="Google logo"
                    focusable="false"
                  >
                    <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                      <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
                      <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
                      <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
                      <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
                    </g>
                  </svg>
                  <span className="font-medium">Mit Google anmelden</span>
                </motion.button>

                <div className="text-center">
                  <button
                    type="button"
                    onClick={handleBackToLanding}
                    className="text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    disabled={isAuthLoading}
                  >
                    Zurück zur Startseite
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}

export default Login;
