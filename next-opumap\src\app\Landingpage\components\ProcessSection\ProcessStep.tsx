'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface ProcessStepProps {
  step: string;
  icon: LucideIcon;
  title: string;
  description: string;
  index: number;
}

export const ProcessStep: React.FC<ProcessStepProps> = ({
  step,
  icon: Icon,
  title,
  description,
  index
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.6, delay: index * 0.2 }}
      className="relative text-center group"
    >
      {/* Step Circle */}
      <div className="relative mx-auto w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center mb-4 sm:mb-6 shadow-lg group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-300 transform group-hover:scale-110">
        <div className="text-white font-bold text-base sm:text-lg">{step}</div>
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-primary/30"
          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
          transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}
        />
      </div>
      
      {/* Icon */}
      <div className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-primary">
        <Icon className="w-full h-full" />
      </div>
      
      {/* Content */}
      <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3">{title}</h3>
      <p className="text-muted-foreground text-sm leading-relaxed">{description}</p>
    </motion.div>
  );
};