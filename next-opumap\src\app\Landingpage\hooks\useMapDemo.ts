'use client';

import { useState, useEffect, useMemo } from 'react';

export interface Company {
  id: number;
  name: string;
  x: number;
  y: number;
  type: string;
  match: number;
}

export const useMapDemo = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [hoveredCompany, setHoveredCompany] = useState<number | null>(null);

  const companies: Company[] = useMemo(() => [
    { id: 1, name: "TechStart GmbH", x: 25, y: 35, type: "tech", match: 95 },
    { id: 2, name: "Regional Handel", x: 60, y: 55, type: "retail", match: 78 },
    { id: 3, name: "Innovation Hub", x: 40, y: 25, type: "innovation", match: 88 },
    { id: 4, name: "Local Services", x: 75, y: 70, type: "service", match: 65 },
    { id: 5, name: "Manufaktur Nord", x: 15, y: 65, type: "production", match: 82 }
  ], []);

  const steps = useMemo(() => [
    "Ihre Strategie definieren",
    "Lokale Unternehmen scannen", 
    "Potenziale analysieren",
    "Partnerschaften initiieren"
  ], []);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [steps.length]);

  return {
    activeStep,
    hoveredCompany,
    setHoveredCompany,
    companies,
    steps
  };
};