{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/AnimatedButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { ArrowRight } from 'lucide-react';\r\n\r\ninterface AnimatedButtonProps {\r\n  onClick: () => void;\r\n  children: React.ReactNode;\r\n  variant?: 'primary' | 'secondary';\r\n  size?: 'small' | 'medium' | 'large';\r\n  showArrow?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const AnimatedButton: React.FC<AnimatedButtonProps> = ({\r\n  onClick,\r\n  children,\r\n  variant = 'primary',\r\n  size = 'medium',\r\n  showArrow = false,\r\n  className = ''\r\n}) => {\r\n  const baseClasses = \"group relative overflow-hidden transition-all duration-300 font-semibold rounded-xl\";\r\n  \r\n  const variantClasses = {\r\n    primary: \"bg-primary text-primary-foreground hover:shadow-xl hover:shadow-primary/25 hover:scale-105\",\r\n    secondary: \"border-2 border-border/50 bg-background/50 backdrop-blur-sm hover:bg-accent hover:border-primary/30 hover:scale-105\"\r\n  };\r\n\r\n  const sizeClasses = {\r\n    small: \"px-4 py-2 text-sm\",\r\n    medium: \"px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base\",\r\n    large: \"px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg\"\r\n  };\r\n\r\n  return (\r\n    <button \r\n      onClick={onClick}\r\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\r\n    >\r\n      <span className=\"relative z-10 flex items-center justify-center gap-2\">\r\n        {children}\r\n        {showArrow && (\r\n          <ArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform\" />\r\n        )}\r\n      </span>\r\n      {variant === 'primary' && (\r\n        <motion.div\r\n          className=\"absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary\"\r\n          animate={{ x: ['-100%', '100%'] }}\r\n          transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}\r\n          style={{ background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)' }}\r\n        />\r\n      )}\r\n    </button>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAeO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,YAAY,KAAK,EACjB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;IACb;IAEA,MAAM,cAAc;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;;0BAExF,8OAAC;gBAAK,WAAU;;oBACb;oBACA,2BACC,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;YAGzB,YAAY,2BACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAS;qBAAO;gBAAC;gBAChC,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,aAAa;gBAAE;gBAC5D,OAAO;oBAAE,YAAY;gBAA0E;;;;;;;;;;;;AAKzG", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/SectionHeader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { LucideIcon } from 'lucide-react';\r\n\r\ninterface SectionHeaderProps {\r\n  badge?: {\r\n    icon: LucideIcon;\r\n    text: string;\r\n  };\r\n  title: React.ReactNode;\r\n  description?: string;\r\n  centered?: boolean;\r\n  maxWidth?: string;\r\n}\r\n\r\nexport const SectionHeader: React.FC<SectionHeaderProps> = ({\r\n  badge,\r\n  title,\r\n  description,\r\n  centered = true,\r\n  maxWidth = \"max-w-4xl\"\r\n}) => {\r\n  const containerClasses = centered ? \"text-center mx-auto\" : \"\";\r\n\r\n  return (\r\n    <div className={`${maxWidth} ${containerClasses} mb-12 sm:mb-16 lg:mb-20`}>\r\n      {badge && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          viewport={{ once: true }}\r\n          transition={{ duration: 0.6 }}\r\n          className=\"inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 mb-4 sm:mb-6\"\r\n        >\r\n          <badge.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3\" />\r\n          <span className=\"font-semibold text-primary text-sm sm:text-base\">\r\n            {badge.text}\r\n          </span>\r\n        </motion.div>\r\n      )}\r\n      \r\n      <motion.h2 \r\n        className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 leading-tight\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.6, delay: badge ? 0.1 : 0 }}\r\n      >\r\n        {title}\r\n      </motion.h2>\r\n      \r\n      {description && (\r\n        <motion.p \r\n          className=\"text-base sm:text-lg text-muted-foreground leading-relaxed\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          viewport={{ once: true }}\r\n          transition={{ duration: 0.6, delay: badge ? 0.2 : 0.1 }}\r\n        >\r\n          {description}\r\n        </motion.p>\r\n      )}\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAiBO,MAAM,gBAA8C,CAAC,EAC1D,KAAK,EACL,KAAK,EACL,WAAW,EACX,WAAW,IAAI,EACf,WAAW,WAAW,EACvB;IACC,MAAM,mBAAmB,WAAW,wBAAwB;IAE5D,qBACE,8OAAC;QAAI,WAAW,GAAG,SAAS,CAAC,EAAE,iBAAiB,wBAAwB,CAAC;;YACtE,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC,MAAM,IAAI;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBAAK,WAAU;kCACb,MAAM,IAAI;;;;;;;;;;;;0BAKjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ,MAAM;gBAAE;0BAEnD;;;;;;YAGF,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ,MAAM;gBAAI;0BAErD;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/FloatingBackground.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst FloatingBackground = () => {\r\n  return (\r\n    <StyledWrapper>\r\n      <svg id=\"background_svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 800\" fill=\"none\" preserveAspectRatio=\"xMidYMid slice\">\r\n        <defs>\r\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--primary-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--primary-light)\" />\r\n          </linearGradient>\r\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"100%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--accent-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--accent-light)\" />\r\n          </linearGradient>\r\n        </defs>\r\n\r\n        {/* Dynamic Shapes inspired by loader.tsx geometric patterns */}\r\n        <path\r\n          className=\"shape float-animation\"\r\n          d=\"M 500 0 C 400 150, 600 250, 500 400 S 700 550, 800 400 S 900 250, 800 100 S 600 0, 500 0 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <circle\r\n          className=\"shape float-animation-alt\"\r\n          cx=\"1000\"\r\n          cy=\"200\"\r\n          r=\"150\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <path\r\n          className=\"shape float-animation-delay\"\r\n          d=\"M 200 600 Q 300 750, 500 700 T 700 650 Q 600 500, 400 550 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.15\"\r\n        />\r\n        <ellipse\r\n          className=\"shape float-animation-longer\"\r\n          cx=\"300\"\r\n          cy=\"100\"\r\n          rx=\"100\"\r\n          ry=\"50\"\r\n          transform=\"rotate(45 300 100)\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n\r\n        {/* Pulsating dots representing connection points */}\r\n        <circle className=\"dot pulse-animation-1\" cx=\"400\" cy=\"200\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-2\" cx=\"700\" cy=\"500\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-3\" cx=\"1100\" cy=\"100\" r=\"8\" fill=\"var(--primary)\" />\r\n\r\n        {/* Connecting lines between dots */}\r\n        <line\r\n          className=\"connecting-line draw-animation-1\"\r\n          x1=\"400\"\r\n          y1=\"200\"\r\n          x2=\"700\"\r\n          y2=\"500\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n        <line\r\n          className=\"connecting-line draw-animation-2\"\r\n          x1=\"700\"\r\n          y1=\"500\"\r\n          x2=\"1100\"\r\n          y2=\"100\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n      </svg>\r\n    </StyledWrapper>\r\n  );\r\n};\r\n\r\nconst StyledWrapper = styled.div`\r\n  position: fixed;\r\n  inset: 0;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n  width: 100vw;\r\n  height: 100vh;\r\n\r\n  #background_svg {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .shape {\r\n    transform-origin: center center;\r\n  }\r\n\r\n  /* Custom CSS Variables for gradients and colors, assuming these exist in the theme */\r\n  /* If not, these would need to be defined or mapped to existing Tailwind colors */\r\n  --primary-dark: hsl(var(--primary));\r\n  --primary-light: hsl(var(--primary) / 0.7);\r\n  --accent-dark: hsl(var(--accent));\r\n  --accent-light: hsl(var(--accent) / 0.7);\r\n\r\n\r\n  @keyframes floatAndBounce {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(10px, -20px) rotate(5deg); }\r\n    50% { transform: translate(-10px, 20px) rotate(-5deg); }\r\n    75% { transform: translate(5px, -10px) rotate(2deg); }\r\n  }\r\n\r\n  @keyframes floatAndBounceAlt {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(-15px, 10px) rotate(-3deg); }\r\n    50% { transform: translate(15px, -10px) rotate(3deg); }\r\n    75% { transform: translate(-5px, 5px) rotate(-1deg); }\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); opacity: 0.8; }\r\n    50% { transform: scale(1.2); opacity: 1; }\r\n  }\r\n\r\n  @keyframes drawLine {\r\n    to {\r\n      stroke-dashoffset: 0;\r\n    }\r\n  }\r\n\r\n  .float-animation {\r\n    animation: floatAndBounce 20s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-alt {\r\n    animation: floatAndBounceAlt 25s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-delay {\r\n    animation: floatAndBounce 22s infinite ease-in-out 2s; /* Add a delay */\r\n  }\r\n\r\n  .float-animation-longer {\r\n    animation: floatAndBounceAlt 28s infinite ease-in-out 4s; /* Longer duration, more delay */\r\n  }\r\n\r\n  .pulse-animation-1 {\r\n    animation: pulse 2s infinite ease-in-out;\r\n  }\r\n\r\n  .pulse-animation-2 {\r\n    animation: pulse 2s infinite ease-in-out 0.5s;\r\n  }\r\n\r\n  .pulse-animation-3 {\r\n    animation: pulse 2s infinite ease-in-out 1s;\r\n  }\r\n\r\n  .draw-animation-1 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 1s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n\r\n  .draw-animation-2 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 3s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n`;\r\n\r\nexport default FloatingBackground; "], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAAqB;IACzB,qBACE,8OAAC;kBACC,cAAA,8OAAC;YAAI,IAAG;YAAiB,OAAM;YAA6B,SAAQ;YAAe,MAAK;YAAO,qBAAoB;;8BACjH,8OAAC;;sCACC,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC1D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;sCAEhC,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAO,IAAG;4BAAO,IAAG;;8CAC5D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;;;;;;;8BAKlC,8OAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,aAAY;;;;;;8BAId,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAO,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BAGxE,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;8BAEnB,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;;;;;;;;;;;;AAK3B;AAEA,MAAM,gBAAgB,2KAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FjC,CAAC;uCAEc", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/index.ts"], "sourcesContent": ["export { AnimatedButton } from './AnimatedButton';\r\nexport { SectionHeader } from './SectionHeader';\r\nexport { default as FloatingBackground } from './FloatingBackground';"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/constants.ts"], "sourcesContent": ["import { Target, MapPin, Brain, Handshake, Compass, Network } from 'lucide-react';\r\n\r\nexport const UNIQUE_FEATURES = [\r\n  {\r\n    icon: Compass,\r\n    title: 'Strategische Navigation',\r\n    description: 'Verwandeln Sie Ihre Geschäftsziele in eine präzise Roadmap für lokale Partnerschaften und Marktexpansion',\r\n    details: [\r\n      'KI-gestützte Strategieentwicklung basierend auf Ihren spezifischen Zielen',\r\n      'Automatische Generierung von Strategie-Papieren für verschiedene Szenarien',\r\n      'Dynamische Anpassung an Marktveränderungen und neue Opportunities'\r\n    ],\r\n    delay: 0.1,\r\n    accentColor: '#3b82f6'\r\n  },\r\n  {\r\n    icon: MapPin,\r\n    title: 'Lokale Intelligenz',\r\n    description: 'Entdecken Sie versteckte Potenziale in Ihrer direkten Nachbarschaft durch unsere einzigartige Kartenansicht',\r\n    details: [\r\n      'Geografische Visualisierung aller relevanten Unternehmen in Ihrer Region',\r\n      'Echtzeit-Updates zu neuen Geschäftsmöglichkeiten in Ihrem Umkreis',\r\n      'Standortbasierte Matching-Algorithmen für optimale Partnerschaften'\r\n    ],\r\n    delay: 0.2,\r\n    accentColor: '#10b981'\r\n  },\r\n  {\r\n    icon: Brain,\r\n    title: 'Intelligente Analyse',\r\n    description: 'Lassen Sie unsere KI für jedes ausgewählte Unternehmen maßgeschneiderte Kooperationsmöglichkeiten identifizieren',\r\n    details: [\r\n      'Deep-Learning-Analyse von Geschäftsmodellen und Marktpositionierung',\r\n      'Automatische Erkennung von Synergiepotenzialen und Win-Win-Situationen',\r\n      'Priorisierung nach Erfolgswahrscheinlichkeit und strategischem Wert'\r\n    ],\r\n    delay: 0.3,\r\n    accentColor: '#8b5cf6'\r\n  },\r\n  {\r\n    icon: Network,\r\n    title: 'Strategische Vernetzung',\r\n    description: 'Bauen Sie systematisch Ihr lokales Unternehmensnetzwerk auf und erschließen Sie neue Geschäftsfelder',\r\n    details: [\r\n      'Strukturierte Kontaktaufnahme mit vorbereiteten Strategieanalysen',\r\n      'Tracking und Management Ihrer Partnerschaftsinitiativen',\r\n      'Langfristige Beziehungspflege durch kontinuierliche Opportunity-Updates'\r\n    ],\r\n    delay: 0.4,\r\n    accentColor: '#f59e0b'\r\n  }\r\n];\r\n\r\nexport const PROCESS_STEPS = [\r\n  {\r\n    step: '01',\r\n    icon: Target,\r\n    title: 'Strategie definieren',\r\n    description: 'Beschreiben Sie Ihre Ziele - ob neue Partnerschaften, Salesexpansion oder regionale Kooperationen'\r\n  },\r\n  {\r\n    step: '02', \r\n    icon: MapPin,\r\n    title: 'Umgebung scannen',\r\n    description: 'Unsere KI analysiert Ihr lokales Geschäftsumfeld und identifiziert relevante Unternehmen'\r\n  },\r\n  {\r\n    step: '03',\r\n    icon: Brain,\r\n    title: 'Potenziale analysieren',\r\n    description: 'Für jedes Unternehmen werden spezifische Kooperationsmöglichkeiten und Synergien bewertet'\r\n  },\r\n  {\r\n    step: '04',\r\n    icon: Handshake,\r\n    title: 'Partnerschaften initiieren',\r\n    description: 'Mit maßgeschneiderten Strategieanalysen gehen Sie professionell in den Kontakt'\r\n  }\r\n];\r\n\r\nexport const HERO_BADGES = [\r\n  { text: \"Kostenfrei starten\", color: \"green\" },\r\n  { text: \"Sofort einsatzbereit\", color: \"blue\" },\r\n  { text: \"DSGVO-konform\", color: \"purple\" }\r\n];\r\n\r\nexport const CTA_FEATURES = [\r\n  \"Keine Vertragsbindung\",\r\n  \"Sofort einsatzbereit\", \r\n  \"Premium Support\"\r\n];"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEO,MAAM,kBAAkB;IAC7B;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,gBAAgB;IAC3B;QACE,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,cAAc;IACzB;QAAE,MAAM;QAAsB,OAAO;IAAQ;IAC7C;QAAE,MAAM;QAAwB,OAAO;IAAO;IAC9C;QAAE,MAAM;QAAiB,OAAO;IAAS;CAC1C;AAEM,MAAM,eAAe;IAC1B;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/index.ts"], "sourcesContent": ["export * from './constants';"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/HeroContent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Zap } from 'lucide-react';\r\nimport { AnimatedButton } from '../shared';\r\nimport { HERO_BADGES } from '../../utils';\r\n\r\ninterface HeroContentProps {\r\n  onGetStarted: () => void;\r\n}\r\n\r\nexport const HeroContent: React.FC<HeroContentProps> = ({ onGetStarted }) => {\r\n  return (\r\n    <motion.div \r\n      className=\"space-y-6 sm:space-y-8\"\r\n      initial={{ opacity: 0, x: -50 }}\r\n      animate={{ opacity: 1, x: 0 }}\r\n      transition={{ duration: 0.8 }}\r\n    >\r\n      {/* Badge */}\r\n      <motion.div \r\n        className=\"inline-flex items-center space-x-2 sm:space-x-3 px-3 sm:px-5 py-2 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 border border-primary/30 backdrop-blur-sm\"\r\n        initial={{ opacity: 0, scale: 0.8 }}\r\n        animate={{ opacity: 1, scale: 1 }}\r\n        transition={{ delay: 0.2, duration: 0.6 }}\r\n      >\r\n        <div className=\"relative\">\r\n          <Zap className=\"w-3 h-3 sm:w-4 sm:h-4 text-primary\" />\r\n          <motion.div\r\n            className=\"absolute inset-0 bg-primary/20 rounded-full\"\r\n            animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}\r\n            transition={{ duration: 2, repeat: Infinity }}\r\n          />\r\n        </div>\r\n        <span className=\"text-xs sm:text-sm font-semibold text-primary\">\r\n          <span className=\"hidden sm:inline\">Die neue Dimension strategischer Geschäftsentwicklung</span>\r\n          <span className=\"sm:hidden\">Strategische Geschäftsentwicklung</span>\r\n        </span>\r\n      </motion.div>\r\n\r\n      {/* Main Heading */}\r\n      <motion.h1 \r\n        className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight\"\r\n        initial={{ opacity: 0, y: 30 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.4, duration: 0.8 }}\r\n      >\r\n        Lokale{' '}\r\n        <span className=\"bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent\">\r\n          Geschäftspotenziale\r\n        </span>{' '}\r\n        systematisch{' '}\r\n        <span className=\"text-primary\">erschließen</span>\r\n      </motion.h1>\r\n\r\n      {/* Description */}\r\n      <motion.p \r\n        className=\"text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-2xl\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.6, duration: 0.8 }}\r\n      >\r\n        OpuMap revolutioniert die Art, wie Unternehmen strategische Partnerschaften entwickeln. \r\n        Definieren Sie Ihre Ziele, visualisieren Sie Ihr lokales Geschäftsumfeld und lassen Sie \r\n        KI maßgeschneiderte Kooperationsmöglichkeiten für Sie identifizieren.\r\n      </motion.p>\r\n\r\n      {/* CTA Buttons */}\r\n      <motion.div \r\n        className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 pt-4\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ delay: 0.8, duration: 0.8 }}\r\n      >\r\n        <AnimatedButton\r\n          onClick={onGetStarted}\r\n          variant=\"primary\"\r\n          size=\"medium\"\r\n          showArrow\r\n          className=\"w-full sm:w-auto\"\r\n        >\r\n          Strategie entwickeln\r\n        </AnimatedButton>\r\n        \r\n        <AnimatedButton\r\n          onClick={() => {}}\r\n          variant=\"secondary\"\r\n          size=\"medium\"\r\n          className=\"w-full sm:w-auto\"\r\n        >\r\n          <span className=\"hidden sm:inline\">Live Demo anzeigen</span>\r\n          <span className=\"sm:hidden\">Live Demo</span>\r\n        </AnimatedButton>\r\n      </motion.div>\r\n\r\n      {/* Feature Badges */}\r\n      <motion.div \r\n        className=\"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 pt-4 sm:pt-6 text-xs sm:text-sm text-muted-foreground\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ delay: 1 }}\r\n      >\r\n        {HERO_BADGES.map((badge, index) => (\r\n          <div key={index} className=\"flex items-center space-x-2\">\r\n            <div className={`w-2 h-2 rounded-full bg-${badge.color}-500`}></div>\r\n            <span>{badge.text}</span>\r\n          </div>\r\n        ))}\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;AAYO,MAAM,cAA0C,CAAC,EAAE,YAAY,EAAE;IACtE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAAE,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCAAC;gCACtD,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;;;;;;;;;;;;kCAGhD,8OAAC;wBAAK,WAAU;;0CACd,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAY;;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;oBACzC;oBACQ;kCACP,8OAAC;wBAAK,WAAU;kCAA4E;;;;;;oBAEpF;oBAAI;oBACC;kCACb,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAIjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;0BACzC;;;;;;0BAOD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,8OAAC,oKAAA,CAAA,iBAAc;wBACb,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,8OAAC,oKAAA,CAAA,iBAAc;wBACb,SAAS,KAAO;wBAChB,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAY;;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAE;0BAEtB,+IAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAW,CAAC,wBAAwB,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC;;;;;;0CAC5D,8OAAC;0CAAM,MAAM,IAAI;;;;;;;uBAFT;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useMapDemo.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useMemo } from 'react';\r\n\r\nexport interface Company {\r\n  id: number;\r\n  name: string;\r\n  x: number;\r\n  y: number;\r\n  type: string;\r\n  match: number;\r\n}\r\n\r\nexport const useMapDemo = () => {\r\n  const [activeStep, setActiveStep] = useState(0);\r\n  const [hoveredCompany, setHoveredCompany] = useState<number | null>(null);\r\n\r\n  const companies: Company[] = useMemo(() => [\r\n    { id: 1, name: \"TechStart GmbH\", x: 25, y: 35, type: \"tech\", match: 95 },\r\n    { id: 2, name: \"Regional Handel\", x: 60, y: 55, type: \"retail\", match: 78 },\r\n    { id: 3, name: \"Innovation Hub\", x: 40, y: 25, type: \"innovation\", match: 88 },\r\n    { id: 4, name: \"Local Services\", x: 75, y: 70, type: \"service\", match: 65 },\r\n    { id: 5, name: \"Manufaktur Nord\", x: 15, y: 65, type: \"production\", match: 82 }\r\n  ], []);\r\n\r\n  const steps = useMemo(() => [\r\n    \"Ihre Strategie definieren\",\r\n    \"Lokale Unternehmen scannen\", \r\n    \"Potenziale analysieren\",\r\n    \"Partnerschaften initiieren\"\r\n  ], []);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setActiveStep((prev) => (prev + 1) % steps.length);\r\n    }, 3000);\r\n    return () => clearInterval(interval);\r\n  }, [steps.length]);\r\n\r\n  return {\r\n    activeStep,\r\n    hoveredCompany,\r\n    setHoveredCompany,\r\n    companies,\r\n    steps\r\n  };\r\n};"], "names": [], "mappings": ";;;AAEA;AAFA;;AAaO,MAAM,aAAa;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,YAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACzC;gBAAE,IAAI;gBAAG,MAAM;gBAAkB,GAAG;gBAAI,GAAG;gBAAI,MAAM;gBAAQ,OAAO;YAAG;YACvE;gBAAE,IAAI;gBAAG,MAAM;gBAAmB,GAAG;gBAAI,GAAG;gBAAI,MAAM;gBAAU,OAAO;YAAG;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAkB,GAAG;gBAAI,GAAG;gBAAI,MAAM;gBAAc,OAAO;YAAG;YAC7E;gBAAE,IAAI;gBAAG,MAAM;gBAAkB,GAAG;gBAAI,GAAG;gBAAI,MAAM;gBAAW,OAAO;YAAG;YAC1E;gBAAE,IAAI;gBAAG,MAAM;gBAAmB,GAAG;gBAAI,GAAG;gBAAI,MAAM;gBAAc,OAAO;YAAG;SAC/E,EAAE,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YAC1B;YACA;YACA;YACA;SACD,EAAE,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;QACnD,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,MAAM,MAAM;KAAC;IAEjB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useResponsiveCheck.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport const useResponsiveCheck = () => {\r\n  const [isTouchDevice, setIsTouchDevice] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;\r\n    setIsTouchDevice(isTouch);\r\n  }, []);\r\n\r\n  return { isTouchDevice };\r\n};"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIO,MAAM,qBAAqB;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,kBAAkB,UAAU,UAAU,cAAc,GAAG;QACvE,iBAAiB;IACnB,GAAG,EAAE;IAEL,OAAO;QAAE;IAAc;AACzB", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/index.ts"], "sourcesContent": ["export { useMapDemo } from './useMapDemo';\r\nexport { useResponsiveCheck } from './useResponsiveCheck';\r\nexport type { Company } from './useMapDemo';"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/InteractiveMapDemo.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useMapDemo, Company } from '../../hooks';\r\n\r\nexport const InteractiveMapDemo: React.FC = () => {\r\n  const { activeStep, hoveredCompany, setHoveredCompany, companies, steps } = useMapDemo();\r\n\r\n  return (\r\n    <>\r\n      <div className=\"relative w-full h-64 sm:h-80 lg:h-96 bg-gradient-to-br from-muted/30 to-muted/10 rounded-2xl sm:rounded-3xl border border-border/20 backdrop-blur-sm\">\r\n        {/* Map Grid */}\r\n        <div className=\"absolute inset-0 opacity-20\">\r\n          <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\">\r\n            <defs>\r\n              <pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\r\n                <path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"0.5\"/>\r\n              </pattern>\r\n            </defs>\r\n            <rect width=\"100\" height=\"100\" fill=\"url(#grid)\" />\r\n          </svg>\r\n        </div>\r\n\r\n        {/* Strategy Area */}\r\n        <motion.div \r\n          className={`absolute top-2 left-2 sm:top-4 sm:left-4 bg-primary/20 backdrop-blur-md rounded-lg sm:rounded-xl p-2 sm:p-3 border min-w-[140px] sm:min-w-[200px] ${\r\n            activeStep === 0 ? 'border-primary' : 'border-primary/30'\r\n          }`}\r\n          initial={{ opacity: 0, scale: 0.8 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ delay: 0.5, duration: 0.5 }}\r\n        >\r\n          <div className=\"text-xs font-medium text-primary mb-1\">Ihre Strategie</div>\r\n          <motion.div \r\n            className={`text-xs sm:text-sm font-bold mb-1 sm:mb-2 ${\r\n              activeStep === 0 ? 'text-primary' : 'text-foreground'\r\n            }`}\r\n          >\r\n            Lokale Partnerschaften\r\n          </motion.div>        \r\n          \r\n          {/* Strategy Details */}\r\n          <AnimatePresence>\r\n            {activeStep === 0 && (\r\n              <motion.div\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: 'auto' }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                transition={{ duration: 0.3 }}\r\n                className=\"space-y-1 border-t border-primary/20 pt-1 sm:pt-2\"\r\n              >\r\n                {[\r\n                  { text: \"Ziel: Neue Kunden gewinnen\", shortText: \"Neue Kunden\" },\r\n                  { text: \"Branche: Tech & Innovation\", shortText: \"Tech\" },\r\n                  { text: \"Radius: 25km\", shortText: \"Radius: 25km\" }\r\n                ].map((item, index) => (\r\n                  <motion.div \r\n                    key={index}\r\n                    className=\"text-xs text-primary/80 flex items-center gap-1 sm:gap-2\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    transition={{ delay: 0.1 * (index + 1) }}\r\n                  >\r\n                    <div className=\"w-1 h-1 rounded-full bg-primary animate-pulse\"></div>\r\n                    <span className=\"hidden sm:inline\">{item.text}</span>\r\n                    <span className=\"sm:hidden\">{item.shortText}</span>\r\n                  </motion.div>\r\n                ))}\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n          \r\n          {/* Pulsing Effect */}\r\n          {activeStep === 0 && (\r\n            <motion.div\r\n              className=\"absolute inset-0 rounded-lg sm:rounded-xl border-2 border-primary/40\"\r\n              animate={{ scale: [1, 1.05, 1], opacity: [0.3, 0.6, 0.3] }}\r\n              transition={{ duration: 2, repeat: Infinity }}\r\n            />\r\n          )}\r\n        </motion.div>\r\n\r\n        {/* Success Badge */}\r\n        <motion.div\r\n          className=\"absolute -bottom-1 -left-2 sm:-bottom-2 sm:-left-3 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg z-20\"\r\n          initial={{ opacity: 0, scale: 0.8 }}\r\n          animate={{ \r\n            opacity: 1, \r\n            scale: 1, \r\n            y: [0, 10, 0] \r\n          }}\r\n          transition={{\r\n            opacity: { delay: 0.7, duration: 0.7, ease: \"circOut\" },\r\n            scale: { delay: 0.7, duration: 0.7, ease: \"circOut\" },\r\n            y: {\r\n              delay: 0.7,\r\n              duration: 4,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }\r\n          }}\r\n        >\r\n          <div className=\"text-lg sm:text-2xl font-bold text-green-500\">2.3x</div>\r\n          <div className=\"text-xs text-muted-foreground\">\r\n            <span className=\"hidden sm:inline\">Mehr Partnerschaften</span>\r\n            <span className=\"sm:hidden\">Partner</span>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Companies on Map */}\r\n        {companies.map((company, index) => (\r\n          <CompanyMarker\r\n            key={company.id}\r\n            company={company}\r\n            index={index}\r\n            activeStep={activeStep}\r\n            hoveredCompany={hoveredCompany}\r\n            onHover={setHoveredCompany}\r\n          />\r\n        ))}\r\n\r\n        {/* Connection Lines */}\r\n        {activeStep >= 2 && (\r\n          <svg className=\"absolute inset-0 w-full h-full pointer-events-none\">\r\n            {companies.filter(c => c.match > 75).map((company, index) => (\r\n              <g key={company.id}>\r\n                <motion.line\r\n                  x1=\"20%\"\r\n                  y1=\"20%\"\r\n                  x2={`${company.x}%`}\r\n                  y2={`${company.y}%`}\r\n                  stroke=\"#3b82f6\"\r\n                  strokeWidth=\"2\"\r\n                  strokeDasharray=\"4,4\"\r\n                  opacity={0.6}\r\n                  initial={{ pathLength: 0 }}\r\n                  animate={{ pathLength: 1 }}\r\n                  transition={{ delay: index * 0.3 + 2, duration: 1 }}\r\n                />\r\n                \r\n                <motion.circle\r\n                  r=\"2\"\r\n                  fill=\"#3b82f6\"\r\n                  opacity={0.8}\r\n                  initial={{ cx: \"20%\", cy: \"20%\" }}\r\n                  animate={{ cx: `${company.x}%`, cy: `${company.y}%` }}\r\n                  transition={{ \r\n                    delay: index * 0.3 + 2.5, \r\n                    duration: 1.5,\r\n                    repeat: Infinity,\r\n                    repeatType: \"loop\",\r\n                    repeatDelay: 3\r\n                  }}\r\n                />\r\n              </g>\r\n            ))}\r\n          </svg>\r\n        )}\r\n      </div>\r\n\r\n      {/* Info Panel */}\r\n      <InfoPanel activeStep={activeStep} steps={steps} companies={companies} />\r\n    </>\r\n  );\r\n};\r\n\r\ninterface CompanyMarkerProps {\r\n  company: Company;\r\n  index: number;\r\n  activeStep: number;\r\n  hoveredCompany: number | null;\r\n  onHover: (id: number | null) => void;\r\n}\r\n\r\nconst CompanyMarker: React.FC<CompanyMarkerProps> = ({\r\n  company,\r\n  index,\r\n  activeStep,\r\n  hoveredCompany,\r\n  onHover\r\n}) => (\r\n  <motion.div\r\n    className={`absolute cursor-pointer transition-all duration-300 ${\r\n      hoveredCompany === company.id \r\n        ? 'scale-125 sm:scale-150 z-20' \r\n        : activeStep >= 1 ? 'scale-100' : 'scale-0'\r\n    }`}\r\n    style={{ left: `${company.x}%`, top: `${company.y}%` }}\r\n    initial={{ scale: 0, opacity: 0 }}\r\n    animate={{ \r\n      scale: activeStep >= 1 ? 1 : 0, \r\n      opacity: activeStep >= 1 ? 1 : 0 \r\n    }}\r\n    transition={{ delay: index * 0.2 + 1 }}\r\n    onMouseEnter={() => onHover(company.id)}\r\n    onMouseLeave={() => onHover(null)}\r\n  >\r\n    <div \r\n      className=\"w-3 h-3 sm:w-4 sm:h-4 rounded-full relative\"\r\n      style={{ \r\n        background: `radial-gradient(circle, hsl(${company.match * 1.2}, 80%, 65%), hsl(${company.match * 1.2}, 60%, 45%))`\r\n      }}\r\n    >\r\n      <div \r\n        className=\"absolute inset-0.5 sm:inset-1 rounded-full\"\r\n        style={{ backgroundColor: `hsl(${company.match * 1.2}, 90%, 80%)` }}\r\n      />\r\n    </div>\r\n\r\n    {/* Pulse Animations */}\r\n    <motion.div \r\n      className=\"absolute -inset-1 rounded-full border-2\"\r\n      animate={{ scale: [1, 2.2, 1], opacity: [0.4, 0, 0.4] }}\r\n      transition={{ \r\n        duration: 3, \r\n        repeat: Infinity, \r\n        delay: index * 0.5,\r\n        ease: [0.4, 0, 0.6, 1],\r\n        repeatType: \"loop\"\r\n      }}\r\n      style={{ borderColor: `hsl(${company.match * 1.2}, 70%, 60%)` }}\r\n    />\r\n\r\n    {/* Tooltip */}\r\n    <AnimatePresence>\r\n      {hoveredCompany === company.id && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 5, scale: 0.9 }}\r\n          animate={{ opacity: 1, y: -8, scale: 1 }}\r\n          exit={{ opacity: 0, y: 5, scale: 0.9 }}\r\n          transition={{ duration: 0.2, ease: \"easeOut\" }}\r\n          className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 bg-background/95 backdrop-blur-sm rounded-lg p-2 border border-border/50 shadow-lg z-30 hidden sm:block\"\r\n          style={{\r\n            minWidth: 'max-content',\r\n            maxWidth: '140px',\r\n            left: company.x > 80 ? 'auto' : '50%',\r\n            right: company.x > 80 ? '0' : 'auto',\r\n            transform: company.x > 80 ? 'translateX(0)' : 'translateX(-50%)'\r\n          }}\r\n        >\r\n          <div className=\"text-xs font-medium mb-1 truncate\">{company.name}</div>\r\n          <div className=\"flex items-center justify-between gap-2\">\r\n            <span className=\"text-xs text-muted-foreground\">Match:</span>\r\n            <div className=\"flex items-center gap-1\">\r\n              <div className=\"w-8 h-1 rounded-full bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 relative overflow-hidden\">\r\n                <motion.div\r\n                  className=\"absolute inset-y-0 left-0 bg-white/40 rounded-full\"\r\n                  initial={{ width: 0 }}\r\n                  animate={{ width: `${company.match}%` }}\r\n                  transition={{ duration: 0.6, delay: 0.1 }}\r\n                />\r\n              </div>\r\n              <span \r\n                className=\"text-xs font-bold tabular-nums\" \r\n                style={{ color: `hsl(${company.match * 1.2}, 70%, 60%)` }}\r\n              >\r\n                {company.match}%\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  </motion.div>\r\n);\r\n\r\ninterface InfoPanelProps {\r\n  activeStep: number;\r\n  steps: string[];\r\n  companies: Company[];\r\n}\r\n\r\nconst InfoPanel: React.FC<InfoPanelProps> = ({ activeStep, steps, companies }) => (\r\n  <div className=\"mt-8 sm:mt-10\">\r\n    <motion.div \r\n      className=\"bg-background/95 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 p-4 sm:p-6 shadow-lg h-[320px] sm:h-[300px]\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ delay: 0.3, duration: 0.6 }}\r\n    >\r\n      {/* Step Header */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <motion.div \r\n            className=\"w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary flex items-center justify-center text-white text-sm sm:text-base font-bold\"\r\n            key={activeStep}\r\n            initial={{ scale: 0.8 }}\r\n            animate={{ scale: 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n          >\r\n            {activeStep + 1}\r\n          </motion.div>\r\n          <div>\r\n            <motion.h3 \r\n              className=\"text-sm sm:text-base font-semibold\"\r\n              key={activeStep}\r\n              initial={{ opacity: 0, x: -10 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              {steps[activeStep]}\r\n            </motion.h3>\r\n            <div className=\"text-xs text-muted-foreground\">\r\n              Schritt {activeStep + 1} von {steps.length}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Progress Dots */}\r\n        <div className=\"flex gap-1.5\">\r\n          {steps.map((_, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className={`w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-colors duration-300 ${\r\n                index <= activeStep ? 'bg-primary' : 'bg-muted-foreground/30'\r\n              }`}\r\n              animate={{ scale: index === activeStep ? 1.2 : 1 }}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Step Content */}\r\n      <AnimatePresence mode=\"wait\">\r\n        <StepContent \r\n          activeStep={activeStep} \r\n          companies={companies} \r\n        />\r\n      </AnimatePresence>\r\n    </motion.div>\r\n  </div>\r\n);\r\n\r\ninterface StepContentProps {\r\n  activeStep: number;\r\n  companies: Company[];\r\n}\r\n\r\nconst StepContent: React.FC<StepContentProps> = ({ activeStep, companies }) => {\r\n  const stepVariants = {\r\n    initial: { opacity: 0, x: 20 },\r\n    animate: { opacity: 1, x: 0 },\r\n    exit: { opacity: 0, x: -20 }\r\n  };\r\n\r\n  const contentByStep = [\r\n    // Step 0: Strategy Definition\r\n    <motion.div\r\n      key=\"step-0\"\r\n      variants={stepVariants}\r\n      initial=\"initial\"\r\n      animate=\"animate\"\r\n      exit=\"exit\"\r\n      transition={{ duration: 0.3 }}\r\n      className=\"flex flex-col h-full py-4 px-3\"\r\n    >\r\n      <h3 className=\"text-sm font-medium mb-0.5\">Strategiedefinition</h3>\r\n      <p className=\"text-xs text-muted-foreground mb-2.5\">Ihre Partnersuche konfigurieren</p>\r\n      \r\n      <div className=\"grid gap-2 mx-0.5\">\r\n        {[\r\n          \"Neue Kunden gewinnen\",\r\n          \"Tech & Innovation\", \r\n          \"Radius: 25km\"\r\n        ].map((item, index) => (\r\n          <div key={index} className=\"flex items-center gap-1.5 p-1.5 bg-primary/5 rounded-md border border-primary/20\">\r\n            <div className=\"w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center\">\r\n              <div className=\"w-1.5 h-1.5 rounded-full bg-primary animate-pulse\"></div>\r\n            </div>\r\n            <div className=\"font-medium text-xs\">{item}</div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </motion.div>,\r\n\r\n    // Step 1: Company Scan\r\n    <motion.div\r\n      key=\"step-1\"\r\n      variants={stepVariants}\r\n      initial=\"initial\"\r\n      animate=\"animate\"\r\n      exit=\"exit\"\r\n      transition={{ duration: 0.3 }}\r\n      className=\"flex flex-col h-full py-5 px-4\"\r\n    >\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div>\r\n          <h3 className=\"text-base font-medium\">Unternehmensscan</h3>\r\n          <p className=\"text-xs text-muted-foreground\">Partner identifizieren</p>\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-1.5 bg-blue-500/10 px-2 py-1 rounded-full\">\r\n          <motion.div\r\n            className=\"w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full\"\r\n            animate={{ rotate: 360 }}\r\n            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n          />\r\n          <span className=\"text-xs font-medium text-blue-500\">\r\n            {companies.length}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div>\r\n        <div className=\"text-xs text-muted-foreground mb-2 ml-1\">Gefundene Unternehmen:</div>\r\n        <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2.5 mx-1\">\r\n          {companies.slice(0, 4).map((company, index) => (\r\n            <motion.div\r\n              key={company.id}\r\n              initial={{ opacity: 0, scale: 0.95 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ delay: index * 0.1 }}\r\n              className=\"flex flex-col p-1.5 bg-blue-500/10 rounded-lg border border-blue-500/20\"\r\n            >\r\n              <div className=\"font-medium text-xs truncate\">{company.name}</div>\r\n              <div className=\"text-xs text-blue-500/80 mt-0.5\">{company.type}</div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </motion.div>,\r\n\r\n    // Step 2: Analysis\r\n    <motion.div\r\n      key=\"step-2\"\r\n      variants={stepVariants}\r\n      initial=\"initial\"\r\n      animate=\"animate\"\r\n      exit=\"exit\"\r\n      transition={{ duration: 0.3 }}\r\n      className=\"space-y-3 flex flex-col h-full justify-start\"\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Matching-Scores werden berechnet...\r\n        </div>\r\n        <div className=\"text-sm font-semibold text-purple-500\">\r\n          {companies.filter(c => c.match > 75).length} potenzielle Partner\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"w-full bg-purple-500/20 rounded-full h-2\">\r\n        <motion.div\r\n          className=\"bg-purple-400 h-2 rounded-full\"\r\n          initial={{ width: 0 }}\r\n          animate={{ width: \"100%\" }}\r\n          transition={{ delay: 0.5, duration: 2 }}\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-2 gap-1.5 overflow-hidden\">\r\n        {companies.filter(c => c.match > 75).map((company, index) => (\r\n          <motion.div\r\n            key={company.id}\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 1 + index * 0.1 }}\r\n            className=\"p-2 bg-purple-500/10 rounded-md border border-purple-500/20 min-w-0\"\r\n          >\r\n            <div className=\"flex flex-col min-w-0\">\r\n              <div className=\"text-xs font-medium truncate mb-1.5\" title={company.name}>\r\n                {company.name}\r\n              </div>\r\n              <div className=\"flex items-center justify-between mb-1\">\r\n                <div className=\"w-5 h-0.5 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full overflow-hidden\">\r\n                  <motion.div\r\n                    className=\"h-full bg-white/40 rounded-full\"\r\n                    initial={{ width: 0 }}\r\n                    animate={{ width: `${company.match}%` }}\r\n                    transition={{ delay: 1.2 + index * 0.1, duration: 0.4 }}\r\n                  />\r\n                </div>\r\n                <span className=\"text-xs font-bold text-purple-500 ml-1.5\">{company.match}%</span>\r\n              </div>\r\n              <div className=\"text-xs text-muted-foreground/80\">Potenzial</div>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n    </motion.div>,\r\n\r\n    // Step 3: Partnerships\r\n    <motion.div\r\n      key=\"step-3\"\r\n      variants={stepVariants}\r\n      initial=\"initial\"\r\n      animate=\"animate\"\r\n      exit=\"exit\"\r\n      transition={{ duration: 0.3 }}\r\n      className=\"space-y-3 flex flex-col h-full justify-start\"\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Premium-Partner bereit für Kontakt:\r\n        </div>\r\n        <div className=\"text-sm font-semibold text-green-500\">\r\n          {companies.filter(c => c.match > 85).length} Premium-Partner\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"flex justify-center gap-2 py-2\">\r\n        {[...Array(5)].map((_, i) => (\r\n          <motion.div\r\n            key={i}\r\n            className=\"w-3 h-3 rounded-full bg-green-400\"\r\n            animate={{ \r\n              scale: [0.8, 1.3, 0.8], \r\n              opacity: [0.4, 1, 0.4] \r\n            }}\r\n            transition={{ \r\n              duration: 2, \r\n              repeat: Infinity, \r\n              delay: i * 0.3,\r\n              repeatType: \"loop\"\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"space-y-2\">\r\n        {companies.filter(c => c.match > 85).map((company, index) => (\r\n          <motion.div\r\n            key={company.id}\r\n            initial={{ opacity: 0, x: -10 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ delay: index * 0.2 }}\r\n            className=\"p-3 bg-green-500/10 rounded-lg border border-green-500/20 flex items-center justify-between\"\r\n          >\r\n            <div>\r\n              <div className=\"text-sm font-medium\">{company.name}</div>\r\n              <div className=\"text-xs text-green-600\">Bereit für Partnerschaft</div>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-sm font-bold text-green-500\">{company.match}%</div>\r\n              <div className=\"text-xs text-muted-foreground\">Match</div>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </div>\r\n    </motion.div>\r\n  ];\r\n\r\n  return contentByStep[activeStep] || null;\r\n};"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAAA;AAJA;;;;AAMO,MAAM,qBAA+B;IAC1C,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,iBAAiB,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,aAAU,AAAD;IAErF,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAgB,SAAQ;;8CACrC,8OAAC;8CACC,cAAA,8OAAC;wCAAQ,IAAG;wCAAO,OAAM;wCAAK,QAAO;wCAAK,cAAa;kDACrD,cAAA,8OAAC;4CAAK,GAAE;4CAAoB,MAAK;4CAAO,QAAO;4CAAe,aAAY;;;;;;;;;;;;;;;;8CAG9E,8OAAC;oCAAK,OAAM;oCAAM,QAAO;oCAAM,MAAK;;;;;;;;;;;;;;;;;kCAKxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,CAAC,kJAAkJ,EAC5J,eAAe,IAAI,mBAAmB,qBACtC;wBACF,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;;0CAExC,8OAAC;gCAAI,WAAU;0CAAwC;;;;;;0CACvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAW,CAAC,0CAA0C,EACpD,eAAe,IAAI,iBAAiB,mBACpC;0CACH;;;;;;0CAKD,8OAAC,yLAAA,CAAA,kBAAe;0CACb,eAAe,mBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAET;wCACC;4CAAE,MAAM;4CAA8B,WAAW;wCAAc;wCAC/D;4CAAE,MAAM;4CAA8B,WAAW;wCAAO;wCACxD;4CAAE,MAAM;4CAAgB,WAAW;wCAAe;qCACnD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;4CAAE;;8DAEvC,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,IAAI;;;;;;8DAC7C,8OAAC;oDAAK,WAAU;8DAAa,KAAK,SAAS;;;;;;;2CARtC;;;;;;;;;;;;;;;4BAgBd,eAAe,mBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;wCAAC;wCAAG;wCAAM;qCAAE;oCAAE,SAAS;wCAAC;wCAAK;wCAAK;qCAAI;gCAAC;gCACzD,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;;;;;;;;;;;;kCAMlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BACP,SAAS;4BACT,OAAO;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBACf;wBACA,YAAY;4BACV,SAAS;gCAAE,OAAO;gCAAK,UAAU;gCAAK,MAAM;4BAAU;4BACtD,OAAO;gCAAE,OAAO;gCAAK,UAAU;gCAAK,MAAM;4BAAU;4BACpD,GAAG;gCACD,OAAO;gCACP,UAAU;gCACV,QAAQ;gCACR,MAAM;4BACR;wBACF;;0CAEA,8OAAC;gCAAI,WAAU;0CAA+C;;;;;;0CAC9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;oBAK/B,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC;4BAEC,SAAS;4BACT,OAAO;4BACP,YAAY;4BACZ,gBAAgB;4BAChB,SAAS;2BALJ,QAAQ,EAAE;;;;;oBAUlB,cAAc,mBACb,8OAAC;wBAAI,WAAU;kCACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC;;kDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,IAAG;wCACH,IAAG;wCACH,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wCACnB,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wCACnB,QAAO;wCACP,aAAY;wCACZ,iBAAgB;wCAChB,SAAS;wCACT,SAAS;4CAAE,YAAY;wCAAE;wCACzB,SAAS;4CAAE,YAAY;wCAAE;wCACzB,YAAY;4CAAE,OAAO,QAAQ,MAAM;4CAAG,UAAU;wCAAE;;;;;;kDAGpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,GAAE;wCACF,MAAK;wCACL,SAAS;wCACT,SAAS;4CAAE,IAAI;4CAAO,IAAI;wCAAM;wCAChC,SAAS;4CAAE,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;4CAAE,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;wCAAC;wCACpD,YAAY;4CACV,OAAO,QAAQ,MAAM;4CACrB,UAAU;4CACV,QAAQ;4CACR,YAAY;4CACZ,aAAa;wCACf;;;;;;;+BA3BI,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAoC1B,8OAAC;gBAAU,YAAY;gBAAY,OAAO;gBAAO,WAAW;;;;;;;;AAGlE;AAUA,MAAM,gBAA8C,CAAC,EACnD,OAAO,EACP,KAAK,EACL,UAAU,EACV,cAAc,EACd,OAAO,EACR,iBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,oDAAoD,EAC9D,mBAAmB,QAAQ,EAAE,GACzB,gCACA,cAAc,IAAI,cAAc,WACpC;QACF,OAAO;YAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAAE,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAAC;QACrD,SAAS;YAAE,OAAO;YAAG,SAAS;QAAE;QAChC,SAAS;YACP,OAAO,cAAc,IAAI,IAAI;YAC7B,SAAS,cAAc,IAAI,IAAI;QACjC;QACA,YAAY;YAAE,OAAO,QAAQ,MAAM;QAAE;QACrC,cAAc,IAAM,QAAQ,QAAQ,EAAE;QACtC,cAAc,IAAM,QAAQ;;0BAE5B,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,4BAA4B,EAAE,QAAQ,KAAK,GAAG,IAAI,iBAAiB,EAAE,QAAQ,KAAK,GAAG,IAAI,YAAY,CAAC;gBACrH;0BAEA,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,CAAC,IAAI,EAAE,QAAQ,KAAK,GAAG,IAAI,WAAW,CAAC;oBAAC;;;;;;;;;;;0BAKtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAAE,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBAAC;gBACtD,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;oBACf,MAAM;wBAAC;wBAAK;wBAAG;wBAAK;qBAAE;oBACtB,YAAY;gBACd;gBACA,OAAO;oBAAE,aAAa,CAAC,IAAI,EAAE,QAAQ,KAAK,GAAG,IAAI,WAAW,CAAC;gBAAC;;;;;;0BAIhE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,mBAAmB,QAAQ,EAAE,kBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAI;oBACxC,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;wBAAG,OAAO;oBAAE;oBACvC,MAAM;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAI;oBACrC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;oBAC7C,WAAU;oBACV,OAAO;wBACL,UAAU;wBACV,UAAU;wBACV,MAAM,QAAQ,CAAC,GAAG,KAAK,SAAS;wBAChC,OAAO,QAAQ,CAAC,GAAG,KAAK,MAAM;wBAC9B,WAAW,QAAQ,CAAC,GAAG,KAAK,kBAAkB;oBAChD;;sCAEA,8OAAC;4BAAI,WAAU;sCAAqC,QAAQ,IAAI;;;;;;sCAChE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC;gDAAC;gDACtC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;;;;;;;;;;;sDAG5C,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,CAAC,IAAI,EAAE,QAAQ,KAAK,GAAG,IAAI,WAAW,CAAC;4CAAC;;gDAEvD,QAAQ,KAAK;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB/B,MAAM,YAAsC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,iBAC3E,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,OAAO;gBAAK,UAAU;YAAI;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCAEV,SAAS;wCAAE,OAAO;oCAAI;oCACtB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;oCAAI;8CAE5C,aAAa;mCALT;;;;;8CAOP,8OAAC;;sDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CAEV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;4CAAI;sDAE3B,KAAK,CAAC,WAAW;2CALb;;;;;sDAOP,8OAAC;4CAAI,WAAU;;gDAAgC;gDACpC,aAAa;gDAAE;gDAAM,MAAM,MAAM;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAW,CAAC,sEAAsE,EAChF,SAAS,aAAa,eAAe,0BACrC;oCACF,SAAS;wCAAE,OAAO,UAAU,aAAa,MAAM;oCAAE;mCAJ5C;;;;;;;;;;;;;;;;8BAWb,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACpB,cAAA,8OAAC;wBACC,YAAY;wBACZ,WAAW;;;;;;;;;;;;;;;;;;;;;;AAYrB,MAAM,cAA0C,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE;IACxE,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;IAC7B;IAEA,MAAM,gBAAgB;QACpB,8BAA8B;sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,8OAAC;oBAAE,WAAU;8BAAuC;;;;;;8BAEpD,8OAAC;oBAAI,WAAU;8BACZ;wBACC;wBACA;wBACA;qBACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CAAuB;;;;;;;2BAJ9B;;;;;;;;;;;WAjBV;;;;;QA2BN,uBAAuB;sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;;;;;;8CAE9D,8OAAC;oCAAK,WAAU;8CACb,UAAU,MAAM;;;;;;;;;;;;;;;;;;8BAKvB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCAA0C;;;;;;sCACzD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAK;oCACnC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAgC,QAAQ,IAAI;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAmC,QAAQ,IAAI;;;;;;;mCAPzD,QAAQ,EAAE;;;;;;;;;;;;;;;;;WA/BnB;;;;;QA6CN,mBAAmB;sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;gCACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,MAAM;gCAAC;;;;;;;;;;;;;8BAIhD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAO;wBACzB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAE;;;;;;;;;;;8BAI1C,8OAAC;oBAAI,WAAU;8BACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,IAAI,QAAQ;4BAAI;4BACrC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAsC,OAAO,QAAQ,IAAI;kDACrE,QAAQ,IAAI;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC;oDAAC;oDACtC,YAAY;wDAAE,OAAO,MAAM,QAAQ;wDAAK,UAAU;oDAAI;;;;;;;;;;;0DAG1D,8OAAC;gDAAK,WAAU;;oDAA4C,QAAQ,KAAK;oDAAC;;;;;;;;;;;;;kDAE5E,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;;;;;;;2BArB/C,QAAQ,EAAE;;;;;;;;;;;WA7BjB;;;;;QAyDN,uBAAuB;sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;gCACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,MAAM;gCAAC;;;;;;;;;;;;;8BAIhD,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAK;oCAAK;iCAAI;gCACtB,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BACxB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,OAAO,IAAI;gCACX,YAAY;4BACd;2BAXK;;;;;;;;;;8BAgBX,8OAAC;oBAAI,WAAU;8BACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,sBACjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;4BACjC,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAuB,QAAQ,IAAI;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAoC,QAAQ,KAAK;gDAAC;;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;2BAZ5C,QAAQ,EAAE;;;;;;;;;;;WAvCjB;;;;;KAyDP;IAED,OAAO,aAAa,CAAC,WAAW,IAAI;AACtC", "debugId": null}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { HeroContent } from './HeroContent';\r\nimport { InteractiveMapDemo } from './InteractiveMapDemo';\r\n\r\ninterface HeroSectionProps {\r\n  onGetStarted: () => void;\r\n}\r\n\r\nexport const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {\r\n  return (\r\n    <section className=\"relative py-6 sm:py-8 md:py-10 lg:py-16 xl:py-20 overflow-hidden\">\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <div className=\"grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center\">\r\n          {/* Left: Content */}\r\n          <HeroContent onGetStarted={onGetStarted} />\r\n          \r\n          {/* Right: Interactive Demo */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50, rotateY: 15 }}\r\n            animate={{ opacity: 1, x: 0, rotateY: 0 }}\r\n            transition={{ delay: 0.6, duration: 1 }}\r\n            className=\"relative order-first lg:order-last\"\r\n          >\r\n            <InteractiveMapDemo />\r\n            \r\n            {/* Floating Stats */}\r\n            <motion.div\r\n              className=\"absolute -top-2 -right-2 sm:-top-4 sm:-right-4 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg\"\r\n              animate={{ y: [0, -10, 0] }}\r\n              transition={{ duration: 4, repeat: Infinity }}\r\n            >\r\n              <div className=\"text-lg sm:text-2xl font-bold text-primary\">94%</div>\r\n              <div className=\"text-xs text-muted-foreground\">Erfolgsrate</div>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWO,MAAM,cAA0C,CAAC,EAAE,YAAY,EAAE;IACtE,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,sKAAA,CAAA,cAAW;wBAAC,cAAc;;;;;;kCAG3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;4BAAI,SAAS;wBAAG;wBAC1C,SAAS;4BAAE,SAAS;4BAAG,GAAG;4BAAG,SAAS;wBAAE;wBACxC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAE;wBACtC,WAAU;;0CAEV,8OAAC,6KAAA,CAAA,qBAAkB;;;;;0CAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;gCAAC;gCAC1B,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;;kDAE5C,8OAAC;wCAAI,WAAU;kDAA6C;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/MorphingFeatureCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { LucideIcon } from 'lucide-react';\r\nimport { useResponsiveCheck } from '../../hooks';\r\n\r\ninterface MorphingFeatureCardProps {\r\n  icon: LucideIcon;\r\n  title: string;\r\n  description: string;\r\n  details: string[];\r\n  delay: number;\r\n  accentColor: string;\r\n}\r\n\r\nexport const MorphingFeatureCard: React.FC<MorphingFeatureCardProps> = ({ \r\n  icon: Icon, \r\n  title, \r\n  description, \r\n  details, \r\n  delay, \r\n  accentColor \r\n}) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const { isTouchDevice } = useResponsiveCheck();\r\n\r\n  const handleClick = () => {\r\n    if (isTouchDevice) {\r\n      setIsExpanded(!isExpanded);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 50, rotateY: -15 }}\r\n      whileInView={{ opacity: 1, y: 0, rotateY: 0 }}\r\n      viewport={{ once: true, amount: 0.3 }}\r\n      transition={{ duration: 0.8, delay, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n      className=\"group relative overflow-hidden\"\r\n      onMouseEnter={() => !isTouchDevice && setIsExpanded(true)}\r\n      onMouseLeave={() => !isTouchDevice && setIsExpanded(false)}\r\n      onClick={handleClick}\r\n    >\r\n      <div className=\"relative bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60 h-full min-h-[320px] sm:min-h-[350px] md:min-h-[380px] flex flex-col\">\r\n        {/* Accent Border */}\r\n        <div \r\n          className=\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl sm:rounded-2xl\"\r\n          style={{ background: `linear-gradient(135deg, ${accentColor}20, transparent)` }}\r\n        />\r\n        \r\n        <div className=\"relative p-3 sm:p-4 md:p-6 lg:p-8 h-full flex flex-col overflow-hidden\">\r\n          {/* Icon */}\r\n          <motion.div \r\n            className=\"w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-lg sm:rounded-xl md:rounded-2xl flex items-center justify-center mb-3 sm:mb-4 md:mb-6 relative overflow-hidden flex-shrink-0\"\r\n            style={{ backgroundColor: `${accentColor}15` }}\r\n            whileHover={{ scale: 1.1, rotate: 5 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n          >\r\n            <Icon \r\n              className=\"w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 transition-colors duration-300\" \r\n              style={{ color: accentColor }}\r\n            />\r\n            <motion.div\r\n              className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent\"\r\n              animate={{ x: ['-100%', '200%'] }}\r\n              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\r\n            />\r\n          </motion.div>\r\n\r\n          {/* Title */}\r\n          <h3 className=\"text-base sm:text-lg md:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 md:mb-4 text-foreground group-hover:text-opacity-90 transition-colors flex-shrink-0\">\r\n            {title}\r\n          </h3>\r\n          \r\n          {/* Description */}\r\n          <p className=\"text-sm sm:text-base text-muted-foreground mb-3 sm:mb-4 md:mb-6 leading-relaxed flex-shrink-0\">\r\n            {description}\r\n          </p>\r\n\r\n          {/* Expandable Details */}\r\n          <div className=\"flex-1 min-h-0\">\r\n            <motion.div\r\n              initial={false}\r\n              animate={{ \r\n                height: isExpanded ? 'auto' : 0, \r\n                opacity: isExpanded ? 1 : 0 \r\n              }}\r\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n              className=\"overflow-hidden\"\r\n            >\r\n              <div className=\"space-y-1.5 sm:space-y-2 md:space-y-3 pt-2 border-t border-border/20\">\r\n                {details.map((detail, i) => (\r\n                  <motion.div\r\n                    key={i}\r\n                    initial={{ opacity: 0, x: -20 }}\r\n                    animate={{ opacity: isExpanded ? 1 : 0, x: isExpanded ? 0 : -20 }}\r\n                    transition={{ delay: i * 0.1 }}\r\n                    className=\"flex items-start space-x-2 sm:space-x-3\"\r\n                  >\r\n                    <div \r\n                      className=\"w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full mt-1.5 sm:mt-2 flex-shrink-0\"\r\n                      style={{ backgroundColor: accentColor }}\r\n                    />\r\n                    <span className=\"text-xs sm:text-sm text-muted-foreground leading-relaxed\">\r\n                      {detail}\r\n                    </span>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Hover Indicator */}\r\n          <motion.div \r\n            className=\"pt-2 sm:pt-3 md:pt-4 text-xs font-medium opacity-0 group-hover:opacity-60 transition-opacity flex-shrink-0\"\r\n            style={{ color: accentColor }}\r\n          >\r\n            <span className=\"hidden sm:inline\">Mehr Details anzeigen</span>\r\n            <span className=\"sm:hidden\">Details anzeigen</span>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAgBO,MAAM,sBAA0D,CAAC,EACtE,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EACX,OAAO,EACP,KAAK,EACL,WAAW,EACZ;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,qBAAkB,AAAD;IAE3C,MAAM,cAAc;QAClB,IAAI,eAAe;YACjB,cAAc,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,SAAS,CAAC;QAAG;QAC3C,aAAa;YAAE,SAAS;YAAG,GAAG;YAAG,SAAS;QAAE;QAC5C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;gBAAC;gBAAM,CAAC;gBAAM;gBAAO;aAAK;QAAC;QACrE,WAAU;QACV,cAAc,IAAM,CAAC,iBAAiB,cAAc;QACpD,cAAc,IAAM,CAAC,iBAAiB,cAAc;QACpD,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,YAAY,CAAC,wBAAwB,EAAE,YAAY,gBAAgB,CAAC;oBAAC;;;;;;8BAGhF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,iBAAiB,GAAG,YAAY,EAAE,CAAC;4BAAC;4BAC7C,YAAY;gCAAE,OAAO;gCAAK,QAAQ;4BAAE;4BACpC,YAAY;gCAAE,MAAM;gCAAU,WAAW;4BAAI;;8CAE7C,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO;oCAAY;;;;;;8CAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;4CAAC;4CAAS;yCAAO;oCAAC;oCAChC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,aAAa;oCAAE;;;;;;;;;;;;sCAKhE,8OAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAIH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;gCACT,SAAS;oCACP,QAAQ,aAAa,SAAS;oCAC9B,SAAS,aAAa,IAAI;gCAC5B;gCACA,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;gCAC/C,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,kBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS,aAAa,IAAI;gDAAG,GAAG,aAAa,IAAI,CAAC;4CAAG;4CAChE,YAAY;gDAAE,OAAO,IAAI;4CAAI;4CAC7B,WAAU;;8DAEV,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAY;;;;;;8DAExC,8OAAC;oDAAK,WAAU;8DACb;;;;;;;2CAXE;;;;;;;;;;;;;;;;;;;;sCAoBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAY;;8CAE5B,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,8OAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 2710, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Eye } from 'lucide-react';\r\nimport { SectionHeader } from '../shared';\r\nimport { MorphingFeatureCard } from './MorphingFeatureCard';\r\nimport { UNIQUE_FEATURES } from '../../utils';\r\n\r\nexport const FeaturesSection: React.FC = () => {\r\n  return (\r\n    <section id=\"features\" className=\"py-8 sm:py-12 md:py-16 lg:py-20 xl:py-32 relative overflow-hidden\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <SectionHeader\r\n          badge={{\r\n            icon: Eye,\r\n            text: \"Einzigartige Technologie\"\r\n          }}\r\n          title={\r\n            <>\r\n              Warum OpuMap anders ist als <br className=\"hidden sm:block\" />\r\n              <span className=\"text-primary\">alles andere auf dem Markt</span>\r\n            </>\r\n          }\r\n          description=\"Während andere Tools oberflächliche Kontaktlisten bieten, schafft OpuMap echte strategische Intelligenz. Unsere KI versteht Ihr Geschäftsmodell und findet nicht nur Kontakte, sondern echte Geschäftschancen.\"\r\n        />\r\n        \r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto\">\r\n          {UNIQUE_FEATURES.map((feature, index) => (\r\n            <MorphingFeatureCard\r\n              key={index}\r\n              icon={feature.icon}\r\n              title={feature.title}\r\n              description={feature.description}\r\n              details={feature.details}\r\n              delay={feature.delay}\r\n              accentColor={feature.accentColor}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AAAA;AANA;;;;;;AAQO,MAAM,kBAA4B;IACvC,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mKAAA,CAAA,gBAAa;oBACZ,OAAO;wBACL,MAAM,gMAAA,CAAA,MAAG;wBACT,MAAM;oBACR;oBACA,qBACE;;4BAAE;0CAC4B,8OAAC;gCAAG,WAAU;;;;;;0CAC1C,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;oBAGnC,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;8BACZ,+IAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,kLAAA,CAAA,sBAAmB;4BAElB,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;4BAChC,SAAS,QAAQ,OAAO;4BACxB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;2BAN3B;;;;;;;;;;;;;;;;;;;;;AAanB", "debugId": null}}, {"offset": {"line": 2801, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/ProcessStep.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { LucideIcon } from 'lucide-react';\r\n\r\ninterface ProcessStepProps {\r\n  step: string;\r\n  icon: LucideIcon;\r\n  title: string;\r\n  description: string;\r\n  index: number;\r\n}\r\n\r\nexport const ProcessStep: React.FC<ProcessStepProps> = ({\r\n  step,\r\n  icon: Icon,\r\n  title,\r\n  description,\r\n  index\r\n}) => {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 50, scale: 0.9 }}\r\n      whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n      viewport={{ once: true, amount: 0.3 }}\r\n      transition={{ duration: 0.6, delay: index * 0.2 }}\r\n      className=\"relative text-center group\"\r\n    >\r\n      {/* Step Circle */}\r\n      <div className=\"relative mx-auto w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center mb-4 sm:mb-6 shadow-lg group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-300 transform group-hover:scale-110\">\r\n        <div className=\"text-white font-bold text-base sm:text-lg\">{step}</div>\r\n        <motion.div\r\n          className=\"absolute inset-0 rounded-full border-2 border-primary/30\"\r\n          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}\r\n          transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}\r\n        />\r\n      </div>\r\n      \r\n      {/* Icon */}\r\n      <div className=\"w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 text-primary\">\r\n        <Icon className=\"w-full h-full\" />\r\n      </div>\r\n      \r\n      {/* Content */}\r\n      <h3 className=\"text-lg sm:text-xl font-bold mb-2 sm:mb-3\">{title}</h3>\r\n      <p className=\"text-muted-foreground text-sm leading-relaxed\">{description}</p>\r\n    </motion.div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAcO,MAAM,cAA0C,CAAC,EACtD,IAAI,EACJ,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EACX,KAAK,EACN;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACzC,aAAa;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QAC1C,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA6C;;;;;;kCAC5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAAE,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBAAC;wBACtD,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,OAAO,QAAQ;wBAAI;;;;;;;;;;;;0BAKpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAIlB,8OAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAC3D,8OAAC;gBAAE,WAAU;0BAAiD;;;;;;;;;;;;AAGpE", "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { SectionHeader } from '../shared';\r\nimport { ProcessStep } from './ProcessStep';\r\nimport { PROCESS_STEPS } from '../../utils';\r\n\r\nexport const ProcessSection: React.FC = () => {\r\n  return (\r\n    <section className=\"py-12 sm:py-16 md:py-20 lg:py-32 relative\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <SectionHeader\r\n          title={\r\n            <>\r\n              Der OpuMap-Prozess: <span className=\"text-primary\">Von der Strategie zur Partnerschaft</span>\r\n            </>\r\n          }\r\n          maxWidth=\"max-w-3xl\"\r\n        />\r\n        \r\n        <div className=\"relative max-w-6xl mx-auto\">\r\n          {/* Connection Line */}\r\n          <div className=\"absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 transform -translate-y-1/2 hidden lg:block\"></div>\r\n          \r\n          <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\">\r\n            {PROCESS_STEPS.map((step, index) => (\r\n              <ProcessStep\r\n                key={index}\r\n                step={step.step}\r\n                icon={step.icon}\r\n                title={step.title}\r\n                description={step.description}\r\n                index={index}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAAA;AALA;;;;;AAOO,MAAM,iBAA2B;IACtC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mKAAA,CAAA,gBAAa;oBACZ,qBACE;;4BAAE;0CACoB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;oBAGvD,UAAS;;;;;;8BAGX,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACZ,+IAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,yKAAA,CAAA,cAAW;oCAEV,MAAM,KAAK,IAAI;oCACf,MAAM,KAAK,IAAI;oCACf,OAAO,KAAK,KAAK;oCACjB,aAAa,KAAK,WAAW;oCAC7B,OAAO;mCALF;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarB", "debugId": null}}, {"offset": {"line": 3007, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/CTASection/index.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Route, Check, ArrowRight } from 'lucide-react';\r\nimport { AnimatedButton } from '../shared';\r\nimport { CTA_FEATURES } from '../../utils';\r\n\r\ninterface CTASectionProps {\r\n  onGetStarted: () => void;\r\n}\r\n\r\nexport const CTASection: React.FC<CTASectionProps> = ({ onGetStarted }) => {\r\n  return (\r\n    <section className=\"py-12 sm:py-16 md:py-20 lg:py-32 relative\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10\"></div>\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30, scale: 0.95 }}\r\n          whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n          viewport={{ once: true }}\r\n          transition={{ duration: 0.8 }}\r\n          className=\"max-w-4xl mx-auto text-center bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl p-8 sm:p-12 md:p-16 rounded-2xl sm:rounded-3xl border border-border/30 shadow-2xl\"\r\n        >\r\n          {/* Icon */}\r\n          <motion.div\r\n            initial={{ scale: 0 }}\r\n            whileInView={{ scale: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ delay: 0.3, type: \"spring\", stiffness: 200 }}\r\n            className=\"w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-6 sm:mb-8 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center\"\r\n          >\r\n            <Route className=\"w-8 h-8 sm:w-10 sm:h-10 text-white\" />\r\n          </motion.div>\r\n          \r\n          {/* Heading */}\r\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6\">\r\n            Revolutionieren Sie Ihre <span className=\"text-primary\">Geschäftsentwicklung</span>\r\n          </h2>\r\n          \r\n          {/* Description */}\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-muted-foreground mb-8 sm:mb-10 max-w-2xl mx-auto leading-relaxed\">\r\n            Schließen Sie sich den Unternehmen an, die bereits mit OpuMap ihre lokalen \r\n            Geschäftspotenziale systematisch erschließen und nachhaltige Partnerschaften aufbauen.\r\n          </p>\r\n          \r\n          {/* CTA Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center\">\r\n            <AnimatedButton\r\n              onClick={onGetStarted}\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              className=\"w-full sm:w-auto group relative overflow-hidden\"\r\n            >\r\n              <span className=\"relative z-10 flex items-center justify-center gap-2 sm:gap-3\">\r\n                <span className=\"hidden sm:inline\">Jetzt starten - kostenfrei</span>\r\n                <span className=\"sm:hidden\">Jetzt starten</span>\r\n                <ArrowRight className=\"w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform\" />\r\n              </span>\r\n              <motion.div\r\n                className=\"absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary opacity-0 group-hover:opacity-100\"\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            </AnimatedButton>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"text-sm text-muted-foreground mb-1\">Oder erst mal</div>\r\n              <button className=\"text-primary font-semibold hover:underline text-sm sm:text-base\">\r\n                Live Demo ansehen →\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Feature List */}\r\n          <div className=\"flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-8 mt-8 sm:mt-10 text-xs sm:text-sm text-muted-foreground\">\r\n            {CTA_FEATURES.map((feature, index) => (\r\n              <div key={index} className=\"flex items-center justify-center space-x-2\">\r\n                <Check className=\"w-3 h-3 sm:w-4 sm:h-4 text-green-500\" />\r\n                <span>{feature}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AANA;;;;;;AAYO,MAAM,aAAwC,CAAC,EAAE,YAAY,EAAE;IACpE,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAK;oBAC1C,aAAa;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBAC1C,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,aAAa;gCAAE,OAAO;4BAAE;4BACxB,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;gCAAK,MAAM;gCAAU,WAAW;4BAAI;4BACzD,WAAU;sCAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAInB,8OAAC;4BAAG,WAAU;;gCAAsE;8CACzD,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAI1D,8OAAC;4BAAE,WAAU;sCAAwG;;;;;;sCAMrH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oKAAA,CAAA,iBAAc;oCACb,SAAS;oCACT,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAAY;;;;;;8DAC5B,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAO,WAAU;sDAAkE;;;;;;;;;;;;;;;;;;sCAOxF,8OAAC;4BAAI,WAAU;sCACZ,+IAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM;;;;;;;mCAFC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}, {"offset": {"line": 3253, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPageRefactored.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { FloatingBackground } from './components/shared';\r\nimport { HeroSection } from './components/HeroSection';\r\nimport { FeaturesSection } from './components/FeaturesSection';\r\nimport { ProcessSection } from './components/ProcessSection';\r\nimport { CTASection } from './components/CTASection';\r\n\r\nconst LandingPageRefactored: React.FC = () => {\r\n  const router = useRouter();\r\n\r\n  const handleGetStarted = () => {\r\n    router.push('/login');\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen relative overflow-hidden\">\r\n      <FloatingBackground />\r\n      \r\n      <HeroSection onGetStarted={handleGetStarted} />\r\n      \r\n      <FeaturesSection />\r\n      \r\n      <ProcessSection />\r\n      \r\n      <CTASection onGetStarted={handleGetStarted} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LandingPageRefactored;"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,MAAM,wBAAkC;IACtC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yNAAA,CAAA,qBAAkB;;;;;0BAEnB,8OAAC,gKAAA,CAAA,cAAW;gBAAC,cAAc;;;;;;0BAE3B,8OAAC,oKAAA,CAAA,kBAAe;;;;;0BAEhB,8OAAC,mKAAA,CAAA,iBAAc;;;;;0BAEf,8OAAC,+JAAA,CAAA,aAAU;gBAAC,cAAc;;;;;;;;;;;;AAGhC;uCAEe", "debugId": null}}, {"offset": {"line": 3323, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPage.tsx"], "sourcesContent": ["export { default } from './LandingPageRefactored';"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3341, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/index.tsx"], "sourcesContent": ["export { default } from './LandingPage';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3359, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/supabaseClient.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createClient } from '@/utils/supabase/client';\r\n\r\n/**\r\n * Custom fetch wrapper that adds Supabase Auth token to requests\r\n * @param url The URL to fetch\r\n * @param options Fetch options\r\n * @returns The fetch response\r\n */\r\nexport async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {\r\n  try {\r\n    // Einfach die Anfrage ohne zusätzliche Header senden, da die Cookies automatisch mitgesendet werden\r\n    const response = await fetch(url, {\r\n      ...options,\r\n      credentials: 'include', // Wichtig: Cookies immer mitsenden\r\n    });\r\n\r\n    // Handle 401 Unauthorized errors\r\n    if (response.status === 401) {\r\n      console.warn('Received 401 Unauthorized response from API');\r\n\r\n      // Überprüfen, ob wir noch eine gültige Session haben\r\n      const supabase = createClient();\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n\r\n      if (!session) {\r\n        console.log('No active session found, redirecting to login');\r\n        // Nur umleiten, wenn wir im Browser sind\r\n        if (typeof window !== 'undefined') {\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n\r\n    return response;\r\n  } catch (error) {\r\n    console.error('API request failed:', error);\r\n    throw error;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACxE,IAAI;QACF,oGAAoG;QACpG,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,aAAa;QACf;QAEA,iCAAiC;QACjC,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,IAAI,CAAC;YAEb,qDAAqD;YACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAE5D,IAAI,CAAC,SAAS;gBACZ,QAAQ,GAAG,CAAC;gBACZ,yCAAyC;gBACzC,uCAAmC;;gBAEnC;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 3398, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/mapStyles.ts"], "sourcesContent": ["import type { Libraries } from '@react-google-maps/api';\r\nimport { DemoMarker } from \"@/types\";\r\n\r\n// Google Maps dark mode style\r\nexport const darkMapStyle: google.maps.MapTypeStyle[] = [\r\n  { elementType: \"geometry\", stylers: [{ color: \"#242f3e\" }] },\r\n  { elementType: \"labels.text.stroke\", stylers: [{ color: \"#242f3e\" }] },\r\n  { elementType: \"labels.text.fill\", stylers: [{ color: \"#746855\" }] },\r\n  {\r\n    featureType: \"administrative.locality\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#d59563\" }]\r\n  },\r\n  {\r\n    featureType: \"poi\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#d59563\" }]\r\n  },\r\n  {\r\n    featureType: \"poi.park\",\r\n    elementType: \"geometry\",\r\n    stylers: [{ color: \"#263c3f\" }]\r\n  },\r\n  {\r\n    featureType: \"poi.park\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#6b9a76\" }]\r\n  },\r\n  {\r\n    featureType: \"road\",\r\n    elementType: \"geometry\",\r\n    stylers: [{ color: \"#38414e\" }]\r\n  },\r\n  {\r\n    featureType: \"road\",\r\n    elementType: \"geometry.stroke\",\r\n    stylers: [{ color: \"#212a37\" }]\r\n  },\r\n  {\r\n    featureType: \"road\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#9ca5b3\" }]\r\n  },\r\n  {\r\n    featureType: \"road.highway\",\r\n    elementType: \"geometry\",\r\n    stylers: [{ color: \"#746855\" }]\r\n  },\r\n  {\r\n    featureType: \"road.highway\",\r\n    elementType: \"geometry.stroke\",\r\n    stylers: [{ color: \"#1f2835\" }]\r\n  },\r\n  {\r\n    featureType: \"road.highway\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#f3d19c\" }]\r\n  },\r\n  {\r\n    featureType: \"transit\",\r\n    elementType: \"geometry\",\r\n    stylers: [{ color: \"#2f3948\" }]\r\n  },\r\n  {\r\n    featureType: \"transit.station\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#d59563\" }]\r\n  },\r\n  {\r\n    featureType: \"water\",\r\n    elementType: \"geometry\",\r\n    stylers: [{ color: \"#17263c\" }]\r\n  },\r\n  {\r\n    featureType: \"water\",\r\n    elementType: \"labels.text.fill\",\r\n    stylers: [{ color: \"#515c6d\" }]\r\n  },\r\n  {\r\n    featureType: \"water\",\r\n    elementType: \"labels.text.stroke\",\r\n    stylers: [{ color: \"#17263c\" }]\r\n  }\r\n];\r\n\r\n// Default map settings\r\nexport const mapDefaults = {\r\n  center: {\r\n    lat: 52.2799, // Osnabrück center\r\n    lng: 8.0472,\r\n  },\r\n  zoom: 10,\r\n  libraries: [\"places\"] as Libraries,\r\n  storageKey: 'opumap_map_state'\r\n};\r\n\r\n// Map options\r\nexport const mapOptions = (isDarkMode: boolean) => ({\r\n  disableDefaultUI: true, // Cleaner map interface\r\n  clickableIcons: true, // Allow clicking POIs\r\n  scrollwheel: true,\r\n  gestureHandling: \"greedy\", // Allow map interaction without modifier keys\r\n  styles: isDarkMode ? darkMapStyle : [],\r\n});\r\n\r\n// Layout styles for map section\r\nexport const mapLayoutStyles = {\r\n  container: 'map-container flex flex-col bg-background text-foreground',\r\n  mapSection: 'map-section w-full',\r\n  mapContainer: 'w-full h-[400px] border border-border rounded-lg',\r\n  contentSection: 'flex flex-col md:flex-row flex-wrap justify-center items-center md:items-start gap-4 md:gap-8 p-4 md:p-8',\r\n};\r\n\r\n// Marker options\r\nexport const markerStyles = {\r\n  selectedMarkerOptions: {\r\n    icon: {\r\n      url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'\r\n    }\r\n  }\r\n};\r\n\r\n// Error and loading states\r\nexport const statusStyles = {\r\n  errorMessage: 'text-red-500 text-center p-4',\r\n  loadingMessage: 'text-center p-4',\r\n};\r\n\r\n// Demo markers (replace with actual data fetching if needed)\r\nexport const DEMO_MARKERS: DemoMarker[] = [\r\n  {\r\n    id: \"1\", // Changed to string\r\n    place_id: \"demo_1\",\r\n    position: { lat: 52.5200, lng: 13.4050 },\r\n    name: \"Beispiel Unternehmen 1\",\r\n    formatted_address: \"Musterstraße 123, Berlin\",\r\n    formatted_phone_number: \"+49 30 123456\",\r\n    website: \"www.beispiel1.de\",\r\n    description: \"Dies ist ein Beispiel-Unternehmen in Berlin.\",\r\n  },\r\n  {\r\n    id: \"2\", // Changed to string\r\n    place_id: \"demo_2\",\r\n    position: { lat: 52.5300, lng: 13.4150 },\r\n    name: \"Beispiel Unternehmen 2\",\r\n    formatted_address: \"Testweg 45, Berlin\",\r\n    formatted_phone_number: \"+49 30 654321\",\r\n    website: \"www.beispiel2.de\",\r\n    description: \"Ein weiteres Beispiel-Unternehmen in Berlin.\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;;AAIO,MAAM,eAA2C;IACtD;QAAE,aAAa;QAAY,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IAAC;IAC3D;QAAE,aAAa;QAAsB,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IAAC;IACrE;QAAE,aAAa;QAAoB,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IAAC;IACnE;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;IACA;QACE,aAAa;QACb,aAAa;QACb,SAAS;YAAC;gBAAE,OAAO;YAAU;SAAE;IACjC;CACD;AAGM,MAAM,cAAc;IACzB,QAAQ;QACN,KAAK;QACL,KAAK;IACP;IACA,MAAM;IACN,WAAW;QAAC;KAAS;IACrB,YAAY;AACd;AAGO,MAAM,aAAa,CAAC,aAAwB,CAAC;QAClD,kBAAkB;QAClB,gBAAgB;QAChB,aAAa;QACb,iBAAiB;QACjB,QAAQ,aAAa,eAAe,EAAE;IACxC,CAAC;AAGM,MAAM,kBAAkB;IAC7B,WAAW;IACX,YAAY;IACZ,cAAc;IACd,gBAAgB;AAClB;AAGO,MAAM,eAAe;IAC1B,uBAAuB;QACrB,MAAM;YACJ,KAAK;QACP;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,cAAc;IACd,gBAAgB;AAClB;AAGO,MAAM,eAA6B;IACxC;QACE,IAAI;QACJ,UAAU;QACV,UAAU;YAAE,KAAK;YAAS,KAAK;QAAQ;QACvC,MAAM;QACN,mBAAmB;QACnB,wBAAwB;QACxB,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;YAAE,KAAK;YAAS,KAAK;QAAQ;QACvC,MAAM;QACN,mBAAmB;QACnB,wBAAwB;QACxB,SAAS;QACT,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 3637, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/businessCardStyles.ts"], "sourcesContent": ["import React from 'react';\n\n// Flip card styles\nexport const flipCardStyles: { [key: string]: React.CSSProperties } = {\n  container: {\n    width: '100%', // Full width for mobile\n    maxWidth: '100%', // Ensure it doesn't overflow on small screens\n    height: '350px', // Default height for desktop view\n    perspective: '1000px',\n    marginTop: '0px',\n    marginLeft: '0px', // No margin on mobile\n    marginRight: '0px', // No margin on mobile\n    position: 'relative',\n    // Media query handled in the component with Tailwind classes\n  },\n  inner: {\n    position: 'relative',\n    width: '100%',\n    height: '100%',\n    textAlign: 'center',\n    transition: 'transform 0.8s',\n    transformStyle: 'preserve-3d',\n    cursor: 'pointer',\n    transformOrigin: 'center center'\n  },\n  front: {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    backfaceVisibility: 'hidden',\n    backgroundColor: 'var(--color-card)',\n    color: 'var(--color-card-foreground)',\n    padding: '20px',\n    borderRadius: '12px',\n    boxSizing: 'border-box',\n    boxShadow: '0 6px 12px rgba(0,0,0,0.3)',\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start', // Changed from space-between to allow more content\n    overflow: 'auto', // Changed from hidden to auto to allow scrolling if needed\n    border: '1px solid var(--color-border)'\n  },\n  back: {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    backfaceVisibility: 'hidden',\n    backgroundColor: 'var(--color-card)',\n    color: 'var(--color-card-foreground)',\n    transform: 'rotateY(180deg)',\n    padding: '20px',\n    borderRadius: '12px',\n    boxSizing: 'border-box',\n    boxShadow: '0 6px 12px rgba(0,0,0,0.3)',\n    overflowY: 'auto',\n    textAlign: 'left',\n    border: '1px solid var(--color-border)'\n  },\n  flipped: {\n    transform: 'rotateY(180deg)'\n  },\n  title: {\n    borderBottom: '2px solid var(--color-border)',\n    paddingBottom: '8px',\n    marginBottom: '10px',\n    fontSize: '1.4rem',\n    fontWeight: '600',\n    textAlign: 'center',\n    color: 'var(--color-card-foreground)'\n  },\n  imageContainer: {\n    display: 'flex',\n    justifyContent: 'center',\n    margin: '10px 0'\n  },\n  image: {\n    width: '120px',\n    height: '120px',\n    objectFit: 'cover',\n    borderRadius: '8px',\n    boxShadow: '0 4px 8px rgba(0,0,0,0.3)',\n    border: '1px solid var(--color-border)'\n  },\n  address: {\n    marginTop: '10px',\n    fontSize: '0.95rem',\n    color: 'var(--color-muted-foreground)',\n    textAlign: 'center'\n  },\n  hint: {\n    fontSize: '11px',\n    marginTop: 'auto',\n    paddingTop: '10px',\n    color: 'var(--color-muted-foreground)',\n    fontStyle: 'italic',\n    textAlign: 'center'\n  },\n  sectionTitle: {\n    marginTop: '12px',\n    marginBottom: '8px',\n    fontSize: '1.1rem',\n    borderBottom: '1px solid var(--color-border)',\n    paddingBottom: '4px',\n    color: 'var(--color-card-foreground)',\n    fontWeight: '500'\n  },\n  infoList: {\n    listStyle: 'none',\n    padding: '0',\n    margin: '8px 0'\n  },\n  infoItem: {\n    padding: '3px 0',\n    borderBottom: '1px dotted var(--color-border)',\n    fontSize: '0.85rem',\n    color: 'var(--color-card-foreground)',\n    lineHeight: '1.4'\n  }\n};\n\n// Placeholder card when no business is selected\nexport const placeholderCardStyles = {\n  container: 'bg-card p-6 rounded-lg shadow border border-border text-center h-[450px] sm:h-[500px] md:h-[350px] w-full flex flex-col justify-center items-center',\n  icon: 'w-16 h-16 text-primary mb-4',\n  text: 'text-muted-foreground'\n};\n\n// Additional styles for business card\nexport const businessCardAdditionalStyles = {\n  flipCardContainer: 'w-full md:mx-4 lg:mx-5 md:w-[450px] lg:w-[450px] h-[450px] sm:h-[500px] md:h-[350px]',\n  scrollArea: 'pr-4',\n  websiteLink: {\n    color: 'hsl(var(--primary))',\n    textDecoration: 'underline'\n  },\n  businessCardContainer: 'w-full flex justify-center md:justify-start md:w-1/3 lg:w-1/4',\n};\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,iBAAyD;IACpE,WAAW;QACT,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,WAAW;QACX,YAAY;QACZ,aAAa;QACb,UAAU;IAEZ;IACA,OAAO;QACL,UAAU;QACV,OAAO;QACP,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,iBAAiB;IACnB;IACA,OAAO;QACL,UAAU;QACV,OAAO;QACP,QAAQ;QACR,oBAAoB;QACpB,iBAAiB;QACjB,OAAO;QACP,SAAS;QACT,cAAc;QACd,WAAW;QACX,WAAW;QACX,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,UAAU;QACV,QAAQ;IACV;IACA,MAAM;QACJ,UAAU;QACV,OAAO;QACP,QAAQ;QACR,oBAAoB;QACpB,iBAAiB;QACjB,OAAO;QACP,WAAW;QACX,SAAS;QACT,cAAc;QACd,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,SAAS;QACP,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,cAAc;QACd,UAAU;QACV,YAAY;QACZ,WAAW;QACX,OAAO;IACT;IACA,gBAAgB;QACd,SAAS;QACT,gBAAgB;QAChB,QAAQ;IACV;IACA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX,cAAc;QACd,WAAW;QACX,QAAQ;IACV;IACA,SAAS;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,WAAW;IACb;IACA,MAAM;QACJ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA,cAAc;QACZ,WAAW;QACX,cAAc;QACd,UAAU;QACV,cAAc;QACd,eAAe;QACf,OAAO;QACP,YAAY;IACd;IACA,UAAU;QACR,WAAW;QACX,SAAS;QACT,QAAQ;IACV;IACA,UAAU;QACR,SAAS;QACT,cAAc;QACd,UAAU;QACV,OAAO;QACP,YAAY;IACd;AACF;AAGO,MAAM,wBAAwB;IACnC,WAAW;IACX,MAAM;IACN,MAAM;AACR;AAGO,MAAM,+BAA+B;IAC1C,mBAAmB;IACnB,YAAY;IACZ,aAAa;QACX,OAAO;QACP,gBAAgB;IAClB;IACA,uBAAuB;AACzB", "debugId": null}}, {"offset": {"line": 3777, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/analysisStyles.ts"], "sourcesContent": ["// Analysis card styles\nexport const analysisCardStyles = {\n  container: 'bg-card p-4 rounded-lg shadow border border-border w-full md:w-64',\n  header: 'flex items-center justify-between mb-4',\n  title: 'text-lg font-semibold text-card-foreground',\n  newButton: (disabled: boolean) => `bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded flex items-center space-x-1 transition ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n  dateSection: 'mb-4',\n  dateLabel: 'text-xs text-muted-foreground',\n  dateValue: 'text-sm font-medium text-card-foreground',\n  viewButton: 'w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm rounded disabled:opacity-50 disabled:cursor-not-allowed transition'\n};\n\n// Analysis modal styles\nexport const modalStyles = {\n  backdrop: 'fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4',\n  container: 'bg-card rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col',\n  header: 'flex justify-between items-center p-4 border-b border-border',\n  title: 'text-xl font-semibold text-card-foreground',\n  closeButton: 'text-muted-foreground hover:text-primary',\n  body: 'p-6 overflow-y-auto flex-grow',\n  dateInfo: 'mb-4 text-sm text-muted-foreground',\n  content: 'prose prose-sm dark:prose-invert max-w-none p-4 rounded bg-background [&>strong]:font-bold [&>strong]:text-black dark:[&>strong]:text-white [&>h1]:text-3xl [&>h1]:font-bold [&>h1]:mb-6 [&>h2]:text-2xl [&>h2]:font-semibold [&>h2]:mb-4 [&>h3]:text-xl [&>h3]:mb-3 [&>p]:text-base [&>p]:leading-relaxed [&>ul]:list-disc [&>ul]:ml-6 [&>ul]:mb-4 [&>ol]:list-decimal [&>ol]:ml-6 [&>ol]:mb-4',\n  footer: 'flex justify-end p-4 border-t border-border bg-muted/50 rounded-b-lg',\n  closeButtonLarge: 'bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out'\n};\n\n// Layout styles for analysis section\nexport const analysisLayoutStyles = {\n  analysisContainer: 'flex flex-col w-full md:w-auto mt-4 md:mt-0',\n  analysisCardsWrapper: 'flex flex-col md:flex-row gap-4 w-full'\n};\n\n// Additional styles for analysis components\nexport const analysisAdditionalStyles = {\n  arrowSpan: 'ml-1',\n  modalCloseIcon: 'w-6 h-6',\n  indentedParagraph: 'ml-4'\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;AAChB,MAAM,qBAAqB;IAChC,WAAW;IACX,QAAQ;IACR,OAAO;IACP,WAAW,CAAC,WAAsB,CAAC,8JAA8J,EAAE,WAAW,kCAAkC,IAAI;IACpP,aAAa;IACb,WAAW;IACX,WAAW;IACX,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;IACV,WAAW;IACX,QAAQ;IACR,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;IACV,SAAS;IACT,QAAQ;IACR,kBAAkB;AACpB;AAGO,MAAM,uBAAuB;IAClC,mBAAmB;IACnB,sBAAsB;AACxB;AAGO,MAAM,2BAA2B;IACtC,WAAW;IACX,gBAAgB;IAChB,mBAAmB;AACrB", "debugId": null}}, {"offset": {"line": 3821, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/opuScannerStyles.ts"], "sourcesContent": ["// OpuScanner integration styles\nexport const opuScannerStyles = {\n  container: 'mt-4 w-full',\n  card: 'bg-card p-4 rounded-lg shadow border border-border w-full md:w-[calc(16rem+1rem+16rem)]',\n  content: 'flex flex-col space-y-4',\n  header: 'flex items-center mb-4',\n  title: 'text-lg font-semibold text-card-foreground',  rowContainer: 'flex md:flex-row flex-col md:items-stretch md:justify-between',\n  statusContainer: 'w-full md:w-1/2 md:pr-4', // Added w-full for mobile view\n  statusBox: 'p-3 rounded-md h-full w-full flex items-center border dark:border-gray-700 border-blue-300 bg-blue-100 shadow-sm dark:bg-gray-800/50 dark:shadow-none', // Anpassung: stärker hervorstehender blauer Hintergrund im Light Mode\n  statusText: 'text-sm text-muted-foreground',  statusIndicator: (active: boolean) => `inline-block w-3 h-3 rounded-full mr-1.5 align-middle ${active ? 'bg-green-500' : 'bg-amber-500'}`,\n  toggleContainer: 'w-full md:w-1/2 mt-4 md:mt-0 flex flex-col items-center justify-center p-3 rounded-md border dark:border-gray-700 border-blue-300 bg-blue-100 shadow-sm dark:bg-gray-800/50 dark:shadow-none', // Gleiche Anpassung mit stärkerem Hintergrund im Light Mode\n  toggleLabel: 'text-sm font-medium text-card-foreground mb-3',\n  toggleDisabled: 'opacity-50 cursor-not-allowed'\n};\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;AACzB,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,SAAS;IACT,QAAQ;IACR,OAAO;IAA+C,cAAc;IACpE,iBAAiB;IACjB,WAAW;IACX,YAAY;IAAkC,iBAAiB,CAAC,SAAoB,CAAC,sDAAsD,EAAE,SAAS,iBAAiB,gBAAgB;IACvL,iBAAiB;IACjB,aAAa;IACb,gBAAgB;AAClB", "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/index.ts"], "sourcesContent": ["// Export all styles from a single file for easier imports\nexport * from './mapStyles';\nexport * from './businessCardStyles';\nexport * from './analysisStyles';\nexport * from './opuScannerStyles';\n"], "names": [], "mappings": "AAAA,0DAA0D;;AAC1D;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3874, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useMapState.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from \"react\";\r\nimport { mapDefaults, darkMapStyle } from \"../styles\";\r\nimport { MapState } from \"@/types\";\r\nimport type { User } from \"@/contexts/AuthContext\";\r\n\r\n/**\r\n * Custom hook to manage map state including center, zoom, and persistence\r\n */\r\nexport const useMapState = (user: User | null) => {\r\n  // Map state variables\r\n  const [mapCenter, setMapCenter] = useState<google.maps.LatLngLiteral>(mapDefaults.center);\r\n  const [mapZoom, setMapZoom] = useState<number>(mapDefaults.zoom);\r\n  const [mapStateLoaded, setMapStateLoaded] = useState<boolean>(false);\r\n  const [isDarkMode, setIsDarkMode] = useState<boolean>(\r\n    typeof document !== 'undefined' && document.documentElement.classList.contains('dark')\r\n  );\r\n\r\n  // Map reference\r\n  const mapRef = useRef<google.maps.Map | null>(null);\r\n  \r\n  // Track user changes to reset map state on login/logout\r\n  const prevUserRef = useRef<User | null>(null);\r\n\r\n  // Function to save map state to localStorage\r\n  const saveMapState = useCallback((businessPlaceId?: string) => {\r\n    if (!user || !mapRef.current) return; // Only save if user is logged in and map is loaded\r\n\r\n    const map = mapRef.current;\r\n    const center = map.getCenter()?.toJSON() || mapDefaults.center;\r\n    const zoom = map.getZoom() || mapDefaults.zoom;\r\n\r\n    const mapState: MapState = {\r\n      center,\r\n      zoom,\r\n      selectedBusinessPlaceId: businessPlaceId,\r\n      lastUpdated: new Date().toISOString()\r\n    };\r\n\r\n    localStorage.setItem(mapDefaults.storageKey, JSON.stringify(mapState));\r\n    console.log('Map state saved:', mapState);\r\n  }, [user]);\r\n\r\n  // Function to load map state from localStorage\r\n  const loadMapState = useCallback(() => {\r\n    if (!user) return null; // Only load if user is logged in\r\n\r\n    try {\r\n      const savedState = localStorage.getItem(mapDefaults.storageKey);\r\n      if (!savedState) return null;\r\n\r\n      const mapState: MapState = JSON.parse(savedState);\r\n      console.log('Map state loaded:', mapState);\r\n      return mapState;\r\n    } catch (error) {\r\n      console.error('Error loading map state:', error);\r\n      return null;\r\n    }\r\n  }, [user]);\r\n\r\n  // Function to reset the map to default state\r\n  const resetMapToDefaults = useCallback(() => {\r\n    console.log(\"Resetting map to defaults\");\r\n\r\n    // Reset map center and zoom\r\n    setMapCenter(mapDefaults.center);\r\n    setMapZoom(mapDefaults.zoom);\r\n    setMapStateLoaded(false);\r\n\r\n    // Update map if it's loaded\r\n    if (mapRef.current) {\r\n      mapRef.current.panTo(mapDefaults.center);\r\n      mapRef.current.setZoom(mapDefaults.zoom);\r\n    }\r\n\r\n    // Clear localStorage\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem(mapDefaults.storageKey);\r\n    }\r\n  }, []);\r\n\r\n  // Handle dark mode changes\r\n  useEffect(() => {\r\n    const observer = new MutationObserver(() => {\r\n      const isDark = document.documentElement.classList.contains('dark');\r\n      setIsDarkMode(isDark);\r\n      if (mapRef.current) {\r\n        mapRef.current.setOptions({ styles: isDark ? darkMapStyle : [] });\r\n      }\r\n    });\r\n    \r\n    if (typeof document !== 'undefined') {\r\n      observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });\r\n    }\r\n    \r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  // Reset map state ONLY on login/logout, not on page navigation\r\n  useEffect(() => {\r\n    // Only run this effect when the component mounts or when user changes\r\n    // If this is the first render, initialize prevUserRef\r\n    if (prevUserRef.current === null && user !== null) {\r\n      // First login after component mount\r\n      console.log(\"First login after component mount\");\r\n      prevUserRef.current = user;\r\n      return;\r\n    }\r\n\r\n    // If user changed from non-null to null (logout) or from null to non-null (login)\r\n    // But not when it's just a re-render with the same user\r\n    if ((prevUserRef.current === null && user !== null) || (prevUserRef.current !== null && user === null)) {\r\n      console.log(\"User login/logout detected, resetting map to defaults\");\r\n      resetMapToDefaults();\r\n    }\r\n\r\n    // Update the previous user ref\r\n    prevUserRef.current = user;\r\n  }, [user, resetMapToDefaults]);\r\n\r\n  return {\r\n    mapCenter,\r\n    setMapCenter,\r\n    mapZoom,\r\n    setMapZoom,\r\n    mapStateLoaded,\r\n    setMapStateLoaded,\r\n    isDarkMode,\r\n    mapRef,\r\n    saveMapState,\r\n    loadMapState,\r\n    resetMapToDefaults\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAOO,MAAM,cAAc,CAAC;IAC1B,sBAAsB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,6IAAA,CAAA,cAAW,CAAC,MAAM;IACxF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,6IAAA,CAAA,cAAW,CAAC,IAAI;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC,OAAO,aAAa,eAAe,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;IAGjF,gBAAgB;IAChB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAE9C,wDAAwD;IACxD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAExC,6CAA6C;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,OAAO,EAAE,QAAQ,mDAAmD;QAEzF,MAAM,MAAM,OAAO,OAAO;QAC1B,MAAM,SAAS,IAAI,SAAS,IAAI,YAAY,6IAAA,CAAA,cAAW,CAAC,MAAM;QAC9D,MAAM,OAAO,IAAI,OAAO,MAAM,6IAAA,CAAA,cAAW,CAAC,IAAI;QAE9C,MAAM,WAAqB;YACzB;YACA;YACA,yBAAyB;YACzB,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,aAAa,OAAO,CAAC,6IAAA,CAAA,cAAW,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;QAC5D,QAAQ,GAAG,CAAC,oBAAoB;IAClC,GAAG;QAAC;KAAK;IAET,+CAA+C;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,MAAM,OAAO,MAAM,iCAAiC;QAEzD,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC,6IAAA,CAAA,cAAW,CAAC,UAAU;YAC9D,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,WAAqB,KAAK,KAAK,CAAC;YACtC,QAAQ,GAAG,CAAC,qBAAqB;YACjC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF,GAAG;QAAC;KAAK;IAET,6CAA6C;IAC7C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,QAAQ,GAAG,CAAC;QAEZ,4BAA4B;QAC5B,aAAa,6IAAA,CAAA,cAAW,CAAC,MAAM;QAC/B,WAAW,6IAAA,CAAA,cAAW,CAAC,IAAI;QAC3B,kBAAkB;QAElB,4BAA4B;QAC5B,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,CAAC,KAAK,CAAC,6IAAA,CAAA,cAAW,CAAC,MAAM;YACvC,OAAO,OAAO,CAAC,OAAO,CAAC,6IAAA,CAAA,cAAW,CAAC,IAAI;QACzC;QAEA,qBAAqB;QACrB,uCAAmC;;QAEnC;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,iBAAiB;YACpC,MAAM,SAAS,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;YAC3D,cAAc;YACd,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,CAAC,UAAU,CAAC;oBAAE,QAAQ,SAAS,6IAAA,CAAA,eAAY,GAAG,EAAE;gBAAC;YACjE;QACF;QAEA,IAAI,OAAO,aAAa,aAAa;YACnC,SAAS,OAAO,CAAC,SAAS,eAAe,EAAE;gBAAE,YAAY;gBAAM,iBAAiB;oBAAC;iBAAQ;YAAC;QAC5F;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sEAAsE;QACtE,sDAAsD;QACtD,IAAI,YAAY,OAAO,KAAK,QAAQ,SAAS,MAAM;YACjD,oCAAoC;YACpC,QAAQ,GAAG,CAAC;YACZ,YAAY,OAAO,GAAG;YACtB;QACF;QAEA,kFAAkF;QAClF,wDAAwD;QACxD,IAAI,AAAC,YAAY,OAAO,KAAK,QAAQ,SAAS,QAAU,YAAY,OAAO,KAAK,QAAQ,SAAS,MAAO;YACtG,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,+BAA+B;QAC/B,YAAY,OAAO,GAAG;IACxB,GAAG;QAAC;QAAM;KAAmB;IAE7B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4005, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useBusinessSelection.ts"], "sourcesContent": ["import { useState, useRef, useCallback, useEffect } from \"react\";\r\nimport { BusinessData } from \"@/types\";\r\nimport type { User } from \"@/contexts/AuthContext\";\r\n\r\n// Helper function to map Google's Photo object to PlacePhoto-like structure\r\nconst mapGooglePhotoToPlacePhoto = (photo: google.maps.places.Photo): google.maps.places.PlacePhoto => {\r\n  return {\r\n    getUrl: (opts?: google.maps.places.PhotoOptions) => photo.getURI(opts),\r\n    height: photo.heightPx,\r\n    width: photo.widthPx,\r\n    html_attributions: photo.authorAttributions.map(attr => attr.displayName),\r\n  } as google.maps.places.PlacePhoto; // Explicit assertion though structure should match\r\n};\r\n\r\nexport const useBusinessSelection = (\r\n  user: User | null,\r\n  saveMapState: (businessPlaceId?: string) => void,\r\n  checkExistingAnalysis: (placeId: string) => void,\r\n  resetAnalysisState: () => void\r\n) => {\r\n  const [selectedBusiness, setSelectedBusiness] = useState<BusinessData | null>(null);\r\n  const [isFlipped, setIsFlipped] = useState<boolean>(false);\r\n  const [isRestoringBusiness, setIsRestoringBusiness] = useState<boolean>(false);\r\n\r\n  // Track the last selected business place_id to prevent twitching\r\n  const lastSelectedBusinessRef = useRef<string | undefined>(undefined);\r\n\r\n  // Function to restore a selected business by its place_id\r\n  const restoreSelectedBusiness = useCallback(async (placeId: string) => {\r\n    if (isRestoringBusiness) return;\r\n\r\n    console.log(\"Restoring business with place_id:\", placeId);\r\n\r\n    // Set flag to prevent concurrent restorations\r\n    setIsRestoringBusiness(true);\r\n\r\n    // Update the last selected business ref\r\n    lastSelectedBusinessRef.current = placeId;\r\n\r\n    try {\r\n      // Check if Google Maps API and Places library import function are available\r\n      if (!window.google?.maps?.importLibrary) {\r\n        console.error(\"Google Maps JavaScript API not fully loaded or importLibrary is not available.\");\r\n        setIsRestoringBusiness(false);\r\n        lastSelectedBusinessRef.current = undefined;\r\n        return;\r\n      }\r\n\r\n      const { Place } = await google.maps.importLibrary(\"places\") as typeof google.maps.places;\r\n\r\n      const place = new Place({ id: placeId });\r\n      const placeResult = await place.fetchFields({\r\n        fields: [\r\n          \"id\",\r\n          \"displayName\",\r\n          \"formattedAddress\",\r\n          \"internationalPhoneNumber\",\r\n          \"nationalPhoneNumber\",\r\n          \"websiteURI\",\r\n          \"photos\",\r\n          \"regularOpeningHours\",\r\n          \"location\",\r\n          \"types\",\r\n          \"rating\",\r\n          \"userRatingCount\",\r\n        ],\r\n      });\r\n\r\n      if (placeResult.place) {\r\n        const fetchedPlace = placeResult.place;\r\n        console.log(\"Restored business from saved state:\", fetchedPlace);\r\n        console.log(\"Raw Opening Hours from API (restoreSelectedBusiness):\", fetchedPlace.regularOpeningHours);\r\n        if (fetchedPlace.regularOpeningHours) {\r\n            console.log(\"Weekday Text from API (restoreSelectedBusiness):\", fetchedPlace.regularOpeningHours.weekday_text);\r\n        }\r\n\r\n        // Map the Place object to the existing BusinessData type\r\n        const businessData: BusinessData = {\r\n          id: fetchedPlace.id || '',\r\n          name: fetchedPlace.displayName || '',\r\n          formatted_address: fetchedPlace.formattedAddress || '',\r\n          formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',\r\n          website: fetchedPlace.websiteURI || '',\r\n          photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],\r\n          opening_hours: fetchedPlace.regularOpeningHours ?? undefined, // Ensure BusinessData.opening_hours is compatible with google.maps.places.OpeningHours\r\n          place_id: fetchedPlace.id || '',\r\n          geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined, // Ensure BusinessData.geometry is compatible\r\n          types: fetchedPlace.types || [],\r\n          rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,\r\n          user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,\r\n        };\r\n\r\n        // First set the selected business\r\n        setSelectedBusiness(businessData);\r\n        setIsFlipped(false);\r\n\r\n        // Then check for existing analysis data, only if place_id is available\r\n        if (businessData.place_id) {\r\n          checkExistingAnalysis(businessData.place_id);\r\n        } else {\r\n          resetAnalysisState();\r\n        }\r\n        setIsRestoringBusiness(false);\r\n      } else {\r\n        console.error(\"Error fetching place details: No place data returned\");\r\n        setIsRestoringBusiness(false);\r\n        lastSelectedBusinessRef.current = undefined;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching place details:\", error);\r\n      setIsRestoringBusiness(false);\r\n      lastSelectedBusinessRef.current = undefined;\r\n    }\r\n  }, [isRestoringBusiness, checkExistingAnalysis, resetAnalysisState]);\r\n\r\n  // Handle marker click\r\n  const handleMarkerClick = useCallback(async (markerData: BusinessData) => {\r\n    console.log(\"Marker clicked:\", markerData);\r\n\r\n    if (!markerData.place_id) {\r\n      console.warn(\"Marker clicked without place_id. Displaying provided data without fetching details.\", markerData);\r\n      // If markerData is already complete enough, set it. Otherwise, consider not setting or resetting.\r\n      setSelectedBusiness(markerData);\r\n      setIsFlipped(false);\r\n      resetAnalysisState(); // No place_id to check analysis for\r\n      if (user) {\r\n        saveMapState(undefined); // Or markerData.place_id if it's somehow relevant despite missing\r\n      }\r\n      return;\r\n    }\r\n\r\n    const placeId = markerData.place_id;\r\n\r\n    // Prevent selecting the same business again or re-fetching if already selected\r\n    if (selectedBusiness?.place_id === placeId) return;\r\n\r\n    // Update the last selected business ref\r\n    lastSelectedBusinessRef.current = placeId;\r\n\r\n    // Double-check that we're not in the middle of restoring a business (optional, could use a different flag if needed)\r\n    if (isRestoringBusiness) {\r\n        console.log(\"Ignoring marker click while restoring another business\");\r\n        return;\r\n    }\r\n\r\n    try {\r\n      if (!window.google?.maps?.importLibrary) {\r\n        console.error(\"Google Maps JavaScript API not fully loaded or importLibrary is not available.\");\r\n        lastSelectedBusinessRef.current = undefined; // Clear ref if fetch fails early\r\n        return;\r\n      }\r\n\r\n      const { Place } = await google.maps.importLibrary(\"places\") as typeof google.maps.places;\r\n      const place = new Place({ id: placeId });\r\n      const placeResult = await place.fetchFields({\r\n        fields: [\r\n          \"id\",\r\n          \"displayName\",\r\n          \"formattedAddress\",\r\n          \"internationalPhoneNumber\",\r\n          \"nationalPhoneNumber\",\r\n          \"websiteURI\",\r\n          \"photos\",\r\n          \"regularOpeningHours\", // Ensure this is fetched\r\n          \"location\",\r\n          \"types\",\r\n          \"rating\",\r\n          \"userRatingCount\",\r\n        ],\r\n      });\r\n\r\n      if (placeResult.place) {\r\n        const fetchedPlace = placeResult.place;\r\n        console.log(\"Fetched details for marker click:\", fetchedPlace);\r\n        console.log(\"Raw Opening Hours from API (handleMarkerClick):\", fetchedPlace.regularOpeningHours);\r\n        if (fetchedPlace.regularOpeningHours) {\r\n            console.log(\"Weekday Text from API (handleMarkerClick):\", fetchedPlace.regularOpeningHours.weekday_text);\r\n        }\r\n\r\n        const businessData: BusinessData = {\r\n          id: fetchedPlace.id || '',\r\n          name: fetchedPlace.displayName || '',\r\n          formatted_address: fetchedPlace.formattedAddress || '',\r\n          formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',\r\n          website: fetchedPlace.websiteURI || '',\r\n          photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],\r\n          opening_hours: fetchedPlace.regularOpeningHours ?? undefined,\r\n          place_id: fetchedPlace.id || '',\r\n          geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined,\r\n          types: fetchedPlace.types || [],\r\n          rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,\r\n          user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,\r\n        };\r\n\r\n        setSelectedBusiness(businessData);\r\n        setIsFlipped(false);\r\n        // Check for existing analysis data, only if place_id is a valid non-empty string\r\n        if (businessData.place_id) {\r\n          checkExistingAnalysis(businessData.place_id);\r\n        } else {\r\n          resetAnalysisState();\r\n        }\r\n        if (user) {\r\n          saveMapState(businessData.place_id);\r\n        }\r\n      } else {\r\n        console.error(\"Error fetching place details for marker: No place data returned\");\r\n        lastSelectedBusinessRef.current = undefined;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching place details for marker:\", error);\r\n      lastSelectedBusinessRef.current = undefined;\r\n    }\r\n  }, [selectedBusiness, user, checkExistingAnalysis, resetAnalysisState, saveMapState, isRestoringBusiness]);\r\n\r\n  // Handle map click (for POIs)\r\n  const handleMapClick = useCallback(async (event: google.maps.MapMouseEvent | google.maps.IconMouseEvent) => {\r\n    // Check if it's a Place ID click event\r\n    if (\"placeId\" in event && event.placeId) {\r\n      const placeId = event.placeId;\r\n      // Prevent selecting the same business again\r\n      if (selectedBusiness?.place_id === placeId) return;\r\n\r\n      // Update the last selected business ref\r\n      lastSelectedBusinessRef.current = placeId;\r\n\r\n      event.stop(); // Prevent info window from opening automatically\r\n\r\n      // Double-check that we're not in the middle of restoring a business\r\n      if (isRestoringBusiness) {\r\n        console.log(\"Ignoring map click while restoring business\");\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // Check if Google Maps API and Places library import function are available\r\n        if (!window.google?.maps?.importLibrary) {\r\n          console.error(\"Google Maps JavaScript API not fully loaded or importLibrary is not available.\");\r\n          lastSelectedBusinessRef.current = undefined;\r\n          return;\r\n        }\r\n\r\n        const { Place } = await google.maps.importLibrary(\"places\") as typeof google.maps.places;\r\n\r\n        const place = new Place({ id: placeId });\r\n        const placeResult = await place.fetchFields({\r\n          fields: [\r\n            \"id\",\r\n            \"displayName\",\r\n            \"formattedAddress\",\r\n            \"internationalPhoneNumber\",\r\n            \"nationalPhoneNumber\",\r\n            \"websiteURI\",\r\n            \"photos\",\r\n            \"regularOpeningHours\",\r\n            \"location\",\r\n            \"types\",\r\n            \"rating\",\r\n            \"userRatingCount\",\r\n          ],\r\n        });\r\n\r\n        if (placeResult.place) {\r\n          const fetchedPlace = placeResult.place;\r\n          console.log(\"Place details:\", fetchedPlace);\r\n          console.log(\"Raw Opening Hours from API (handleMapClick):\", fetchedPlace.regularOpeningHours);\r\n          if (fetchedPlace.regularOpeningHours) {\r\n              console.log(\"Weekday Text from API (handleMapClick):\", fetchedPlace.regularOpeningHours.weekday_text);\r\n          }\r\n\r\n          // Map the Place object to the existing BusinessData type\r\n          const businessData: BusinessData = {\r\n            id: fetchedPlace.id || '',\r\n            name: fetchedPlace.displayName || '',\r\n            formatted_address: fetchedPlace.formattedAddress || '',\r\n            formatted_phone_number: fetchedPlace.internationalPhoneNumber || fetchedPlace.nationalPhoneNumber || '',\r\n            website: fetchedPlace.websiteURI || '',\r\n            photos: fetchedPlace.photos ? fetchedPlace.photos.map(mapGooglePhotoToPlacePhoto) : [],\r\n            opening_hours: fetchedPlace.regularOpeningHours ?? undefined, // Ensure BusinessData.opening_hours is compatible\r\n            place_id: fetchedPlace.id || '',\r\n            geometry: fetchedPlace.location ? { location: fetchedPlace.location } : undefined, // Ensure BusinessData.geometry is compatible\r\n            types: fetchedPlace.types || [],\r\n            rating: fetchedPlace.rating === null || fetchedPlace.rating === undefined ? undefined : fetchedPlace.rating,\r\n            user_ratings_total: fetchedPlace.userRatingCount === null || fetchedPlace.userRatingCount === undefined ? undefined : fetchedPlace.userRatingCount,\r\n          };\r\n\r\n          // First set the selected business\r\n          setSelectedBusiness(businessData);\r\n          setIsFlipped(false); // Reset flip state\r\n\r\n          // Then check for existing analysis data, only if place_id is available\r\n          if (businessData.place_id) {\r\n            checkExistingAnalysis(businessData.place_id);\r\n          } else {\r\n            resetAnalysisState();\r\n          }\r\n\r\n          // Save map state after selecting a business\r\n          if (user) {\r\n            saveMapState(businessData.place_id);\r\n          }\r\n        } else {\r\n          console.error(\"Error fetching place details: No place data returned\");\r\n          lastSelectedBusinessRef.current = undefined;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching place details:\", error);\r\n        lastSelectedBusinessRef.current = undefined;\r\n      }\r\n    }\r\n    // Could add logic here to deselect business if clicking elsewhere on the map\r\n  }, [selectedBusiness, isRestoringBusiness, user, checkExistingAnalysis, resetAnalysisState, saveMapState]);\r\n\r\n  // Toggle flip state for business card\r\n  const toggleFlip = useCallback(() => {\r\n    setIsFlipped(!isFlipped);\r\n  }, [isFlipped]);\r\n\r\n  // Restore selected business when component mounts or user changes, if a placeId is stored\r\n  useEffect(() => {\r\n    // Only proceed if user is logged in, and we have a business ID to restore\r\n    if (user && lastSelectedBusinessRef.current && !selectedBusiness && !isRestoringBusiness) {\r\n      console.log(\"Attempting to restore business from saved state on mount/user change:\", lastSelectedBusinessRef.current);\r\n      restoreSelectedBusiness(lastSelectedBusinessRef.current);\r\n    }\r\n  }, [user, selectedBusiness, isRestoringBusiness, restoreSelectedBusiness, lastSelectedBusinessRef]);\r\n\r\n  // Save selected business ID when component unmounts or selected business changes\r\n  useEffect(() => {\r\n    return () => {\r\n      if (user && selectedBusiness?.place_id) {\r\n        lastSelectedBusinessRef.current = selectedBusiness.place_id;\r\n        saveMapState(selectedBusiness.place_id);\r\n      } else if (user && !selectedBusiness) {\r\n         // If no business is selected, clear the saved state\r\n        saveMapState(undefined);\r\n      }\r\n    };\r\n  }, [user, selectedBusiness, saveMapState]);\r\n\r\n  return {\r\n    selectedBusiness,\r\n    setSelectedBusiness,\r\n    isFlipped,\r\n    setIsFlipped,\r\n    isRestoringBusiness,\r\n    setIsRestoringBusiness,\r\n    lastSelectedBusinessRef,\r\n    restoreSelectedBusiness,\r\n    handleMarkerClick,\r\n    handleMapClick,\r\n    toggleFlip,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAIA,4EAA4E;AAC5E,MAAM,6BAA6B,CAAC;IAClC,OAAO;QACL,QAAQ,CAAC,OAA2C,MAAM,MAAM,CAAC;QACjE,QAAQ,MAAM,QAAQ;QACtB,OAAO,MAAM,OAAO;QACpB,mBAAmB,MAAM,kBAAkB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW;IAC1E,GAAoC,mDAAmD;AACzF;AAEO,MAAM,uBAAuB,CAClC,MACA,cACA,uBACA;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAExE,iEAAiE;IACjE,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE3D,0DAA0D;IAC1D,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjD,IAAI,qBAAqB;QAEzB,QAAQ,GAAG,CAAC,qCAAqC;QAEjD,8CAA8C;QAC9C,uBAAuB;QAEvB,wCAAwC;QACxC,wBAAwB,OAAO,GAAG;QAElC,IAAI;YACF,4EAA4E;YAC5E,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,eAAe;gBACvC,QAAQ,KAAK,CAAC;gBACd,uBAAuB;gBACvB,wBAAwB,OAAO,GAAG;gBAClC;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;YAElD,MAAM,QAAQ,IAAI,MAAM;gBAAE,IAAI;YAAQ;YACtC,MAAM,cAAc,MAAM,MAAM,WAAW,CAAC;gBAC1C,QAAQ;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YAEA,IAAI,YAAY,KAAK,EAAE;gBACrB,MAAM,eAAe,YAAY,KAAK;gBACtC,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC,yDAAyD,aAAa,mBAAmB;gBACrG,IAAI,aAAa,mBAAmB,EAAE;oBAClC,QAAQ,GAAG,CAAC,oDAAoD,aAAa,mBAAmB,CAAC,YAAY;gBACjH;gBAEA,yDAAyD;gBACzD,MAAM,eAA6B;oBACjC,IAAI,aAAa,EAAE,IAAI;oBACvB,MAAM,aAAa,WAAW,IAAI;oBAClC,mBAAmB,aAAa,gBAAgB,IAAI;oBACpD,wBAAwB,aAAa,wBAAwB,IAAI,aAAa,mBAAmB,IAAI;oBACrG,SAAS,aAAa,UAAU,IAAI;oBACpC,QAAQ,aAAa,MAAM,GAAG,aAAa,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;oBACtF,eAAe,aAAa,mBAAmB,IAAI;oBACnD,UAAU,aAAa,EAAE,IAAI;oBAC7B,UAAU,aAAa,QAAQ,GAAG;wBAAE,UAAU,aAAa,QAAQ;oBAAC,IAAI;oBACxE,OAAO,aAAa,KAAK,IAAI,EAAE;oBAC/B,QAAQ,aAAa,MAAM,KAAK,QAAQ,aAAa,MAAM,KAAK,YAAY,YAAY,aAAa,MAAM;oBAC3G,oBAAoB,aAAa,eAAe,KAAK,QAAQ,aAAa,eAAe,KAAK,YAAY,YAAY,aAAa,eAAe;gBACpJ;gBAEA,kCAAkC;gBAClC,oBAAoB;gBACpB,aAAa;gBAEb,uEAAuE;gBACvE,IAAI,aAAa,QAAQ,EAAE;oBACzB,sBAAsB,aAAa,QAAQ;gBAC7C,OAAO;oBACL;gBACF;gBACA,uBAAuB;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,uBAAuB;gBACvB,wBAAwB,OAAO,GAAG;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,uBAAuB;YACvB,wBAAwB,OAAO,GAAG;QACpC;IACF,GAAG;QAAC;QAAqB;QAAuB;KAAmB;IAEnE,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,QAAQ,IAAI,CAAC,uFAAuF;YACpG,kGAAkG;YAClG,oBAAoB;YACpB,aAAa;YACb,sBAAsB,oCAAoC;YAC1D,IAAI,MAAM;gBACR,aAAa,YAAY,kEAAkE;YAC7F;YACA;QACF;QAEA,MAAM,UAAU,WAAW,QAAQ;QAEnC,+EAA+E;QAC/E,IAAI,kBAAkB,aAAa,SAAS;QAE5C,wCAAwC;QACxC,wBAAwB,OAAO,GAAG;QAElC,qHAAqH;QACrH,IAAI,qBAAqB;YACrB,QAAQ,GAAG,CAAC;YACZ;QACJ;QAEA,IAAI;YACF,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,eAAe;gBACvC,QAAQ,KAAK,CAAC;gBACd,wBAAwB,OAAO,GAAG,WAAW,iCAAiC;gBAC9E;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;YAClD,MAAM,QAAQ,IAAI,MAAM;gBAAE,IAAI;YAAQ;YACtC,MAAM,cAAc,MAAM,MAAM,WAAW,CAAC;gBAC1C,QAAQ;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YAEA,IAAI,YAAY,KAAK,EAAE;gBACrB,MAAM,eAAe,YAAY,KAAK;gBACtC,QAAQ,GAAG,CAAC,qCAAqC;gBACjD,QAAQ,GAAG,CAAC,mDAAmD,aAAa,mBAAmB;gBAC/F,IAAI,aAAa,mBAAmB,EAAE;oBAClC,QAAQ,GAAG,CAAC,8CAA8C,aAAa,mBAAmB,CAAC,YAAY;gBAC3G;gBAEA,MAAM,eAA6B;oBACjC,IAAI,aAAa,EAAE,IAAI;oBACvB,MAAM,aAAa,WAAW,IAAI;oBAClC,mBAAmB,aAAa,gBAAgB,IAAI;oBACpD,wBAAwB,aAAa,wBAAwB,IAAI,aAAa,mBAAmB,IAAI;oBACrG,SAAS,aAAa,UAAU,IAAI;oBACpC,QAAQ,aAAa,MAAM,GAAG,aAAa,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;oBACtF,eAAe,aAAa,mBAAmB,IAAI;oBACnD,UAAU,aAAa,EAAE,IAAI;oBAC7B,UAAU,aAAa,QAAQ,GAAG;wBAAE,UAAU,aAAa,QAAQ;oBAAC,IAAI;oBACxE,OAAO,aAAa,KAAK,IAAI,EAAE;oBAC/B,QAAQ,aAAa,MAAM,KAAK,QAAQ,aAAa,MAAM,KAAK,YAAY,YAAY,aAAa,MAAM;oBAC3G,oBAAoB,aAAa,eAAe,KAAK,QAAQ,aAAa,eAAe,KAAK,YAAY,YAAY,aAAa,eAAe;gBACpJ;gBAEA,oBAAoB;gBACpB,aAAa;gBACb,iFAAiF;gBACjF,IAAI,aAAa,QAAQ,EAAE;oBACzB,sBAAsB,aAAa,QAAQ;gBAC7C,OAAO;oBACL;gBACF;gBACA,IAAI,MAAM;oBACR,aAAa,aAAa,QAAQ;gBACpC;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,wBAAwB,OAAO,GAAG;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wBAAwB,OAAO,GAAG;QACpC;IACF,GAAG;QAAC;QAAkB;QAAM;QAAuB;QAAoB;QAAc;KAAoB;IAEzG,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,uCAAuC;QACvC,IAAI,aAAa,SAAS,MAAM,OAAO,EAAE;YACvC,MAAM,UAAU,MAAM,OAAO;YAC7B,4CAA4C;YAC5C,IAAI,kBAAkB,aAAa,SAAS;YAE5C,wCAAwC;YACxC,wBAAwB,OAAO,GAAG;YAElC,MAAM,IAAI,IAAI,iDAAiD;YAE/D,oEAAoE;YACpE,IAAI,qBAAqB;gBACvB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,4EAA4E;gBAC5E,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,eAAe;oBACvC,QAAQ,KAAK,CAAC;oBACd,wBAAwB,OAAO,GAAG;oBAClC;gBACF;gBAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;gBAElD,MAAM,QAAQ,IAAI,MAAM;oBAAE,IAAI;gBAAQ;gBACtC,MAAM,cAAc,MAAM,MAAM,WAAW,CAAC;oBAC1C,QAAQ;wBACN;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;gBAEA,IAAI,YAAY,KAAK,EAAE;oBACrB,MAAM,eAAe,YAAY,KAAK;oBACtC,QAAQ,GAAG,CAAC,kBAAkB;oBAC9B,QAAQ,GAAG,CAAC,gDAAgD,aAAa,mBAAmB;oBAC5F,IAAI,aAAa,mBAAmB,EAAE;wBAClC,QAAQ,GAAG,CAAC,2CAA2C,aAAa,mBAAmB,CAAC,YAAY;oBACxG;oBAEA,yDAAyD;oBACzD,MAAM,eAA6B;wBACjC,IAAI,aAAa,EAAE,IAAI;wBACvB,MAAM,aAAa,WAAW,IAAI;wBAClC,mBAAmB,aAAa,gBAAgB,IAAI;wBACpD,wBAAwB,aAAa,wBAAwB,IAAI,aAAa,mBAAmB,IAAI;wBACrG,SAAS,aAAa,UAAU,IAAI;wBACpC,QAAQ,aAAa,MAAM,GAAG,aAAa,MAAM,CAAC,GAAG,CAAC,8BAA8B,EAAE;wBACtF,eAAe,aAAa,mBAAmB,IAAI;wBACnD,UAAU,aAAa,EAAE,IAAI;wBAC7B,UAAU,aAAa,QAAQ,GAAG;4BAAE,UAAU,aAAa,QAAQ;wBAAC,IAAI;wBACxE,OAAO,aAAa,KAAK,IAAI,EAAE;wBAC/B,QAAQ,aAAa,MAAM,KAAK,QAAQ,aAAa,MAAM,KAAK,YAAY,YAAY,aAAa,MAAM;wBAC3G,oBAAoB,aAAa,eAAe,KAAK,QAAQ,aAAa,eAAe,KAAK,YAAY,YAAY,aAAa,eAAe;oBACpJ;oBAEA,kCAAkC;oBAClC,oBAAoB;oBACpB,aAAa,QAAQ,mBAAmB;oBAExC,uEAAuE;oBACvE,IAAI,aAAa,QAAQ,EAAE;wBACzB,sBAAsB,aAAa,QAAQ;oBAC7C,OAAO;wBACL;oBACF;oBAEA,4CAA4C;oBAC5C,IAAI,MAAM;wBACR,aAAa,aAAa,QAAQ;oBACpC;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC;oBACd,wBAAwB,OAAO,GAAG;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,wBAAwB,OAAO,GAAG;YACpC;QACF;IACA,6EAA6E;IAC/E,GAAG;QAAC;QAAkB;QAAqB;QAAM;QAAuB;QAAoB;KAAa;IAEzG,sCAAsC;IACtC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,aAAa,CAAC;IAChB,GAAG;QAAC;KAAU;IAEd,0FAA0F;IAC1F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0EAA0E;QAC1E,IAAI,QAAQ,wBAAwB,OAAO,IAAI,CAAC,oBAAoB,CAAC,qBAAqB;YACxF,QAAQ,GAAG,CAAC,yEAAyE,wBAAwB,OAAO;YACpH,wBAAwB,wBAAwB,OAAO;QACzD;IACF,GAAG;QAAC;QAAM;QAAkB;QAAqB;QAAyB;KAAwB;IAElG,iFAAiF;IACjF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,QAAQ,kBAAkB,UAAU;gBACtC,wBAAwB,OAAO,GAAG,iBAAiB,QAAQ;gBAC3D,aAAa,iBAAiB,QAAQ;YACxC,OAAO,IAAI,QAAQ,CAAC,kBAAkB;gBACnC,oDAAoD;gBACrD,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAM;QAAkB;KAAa;IAEzC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4363, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/schnelle-analyse.ts"], "sourcesContent": ["import { BusinessData, AnalysisResult as SharedAnalysisResult } from '@/types'; // Import shared types\r\nimport { fetchWithAuth } from '@/lib/supabaseClient';\r\n\r\n// Define structure for saving data to our backend\r\ninterface SaveAnalysisPayload {\r\n    businessData: {\r\n        name: string;\r\n        address: string;\r\n        phone: string;\r\n        website: string;\r\n        placeId: string;\r\n        lat: number;\r\n        lng: number;\r\n    };\r\n    analysisContent: string;\r\n    analysisDate: string; // ISO string date\r\n}\r\n\r\n/**\r\n * Runs a quick analysis using the Perplexity API and saves the result.\r\n * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.\r\n * Consider moving this logic to a Next.js API route for better security.\r\n * @param businessData - Data of the business to analyze.\r\n * @returns The analysis content and the result from saving to the database, or null on failure.\r\n */\r\n// Define a more specific return type if the structure of saveResult is known\r\ninterface RunQuickAnalysisResult {\r\n    analysisContent: string;\r\n    analysisDate: string;\r\n    saveResult: unknown; // Use 'unknown' instead of 'any' for the save result initially\r\n}\r\nexport async function runQuickAnalysis(businessData: BusinessData): Promise<RunQuickAnalysisResult | null> {\r\n    // Construct prompt safely, checking for property existence\r\n    let prompt = \"Gebe mir in kurzen Stichpunkten eine schnelle Analyse über das folgende Unternehmen mit Fokus auf Key-Kennzahlen und Wettbewerbsposition:\\n\\n\";\r\n    if (businessData.name) prompt += `- Unternehmensname: ${businessData.name}\\n`;\r\n    if (businessData.formatted_address) prompt += `- Adresse: ${businessData.formatted_address}\\n`;\r\n    if (businessData.formatted_phone_number) prompt += `- Telefonnummer: ${businessData.formatted_phone_number}\\n`;\r\n    if (businessData.website) prompt += `- Webseite: ${businessData.website}\\n`;\r\n\r\n    // Get API key from environment variables (client-side accessible)\r\n    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;\r\n    if (!token) {\r\n        console.error(\"Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.\");\r\n        throw new Error(\"Perplexity API Token ist nicht konfiguriert.\");\r\n    }\r\n\r\n    const options = {\r\n        method: 'POST',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n            accept: 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n            model: \"sonar\",\r\n            messages: [\r\n                { role: \"system\", content: \"Be precise and concise. Provide key metrics and competitive position analysis in bullet points. No explanations or extra formatting.\" },\r\n                { role: \"user\", content: prompt }\r\n            ],\r\n            // Adjust parameters as needed\r\n            // max_tokens: 3000,\r\n            // temperature: 0.2,\r\n        })\r\n    };\r\n\r\n    try {\r\n        const response = await fetch('https://api.perplexity.ai/chat/completions', options);\r\n        if (!response.ok) {\r\n            const errorText = await response.text();\r\n            console.error(\"Perplexity API Error Response:\", errorText);\r\n            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`); // Limit error message length\r\n        }\r\n\r\n        const data = await response.json();\r\n        let analysisContent = \"\";\r\n\r\n        // Extract analysis content\r\n        if (data?.choices?.[0]?.message?.content) {\r\n            analysisContent = data.choices[0].message.content;\r\n        } else {\r\n            console.warn(\"Could not extract analysis content from Perplexity response:\", data);\r\n            analysisContent = \"Analyse konnte nicht extrahiert werden.\"; // Provide default message\r\n        }\r\n\r\n        // Save the analysis to the application's database via its API route\r\n        const analysisDate = new Date().toISOString();\r\n        const saveResult = await saveAnalysisToDatabase(businessData, analysisContent, analysisDate);\r\n\r\n        return {\r\n            analysisContent,\r\n            analysisDate, // Return the date used for saving\r\n            saveResult // Assuming saveResult is the direct JSON response from the backend save operation\r\n        };\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler in runQuickAnalysis:\", error);\r\n        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n        // Re-throw or handle error appropriately for the UI\r\n        throw new Error(`Fehler bei der Schnellanalyse: ${message}`);\r\n    }\r\n}\r\n\r\n/**\r\n * Saves the analysis result to the application's backend database.\r\n * @param businessData - Data of the analyzed business.\r\n * @param analysisContent - The content generated by the analysis API.\r\n * @param analysisDate - The ISO string date of the analysis.\r\n * @returns The result from the backend save operation (use unknown or a specific type).\r\n */\r\nasync function saveAnalysisToDatabase(businessData: BusinessData, analysisContent: string, analysisDate: string): Promise<unknown> {\r\n    // Extract lat/lng carefully, checking for different structures\r\n    let lat: number = 0;\r\n    let lng: number = 0;\r\n    if ('geometry' in businessData && businessData.geometry?.location) {\r\n        const location = businessData.geometry.location;\r\n        // Check if lat/lng are functions and call them, otherwise treat as numbers\r\n        if (typeof location.lat === 'function' && typeof location.lng === 'function') {\r\n            lat = location.lat();\r\n            lng = location.lng();\r\n        } else {\r\n            // Fallback: Explicitly cast to unknown then LatLngLiteral\r\n            const literalLocation = location as unknown as google.maps.LatLngLiteral;\r\n            lat = literalLocation.lat ?? 0;\r\n            lng = literalLocation.lng ?? 0;\r\n        }\r\n    } else if ('position' in businessData && businessData.position) { // Handle DemoMarker position\r\n        lat = businessData.position.lat;\r\n        lng = businessData.position.lng;\r\n    }\r\n\r\n    // Determine placeId safely\r\n    let placeId = businessData.place_id ?? \"\"; // Prefer place_id if it exists\r\n    if (!placeId && 'id' in businessData) { // If no place_id, check if it's a DemoMarker with an id\r\n        placeId = `demo_${businessData.id}`;\r\n    }\r\n\r\n    const payload: SaveAnalysisPayload = {\r\n        businessData: {\r\n            name: businessData.name ?? \"Unbekannt\",\r\n            address: businessData.formatted_address ?? \"\",\r\n            phone: businessData.formatted_phone_number ?? \"\",\r\n            website: businessData.website ?? \"\",\r\n            placeId: placeId, // Use the safely determined placeId\r\n            lat,\r\n            lng\r\n        },\r\n        analysisContent,\r\n        analysisDate\r\n    };\r\n\r\n    try {\r\n        // Use the new analyses endpoint with type=quick\r\n        const response = await fetchWithAuth('/api/analyses', {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify({\r\n                ...payload,\r\n                type: 'quick' // Add the type parameter\r\n            })\r\n        });\r\n\r\n        if (!response.ok) {\r\n            const errorText = await response.text();\r\n            console.error(\"Server Error Saving Analysis:\", errorText);\r\n            throw new Error(`Server-Fehler (${response.status}) beim Speichern der Analyse.`);\r\n        }\r\n\r\n        return await response.json(); // Return the parsed JSON response\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler beim Speichern der Analyse:\", error);\r\n        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n        throw new Error(`Fehler beim Speichern der Analyse: ${message}`);\r\n    }\r\n}\r\n\r\n/**\r\n * Fetches existing quick analysis data for a given place ID from the backend.\r\n * @param placeId - The Google Place ID of the business.\r\n * @returns The analysis data or null if not found.\r\n */\r\nexport async function getQuickAnalysis(placeId: string): Promise<SharedAnalysisResult | null> { // Use shared type for return\r\n    if (!placeId) {\r\n        console.warn(\"getQuickAnalysis called without placeId\");\r\n        return null;\r\n    }\r\n    try {\r\n        // Use the new analyses/quick endpoint\r\n        const response = await fetchWithAuth(`/api/analyses/quick/${placeId}`);\r\n\r\n        // Handle authentication errors gracefully\r\n        if (response.status === 401) {\r\n            console.warn(\"Authentication required for fetching analysis data\");\r\n            return null; // Return null instead of throwing an error\r\n        }\r\n\r\n        if (!response.ok) {\r\n            if (response.status === 404) {\r\n                console.log(`Keine schnelle Analyse für placeId ${placeId} gefunden.`);\r\n                return null; // Not found is not necessarily an error here\r\n            }\r\n            const errorText = await response.text();\r\n            console.error(\"Server Error Fetching Analysis:\", errorText);\r\n            // Don't throw, just return null to prevent UI crashes\r\n            return null;\r\n        }\r\n\r\n        const data: SharedAnalysisResult = await response.json(); // Use shared type\r\n        return data;\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler beim Abrufen der Analyse:\", error);\r\n        // Always return null to indicate failure without crashing UI\r\n        return null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;AA8BO,eAAe,iBAAiB,YAA0B;IAC7D,2DAA2D;IAC3D,IAAI,SAAS;IACb,IAAI,aAAa,IAAI,EAAE,UAAU,CAAC,oBAAoB,EAAE,aAAa,IAAI,CAAC,EAAE,CAAC;IAC7E,IAAI,aAAa,iBAAiB,EAAE,UAAU,CAAC,WAAW,EAAE,aAAa,iBAAiB,CAAC,EAAE,CAAC;IAC9F,IAAI,aAAa,sBAAsB,EAAE,UAAU,CAAC,iBAAiB,EAAE,aAAa,sBAAsB,CAAC,EAAE,CAAC;IAC9G,IAAI,aAAa,OAAO,EAAE,UAAU,CAAC,YAAY,EAAE,aAAa,OAAO,CAAC,EAAE,CAAC;IAE3E,kEAAkE;IAClE,MAAM;IACN,uCAAY;;IAGZ;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,SAAS;YACL,eAAe,CAAC,OAAO,EAAE,OAAO;YAChC,gBAAgB;YAChB,QAAQ;QACZ;QACA,MAAM,KAAK,SAAS,CAAC;YACjB,OAAO;YACP,UAAU;gBACN;oBAAE,MAAM;oBAAU,SAAS;gBAAuI;gBAClK;oBAAE,MAAM;oBAAQ,SAAS;gBAAO;aACnC;QAIL;IACJ;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,8CAA8C;QAC3E,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,UAAU,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,6BAA6B;QACnI;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,kBAAkB;QAEtB,2BAA2B;QAC3B,IAAI,MAAM,SAAS,CAAC,EAAE,EAAE,SAAS,SAAS;YACtC,kBAAkB,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;QACrD,OAAO;YACH,QAAQ,IAAI,CAAC,gEAAgE;YAC7E,kBAAkB,2CAA2C,0BAA0B;QAC3F;QAEA,oEAAoE;QACpE,MAAM,eAAe,IAAI,OAAO,WAAW;QAC3C,MAAM,aAAa,MAAM,uBAAuB,cAAc,iBAAiB;QAE/E,OAAO;YACH;YACA;YACA;QACJ;IACJ,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,oDAAoD;QACpD,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS;IAC/D;AACJ;AAEA;;;;;;CAMC,GACD,eAAe,uBAAuB,YAA0B,EAAE,eAAuB,EAAE,YAAoB;IAC3G,+DAA+D;IAC/D,IAAI,MAAc;IAClB,IAAI,MAAc;IAClB,IAAI,cAAc,gBAAgB,aAAa,QAAQ,EAAE,UAAU;QAC/D,MAAM,WAAW,aAAa,QAAQ,CAAC,QAAQ;QAC/C,2EAA2E;QAC3E,IAAI,OAAO,SAAS,GAAG,KAAK,cAAc,OAAO,SAAS,GAAG,KAAK,YAAY;YAC1E,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG;QACtB,OAAO;YACH,0DAA0D;YAC1D,MAAM,kBAAkB;YACxB,MAAM,gBAAgB,GAAG,IAAI;YAC7B,MAAM,gBAAgB,GAAG,IAAI;QACjC;IACJ,OAAO,IAAI,cAAc,gBAAgB,aAAa,QAAQ,EAAE;QAC5D,MAAM,aAAa,QAAQ,CAAC,GAAG;QAC/B,MAAM,aAAa,QAAQ,CAAC,GAAG;IACnC;IAEA,2BAA2B;IAC3B,IAAI,UAAU,aAAa,QAAQ,IAAI,IAAI,+BAA+B;IAC1E,IAAI,CAAC,WAAW,QAAQ,cAAc;QAClC,UAAU,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;IACvC;IAEA,MAAM,UAA+B;QACjC,cAAc;YACV,MAAM,aAAa,IAAI,IAAI;YAC3B,SAAS,aAAa,iBAAiB,IAAI;YAC3C,OAAO,aAAa,sBAAsB,IAAI;YAC9C,SAAS,aAAa,OAAO,IAAI;YACjC,SAAS;YACT;YACA;QACJ;QACA;QACA;IACJ;IAEA,IAAI;QACA,gDAAgD;QAChD,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB;YAClD,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBACjB,GAAG,OAAO;gBACV,MAAM,QAAQ,yBAAyB;YAC3C;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,6BAA6B,CAAC;QACpF;QAEA,OAAO,MAAM,SAAS,IAAI,IAAI,kCAAkC;IACpE,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS;IACnE;AACJ;AAOO,eAAe,iBAAiB,OAAe;IAClD,IAAI,CAAC,SAAS;QACV,QAAQ,IAAI,CAAC;QACb,OAAO;IACX;IACA,IAAI;QACA,sCAAsC;QACtC,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,oBAAoB,EAAE,SAAS;QAErE,0CAA0C;QAC1C,IAAI,SAAS,MAAM,KAAK,KAAK;YACzB,QAAQ,IAAI,CAAC;YACb,OAAO,MAAM,2CAA2C;QAC5D;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,IAAI,SAAS,MAAM,KAAK,KAAK;gBACzB,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,UAAU,CAAC;gBACrE,OAAO,MAAM,6CAA6C;YAC9D;YACA,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,mCAAmC;YACjD,sDAAsD;YACtD,OAAO;QACX;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI,IAAI,kBAAkB;QAC5E,OAAO;IACX,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,oCAAoC;QAClD,6DAA6D;QAC7D,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 4538, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/tiefe-analyse.ts"], "sourcesContent": ["import { BusinessData, AnalysisResult as SharedAnalysisResult } from '@/types'; // Import shared types\r\nimport { fetchWithAuth } from '@/lib/supabaseClient';\r\n\r\n// Define structure for saving data to our backend (can reuse if identical to schnelle-analyse)\r\ninterface SaveAnalysisPayload {\r\n    businessData: {\r\n        name: string;\r\n        address: string;\r\n        phone: string;\r\n        website: string;\r\n        placeId: string;\r\n        lat: number;\r\n        lng: number;\r\n    };\r\n    analysisContent: string;\r\n    analysisDate: string; // ISO string date\r\n}\r\n\r\n/**\r\n * Runs a deep analysis using the Perplexity API and saves the result.\r\n * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.\r\n * Consider moving this logic to a Next.js API route for better security.\r\n * @param businessData - Data of the business to analyze.\r\n * @returns The analysis content and the result from saving to the database, or null on failure.\r\n */\r\n// Define a more specific return type if the structure of saveResult is known\r\ninterface RunDeepAnalysisResult {\r\n    analysisContent: string;\r\n    analysisDate: string;\r\n    saveResult: unknown; // Use 'unknown' instead of 'any'\r\n}\r\nexport async function runDeepAnalysis(businessData: BusinessData): Promise<RunDeepAnalysisResult | null> {\r\n    // Construct prompt safely\r\n    let prompt = \"Gebe mir eine detaillierte Analyse über das folgende Unternehmen mit Fokus auf Key-Kennzahlen, Wettbewerbsposition, Markttrends und Zukunftsperspektiven:\\n\\n\";\r\n    if (businessData.name) prompt += `- Unternehmensname: ${businessData.name}\\n`;\r\n    if (businessData.formatted_address) prompt += `- Adresse: ${businessData.formatted_address}\\n`;\r\n    if (businessData.formatted_phone_number) prompt += `- Telefonnummer: ${businessData.formatted_phone_number}\\n`;\r\n    if (businessData.website) prompt += `- Webseite: ${businessData.website}\\n`;\r\n\r\n    // Get API key\r\n    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;\r\n    if (!token) {\r\n        console.error(\"Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.\");\r\n        throw new Error(\"Perplexity API Token ist nicht konfiguriert.\");\r\n    }\r\n\r\n    const options = {\r\n        method: 'POST',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n            accept: 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n            model: \"sonar-pro\", // Use the large online sonar model as requested\r\n            messages: [\r\n                { role: \"system\", content: \"Provide a comprehensive analysis with key metrics, competitive positioning, market trends, and future outlook. Be precise and well-structured.\" },\r\n                { role: \"user\", content: prompt }\r\n            ],\r\n             // max_tokens: 5000, // Consider adjusting based on model and needs\r\n             // temperature: 0.2,\r\n        })\r\n    };\r\n\r\n    try {\r\n        const response = await fetch('https://api.perplexity.ai/chat/completions', options);\r\n        if (!response.ok) {\r\n            const errorText = await response.text();\r\n            console.error(\"Perplexity API Error Response:\", errorText);\r\n            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        let analysisContent = \"\";\r\n\r\n        if (data?.choices?.[0]?.message?.content) {\r\n            analysisContent = data.choices[0].message.content;\r\n        } else {\r\n            console.warn(\"Could not extract analysis content from Perplexity response:\", data);\r\n            analysisContent = \"Detaillierte Analyse konnte nicht extrahiert werden.\";\r\n        }\r\n\r\n        const analysisDate = new Date().toISOString();\r\n        const saveResult = await saveAnalysisToDatabase(businessData, analysisContent, analysisDate);\r\n\r\n        return {\r\n            analysisContent,\r\n            analysisDate,\r\n            saveResult // Assuming saveResult is the direct JSON response\r\n        };\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler in runDeepAnalysis:\", error);\r\n        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n        throw new Error(`Fehler bei der Tiefenanalyse: ${message}`);\r\n    }\r\n}\r\n\r\n/**\r\n * Saves the deep analysis result to the application's backend database.\r\n * @param businessData - Data of the analyzed business.\r\n * @param analysisContent - The content generated by the analysis API.\r\n * @param analysisDate - The ISO string date of the analysis.\r\n * @returns The result from the backend save operation (use unknown or a specific type).\r\n */\r\nasync function saveAnalysisToDatabase(businessData: BusinessData, analysisContent: string, analysisDate: string): Promise<unknown> {\r\n    // Extract lat/lng carefully\r\n    let lat: number = 0;\r\n    let lng: number = 0;\r\n    if ('geometry' in businessData && businessData.geometry?.location) {\r\n        const location = businessData.geometry.location;\r\n        if (typeof location.lat === 'function' && typeof location.lng === 'function') {\r\n            lat = location.lat();\r\n            lng = location.lng();\r\n        } else {\r\n            // Fallback: Explicitly cast to unknown then LatLngLiteral\r\n            const literalLocation = location as unknown as google.maps.LatLngLiteral;\r\n            lat = literalLocation.lat ?? 0;\r\n            lng = literalLocation.lng ?? 0;\r\n        }\r\n    } else if ('position' in businessData && businessData.position) {\r\n        lat = businessData.position.lat;\r\n        lng = businessData.position.lng;\r\n    }\r\n\r\n    // Determine placeId safely\r\n    let placeId = businessData.place_id ?? \"\";\r\n    if (!placeId && 'id' in businessData) {\r\n        placeId = `demo_${businessData.id}`;\r\n    }\r\n\r\n    const payload: SaveAnalysisPayload = {\r\n        businessData: {\r\n            name: businessData.name ?? \"Unbekannt\",\r\n            address: businessData.formatted_address ?? \"\",\r\n            phone: businessData.formatted_phone_number ?? \"\",\r\n            website: businessData.website ?? \"\",\r\n            placeId: placeId,\r\n            lat,\r\n            lng\r\n        },\r\n        analysisContent,\r\n        analysisDate\r\n    };\r\n\r\n    try {\r\n        // Use the new analyses endpoint with type=deep\r\n        const response = await fetchWithAuth('/api/analyses', {\r\n            method: 'POST',\r\n            headers: {\r\n                'Content-Type': 'application/json'\r\n            },\r\n            body: JSON.stringify({\r\n                ...payload,\r\n                type: 'deep' // Add the type parameter\r\n            })\r\n        });\r\n\r\n        if (!response.ok) {\r\n            const errorText = await response.text();\r\n            console.error(\"Server Error Saving Deep Analysis:\", errorText);\r\n            throw new Error(`Server-Fehler (${response.status}) beim Speichern der Tiefenanalyse.`);\r\n        }\r\n\r\n        return await response.json(); // Return parsed JSON response\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler beim Speichern der Tiefenanalyse:\", error);\r\n        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n        throw new Error(`Fehler beim Speichern der Tiefenanalyse: ${message}`);\r\n    }\r\n}\r\n\r\n/**\r\n * Fetches existing deep analysis data for a given place ID from the backend.\r\n * @param placeId - The Google Place ID of the business.\r\n * @returns The analysis data or null if not found.\r\n */\r\nexport async function getDeepAnalysis(placeId: string): Promise<SharedAnalysisResult | null> { // Use shared type\r\n    if (!placeId) {\r\n        console.warn(\"getDeepAnalysis called without placeId\");\r\n        return null;\r\n    }\r\n    try {\r\n        // Use the new analyses/deep endpoint\r\n        const response = await fetchWithAuth(`/api/analyses/deep/${placeId}`);\r\n\r\n        // Handle authentication errors gracefully\r\n        if (response.status === 401) {\r\n            console.warn(\"Authentication required for fetching deep analysis data\");\r\n            return null; // Return null instead of throwing an error\r\n        }\r\n\r\n        if (!response.ok) {\r\n            if (response.status === 404) {\r\n                console.log(`Keine tiefe Analyse für placeId ${placeId} gefunden.`);\r\n                return null;\r\n            }\r\n            const errorText = await response.text();\r\n            console.error(\"Server Error Fetching Deep Analysis:\", errorText);\r\n            // Don't throw, just return null to prevent UI crashes\r\n            return null;\r\n        }\r\n\r\n        const data: SharedAnalysisResult = await response.json(); // Use shared type\r\n        return data;\r\n    } catch (error: unknown) {\r\n        console.error(\"Fehler beim Abrufen der Tiefenanalyse:\", error);\r\n        return null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;AA8BO,eAAe,gBAAgB,YAA0B;IAC5D,0BAA0B;IAC1B,IAAI,SAAS;IACb,IAAI,aAAa,IAAI,EAAE,UAAU,CAAC,oBAAoB,EAAE,aAAa,IAAI,CAAC,EAAE,CAAC;IAC7E,IAAI,aAAa,iBAAiB,EAAE,UAAU,CAAC,WAAW,EAAE,aAAa,iBAAiB,CAAC,EAAE,CAAC;IAC9F,IAAI,aAAa,sBAAsB,EAAE,UAAU,CAAC,iBAAiB,EAAE,aAAa,sBAAsB,CAAC,EAAE,CAAC;IAC9G,IAAI,aAAa,OAAO,EAAE,UAAU,CAAC,YAAY,EAAE,aAAa,OAAO,CAAC,EAAE,CAAC;IAE3E,cAAc;IACd,MAAM;IACN,uCAAY;;IAGZ;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,SAAS;YACL,eAAe,CAAC,OAAO,EAAE,OAAO;YAChC,gBAAgB;YAChB,QAAQ;QACZ;QACA,MAAM,KAAK,SAAS,CAAC;YACjB,OAAO;YACP,UAAU;gBACN;oBAAE,MAAM;oBAAU,SAAS;gBAAiJ;gBAC5K;oBAAE,MAAM;oBAAQ,SAAS;gBAAO;aACnC;QAGL;IACJ;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,8CAA8C;QAC3E,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,UAAU,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QACnG;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,kBAAkB;QAEtB,IAAI,MAAM,SAAS,CAAC,EAAE,EAAE,SAAS,SAAS;YACtC,kBAAkB,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;QACrD,OAAO;YACH,QAAQ,IAAI,CAAC,gEAAgE;YAC7E,kBAAkB;QACtB;QAEA,MAAM,eAAe,IAAI,OAAO,WAAW;QAC3C,MAAM,aAAa,MAAM,uBAAuB,cAAc,iBAAiB;QAE/E,OAAO;YACH;YACA;YACA;QACJ;IACJ,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS;IAC9D;AACJ;AAEA;;;;;;CAMC,GACD,eAAe,uBAAuB,YAA0B,EAAE,eAAuB,EAAE,YAAoB;IAC3G,4BAA4B;IAC5B,IAAI,MAAc;IAClB,IAAI,MAAc;IAClB,IAAI,cAAc,gBAAgB,aAAa,QAAQ,EAAE,UAAU;QAC/D,MAAM,WAAW,aAAa,QAAQ,CAAC,QAAQ;QAC/C,IAAI,OAAO,SAAS,GAAG,KAAK,cAAc,OAAO,SAAS,GAAG,KAAK,YAAY;YAC1E,MAAM,SAAS,GAAG;YAClB,MAAM,SAAS,GAAG;QACtB,OAAO;YACH,0DAA0D;YAC1D,MAAM,kBAAkB;YACxB,MAAM,gBAAgB,GAAG,IAAI;YAC7B,MAAM,gBAAgB,GAAG,IAAI;QACjC;IACJ,OAAO,IAAI,cAAc,gBAAgB,aAAa,QAAQ,EAAE;QAC5D,MAAM,aAAa,QAAQ,CAAC,GAAG;QAC/B,MAAM,aAAa,QAAQ,CAAC,GAAG;IACnC;IAEA,2BAA2B;IAC3B,IAAI,UAAU,aAAa,QAAQ,IAAI;IACvC,IAAI,CAAC,WAAW,QAAQ,cAAc;QAClC,UAAU,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;IACvC;IAEA,MAAM,UAA+B;QACjC,cAAc;YACV,MAAM,aAAa,IAAI,IAAI;YAC3B,SAAS,aAAa,iBAAiB,IAAI;YAC3C,OAAO,aAAa,sBAAsB,IAAI;YAC9C,SAAS,aAAa,OAAO,IAAI;YACjC,SAAS;YACT;YACA;QACJ;QACA;QACA;IACJ;IAEA,IAAI;QACA,+CAA+C;QAC/C,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB;YAClD,QAAQ;YACR,SAAS;gBACL,gBAAgB;YACpB;YACA,MAAM,KAAK,SAAS,CAAC;gBACjB,GAAG,OAAO;gBACV,MAAM,OAAO,yBAAyB;YAC1C;QACJ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,mCAAmC,CAAC;QAC1F;QAEA,OAAO,MAAM,SAAS,IAAI,IAAI,8BAA8B;IAChE,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS;IACzE;AACJ;AAOO,eAAe,gBAAgB,OAAe;IACjD,IAAI,CAAC,SAAS;QACV,QAAQ,IAAI,CAAC;QACb,OAAO;IACX;IACA,IAAI;QACA,qCAAqC;QACrC,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,CAAC,mBAAmB,EAAE,SAAS;QAEpE,0CAA0C;QAC1C,IAAI,SAAS,MAAM,KAAK,KAAK;YACzB,QAAQ,IAAI,CAAC;YACb,OAAO,MAAM,2CAA2C;QAC5D;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,IAAI,SAAS,MAAM,KAAK,KAAK;gBACzB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,QAAQ,UAAU,CAAC;gBAClE,OAAO;YACX;YACA,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,wCAAwC;YACtD,sDAAsD;YACtD,OAAO;QACX;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI,IAAI,kBAAkB;QAC5E,OAAO;IACX,EAAE,OAAO,OAAgB;QACrB,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 4708, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useAnalysis.ts"], "sourcesContent": ["import { useState, useCallback } from \"react\";\r\nimport { BusinessData, AnalysisResult } from \"@/types\";\r\nimport { runQuickAnalysis, getQuickAnalysis } from \"@/api/schnelle-analyse\";\r\nimport { runDeepAnalysis, getDeepAnalysis } from \"@/api/tiefe-analyse\";\r\nimport type { User } from \"@/contexts/AuthContext\";\r\n\r\n/**\r\n * Custom hook to manage analysis data and operations\r\n */\r\nexport const useAnalysis = (user: User | null) => {\r\n  // Analysis state\r\n  const [quickAnalysisData, setQuickAnalysisData] = useState<string | null>(null);\r\n  const [quickAnalysisDate, setQuickAnalysisDate] = useState<string | null>(null);\r\n  const [isQuickAnalysisLoading, setIsQuickAnalysisLoading] = useState<boolean>(false);\r\n\r\n  const [deepAnalysisData, setDeepAnalysisData] = useState<string | null>(null);\r\n  const [deepAnalysisDate, setDeepAnalysisDate] = useState<string | null>(null);\r\n  const [isDeepAnalysisLoading, setIsDeepAnalysisLoading] = useState<boolean>(false);\r\n\r\n  const [showAnalysisModal, setShowAnalysisModal] = useState<boolean>(false);\r\n  const [currentAnalysisType, setCurrentAnalysisType] = useState<\"schnell\" | \"tief\">(\"schnell\");\r\n\r\n  // Reset analysis data when a new business is selected or deselected\r\n  const resetAnalysisState = useCallback(() => {\r\n    setQuickAnalysisData(null);\r\n    setQuickAnalysisDate(null);\r\n    setDeepAnalysisData(null);\r\n    setDeepAnalysisDate(null);\r\n    setIsQuickAnalysisLoading(false); // Also reset loading states\r\n    setIsDeepAnalysisLoading(false);\r\n  }, []);\r\n\r\n  // Check if analysis data already exists for the selected place ID\r\n  const checkExistingAnalysis = useCallback(async (placeId: string) => {\r\n    // Don't fetch analysis data if we're not logged in\r\n    if (!user) {\r\n      console.log(\"Skipping analysis fetch - user not logged in\");\r\n      return;\r\n    }\r\n\r\n    // Wrap everything in a try-catch to prevent any errors from bubbling up\r\n    try {\r\n      console.log(`Fetching analysis data for place ID: ${placeId}`);\r\n\r\n      // Use Promise.allSettled to fetch both analysis types in parallel\r\n      // This ensures that if one fails, the other can still complete\r\n      const [quickPromise, deepPromise] = await Promise.allSettled([\r\n        getQuickAnalysis(placeId),\r\n        getDeepAnalysis(placeId)\r\n      ]);\r\n\r\n      // Handle quick analysis result\r\n      if (quickPromise.status === 'fulfilled') {\r\n        const quickResult = quickPromise.value;\r\n        console.log(`Quick analysis result for ${placeId}:`, quickResult);\r\n\r\n        if (quickResult && quickResult.analysisContent) {\r\n          setQuickAnalysisData(quickResult.analysisContent);\r\n          setQuickAnalysisDate(quickResult.analysisDate);\r\n        } else {\r\n          setQuickAnalysisData(null);\r\n          setQuickAnalysisDate(null);\r\n        }\r\n      } else {\r\n        console.warn(\"Quick analysis fetch rejected:\", quickPromise.reason);\r\n        setQuickAnalysisData(null);\r\n        setQuickAnalysisDate(null);\r\n      }\r\n\r\n      // Handle deep analysis result\r\n      if (deepPromise.status === 'fulfilled') {\r\n        const deepResult = deepPromise.value;\r\n        console.log(`Deep analysis result for ${placeId}:`, deepResult);\r\n\r\n        if (deepResult && deepResult.analysisContent) {\r\n          setDeepAnalysisData(deepResult.analysisContent);\r\n          setDeepAnalysisDate(deepResult.analysisDate);\r\n        } else {\r\n          setDeepAnalysisData(null);\r\n          setDeepAnalysisDate(null);\r\n        }\r\n      } else {\r\n        console.warn(\"Deep analysis fetch rejected:\", deepPromise.reason);\r\n        setDeepAnalysisData(null);\r\n        setDeepAnalysisDate(null);\r\n      }\r\n    } catch (error) {\r\n      // This catch block should never be reached due to Promise.allSettled,\r\n      // but we keep it as a safety net\r\n      console.error(\"Unexpected error in checkExistingAnalysis:\", error);\r\n      resetAnalysisState();\r\n    }\r\n  }, [user, resetAnalysisState]);\r\n\r\n  // Handler to trigger a new quick analysis\r\n  const handleCreateQuickAnalysis = useCallback(async (selectedBusiness: BusinessData) => {\r\n    if (!selectedBusiness || !selectedBusiness.place_id) return; // Guard clause\r\n\r\n    try {\r\n      setIsQuickAnalysisLoading(true);\r\n      const result: AnalysisResult | null = await runQuickAnalysis(selectedBusiness);\r\n      if (result && result.analysisContent) {\r\n        setQuickAnalysisData(result.analysisContent);\r\n        setQuickAnalysisDate(result.analysisDate || new Date().toISOString()); // Use current date if missing\r\n        setCurrentAnalysisType(\"schnell\");\r\n        setShowAnalysisModal(true); // Show modal with new data\r\n      } else {\r\n        console.error(\"Keine Schnellanalyseergebnisse erhalten\");\r\n        // Use a more user-friendly notification instead of alert if possible\r\n        alert(\"Fehler bei der Erstellung der Schnellanalyse.\");\r\n      }\r\n    } catch (error: unknown) {\r\n      console.error(\"Fehler bei der Erstellung der Schnellanalyse:\", error);\r\n      const message = error instanceof Error ? error.message : \"Unbekannter Fehler\";\r\n      alert(\"Fehler bei der Erstellung der Schnellanalyse: \" + message);\r\n    } finally {\r\n      setIsQuickAnalysisLoading(false); // Ensure loading state is reset\r\n    }\r\n  }, []);\r\n\r\n  // Handler to view existing quick analysis data\r\n  const handleViewQuickAnalysis = useCallback(() => {\r\n    if (quickAnalysisData) {\r\n      setCurrentAnalysisType(\"schnell\");\r\n      setShowAnalysisModal(true);\r\n    }\r\n    // Optionally, add an else block to inform the user if no data exists\r\n  }, [quickAnalysisData]);\r\n\r\n  // Handler to trigger a new deep analysis\r\n  const handleCreateDeepAnalysis = useCallback(async (selectedBusiness: BusinessData) => {\r\n    if (!selectedBusiness || !selectedBusiness.place_id) return; // Guard clause\r\n\r\n    try {\r\n      setIsDeepAnalysisLoading(true);\r\n      const result: AnalysisResult | null = await runDeepAnalysis(selectedBusiness);\r\n      if (result && result.analysisContent) {\r\n        setDeepAnalysisData(result.analysisContent);\r\n        setDeepAnalysisDate(result.analysisDate || new Date().toISOString()); // Use current date if missing\r\n        setCurrentAnalysisType(\"tief\");\r\n        setShowAnalysisModal(true); // Show modal with new data\r\n      } else {\r\n        console.error(\"Keine Tiefenanalyseergebnisse erhalten\");\r\n        alert(\"Fehler bei der Erstellung der Tiefenanalyse.\");\r\n      }\r\n    } catch (error: unknown) {\r\n      console.error(\"Fehler bei der Erstellung der Tiefenanalyse:\", error);\r\n      const message = error instanceof Error ? error.message : \"Unbekannter Fehler\";\r\n      alert(\"Fehler bei der Erstellung der Tiefenanalyse: \" + message);\r\n    } finally {\r\n      setIsDeepAnalysisLoading(false); // Ensure loading state is reset\r\n    }\r\n  }, []);\r\n\r\n  // Handler to view existing deep analysis data\r\n  const handleViewDeepAnalysis = useCallback(() => {\r\n    if (deepAnalysisData) {\r\n      setCurrentAnalysisType(\"tief\");\r\n      setShowAnalysisModal(true);\r\n    }\r\n    // Optionally, add an else block to inform the user if no data exists\r\n  }, [deepAnalysisData]);\r\n\r\n  return {\r\n    quickAnalysisData,\r\n    quickAnalysisDate,\r\n    isQuickAnalysisLoading,\r\n    deepAnalysisData,\r\n    deepAnalysisDate,\r\n    isDeepAnalysisLoading,\r\n    showAnalysisModal,\r\n    setShowAnalysisModal,\r\n    currentAnalysisType,\r\n    resetAnalysisState,\r\n    checkExistingAnalysis,\r\n    handleCreateQuickAnalysis,\r\n    handleViewQuickAnalysis,\r\n    handleCreateDeepAnalysis,\r\n    handleViewDeepAnalysis\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAMO,MAAM,cAAc,CAAC;IAC1B,iBAAiB;IACjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE9E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE5E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnF,oEAAoE;IACpE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,qBAAqB;QACrB,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,0BAA0B,QAAQ,4BAA4B;QAC9D,yBAAyB;IAC3B,GAAG,EAAE;IAEL,kEAAkE;IAClE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,mDAAmD;QACnD,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,wEAAwE;QACxE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,SAAS;YAE7D,kEAAkE;YAClE,+DAA+D;YAC/D,MAAM,CAAC,cAAc,YAAY,GAAG,MAAM,QAAQ,UAAU,CAAC;gBAC3D,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACjB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;aACjB;YAED,+BAA+B;YAC/B,IAAI,aAAa,MAAM,KAAK,aAAa;gBACvC,MAAM,cAAc,aAAa,KAAK;gBACtC,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAErD,IAAI,eAAe,YAAY,eAAe,EAAE;oBAC9C,qBAAqB,YAAY,eAAe;oBAChD,qBAAqB,YAAY,YAAY;gBAC/C,OAAO;oBACL,qBAAqB;oBACrB,qBAAqB;gBACvB;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,kCAAkC,aAAa,MAAM;gBAClE,qBAAqB;gBACrB,qBAAqB;YACvB;YAEA,8BAA8B;YAC9B,IAAI,YAAY,MAAM,KAAK,aAAa;gBACtC,MAAM,aAAa,YAAY,KAAK;gBACpC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAEpD,IAAI,cAAc,WAAW,eAAe,EAAE;oBAC5C,oBAAoB,WAAW,eAAe;oBAC9C,oBAAoB,WAAW,YAAY;gBAC7C,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;gBACtB;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,iCAAiC,YAAY,MAAM;gBAChE,oBAAoB;gBACpB,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,sEAAsE;YACtE,iCAAiC;YACjC,QAAQ,KAAK,CAAC,8CAA8C;YAC5D;QACF;IACF,GAAG;QAAC;QAAM;KAAmB;IAE7B,0CAA0C;IAC1C,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnD,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,QAAQ,EAAE,QAAQ,eAAe;QAE5E,IAAI;YACF,0BAA0B;YAC1B,MAAM,SAAgC,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;YAC7D,IAAI,UAAU,OAAO,eAAe,EAAE;gBACpC,qBAAqB,OAAO,eAAe;gBAC3C,qBAAqB,OAAO,YAAY,IAAI,IAAI,OAAO,WAAW,KAAK,8BAA8B;gBACrG,uBAAuB;gBACvB,qBAAqB,OAAO,2BAA2B;YACzD,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,qEAAqE;gBACrE,MAAM;YACR;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,MAAM,mDAAmD;QAC3D,SAAU;YACR,0BAA0B,QAAQ,gCAAgC;QACpE;IACF,GAAG,EAAE;IAEL,+CAA+C;IAC/C,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,IAAI,mBAAmB;YACrB,uBAAuB;YACvB,qBAAqB;QACvB;IACA,qEAAqE;IACvE,GAAG;QAAC;KAAkB;IAEtB,yCAAyC;IACzC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAClD,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,QAAQ,EAAE,QAAQ,eAAe;QAE5E,IAAI;YACF,yBAAyB;YACzB,MAAM,SAAgC,MAAM,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE;YAC5D,IAAI,UAAU,OAAO,eAAe,EAAE;gBACpC,oBAAoB,OAAO,eAAe;gBAC1C,oBAAoB,OAAO,YAAY,IAAI,IAAI,OAAO,WAAW,KAAK,8BAA8B;gBACpG,uBAAuB;gBACvB,qBAAqB,OAAO,2BAA2B;YACzD,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,MAAM;YACR;QACF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,MAAM,kDAAkD;QAC1D,SAAU;YACR,yBAAyB,QAAQ,gCAAgC;QACnE;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,kBAAkB;YACpB,uBAAuB;YACvB,qBAAqB;QACvB;IACA,qEAAqE;IACvE,GAAG;QAAC;KAAiB;IAErB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4885, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useOpuScannerIntegration.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from \"react\";\r\nimport { BusinessData } from \"@/types\";\r\nimport type { User } from \"@/contexts/AuthContext\";\r\nimport { toast } from 'react-toastify'; // Import toast for notifications\r\nimport { SelectedCompany } from \"@/app/opuscanner/types\"; // Add import for SelectedCompany type\r\n\r\n/**\r\n * Custom hook to manage OpuScanner integration\r\n */\r\nexport const useOpuScannerIntegration = (\r\n  user: User | null,\r\n  selectedBusiness: BusinessData | null,\r\n  fetchWithAuth: (url: string, options?: RequestInit) => Promise<Response>,\r\n  hasQuickAnalysis: boolean, // Add parameter\r\n  hasDeepAnalysis: boolean   // Add parameter\r\n) => {\r\n  // Helper function to handle 401 responses\r\n  const handleUnauthorized = (response: Response): boolean => {\r\n    if (response.status === 401) {\r\n      window.location.href = '/login';\r\n      return true;\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // State for OpuScanner integration\r\n  const [addToOpuScanner, setAddToOpuScanner] = useState<boolean>(false);\r\n  const [isCompanySelected, setIsCompanySelected] = useState<boolean>(false);\r\n  const [isAddingToOpuScanner, setIsAddingToOpuScanner] = useState<boolean>(false);\r\n  const currentBusinessIdRef = useRef<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (selectedBusiness?.place_id) {\r\n      currentBusinessIdRef.current = selectedBusiness.place_id;\r\n    } else {\r\n      currentBusinessIdRef.current = null;\r\n    }\r\n  }, [selectedBusiness?.place_id]);\r\n\r\n  // Check if the company is already selected in OpuScanner\r\n  const checkIfCompanyIsSelected = useCallback(async (placeId: string) => {\r\n    if (!user) return;\r\n\r\n    try {\r\n      const response = await fetchWithAuth('/api/selected-companies');\r\n\r\n        if (handleUnauthorized(response)) return;\r\n      if (!response.ok) {\r\n        throw new Error('Fehler beim Abrufen der ausgewählten Unternehmen');\r\n      }\r\n\r\n      const data = await response.json();\r\n      if (currentBusinessIdRef.current !== placeId) {\r\n        return;\r\n      }\r\n      const isSelected = data.companies.some((company: SelectedCompany) => company.place_id === placeId);\r\n\r\n      setIsCompanySelected(isSelected);\r\n      setAddToOpuScanner(isSelected); // Sync the toggle with the actual state\r\n\r\n    } catch (error) {\r\n      console.error('Fehler beim Prüfen des Unternehmensstatus:', error);\r\n    }\r\n  }, [user, fetchWithAuth]);\r\n\r\n  // Add the current company to OpuScanner\r\n  const addCompanyToOpuScanner = useCallback(async () => {\r\n    if (!selectedBusiness || !selectedBusiness.place_id || !user) return;\r\n\r\n    // --- Analysis Check ---\r\n    if (!hasQuickAnalysis && !hasDeepAnalysis) {\r\n      console.warn(\"Versuch, Unternehmen ohne Analyse zum OpuScanner hinzuzufügen.\");\r\n      toast.warn(\"Unternehmen kann nicht hinzugefügt werden: Mindestens eine Analyse (Schnell oder Tief) muss vorhanden sein.\", {\r\n        position: \"bottom-right\",\r\n        autoClose: 5000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true,\r\n        progress: undefined,\r\n      });\r\n      setAddToOpuScanner(false); // Reset the toggle immediately\r\n      return; // Stop execution\r\n    }\r\n    // --- End Analysis Check ---\r\n\r\n    setIsAddingToOpuScanner(true);\r\n\r\n    try {\r\n      const companyData = {\r\n        place_id: selectedBusiness.place_id,\r\n        name: selectedBusiness.name || 'Unbekanntes Unternehmen',\r\n        address: selectedBusiness.formatted_address || ''\r\n      };\r\n\r\n      const response = await fetchWithAuth('/api/selected-companies', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(companyData)\r\n      });\r\n\r\n        if (response.status === 401) {\r\n            window.location.href = '/login';\r\n            return;\r\n        }\r\n\r\n        if (!response.ok) {\r\n          throw new Error('Fehler beim Hinzufügen des Unternehmens zur Auswahl');\r\n        }\r\n\r\n      setIsCompanySelected(true);\r\n      console.log('Unternehmen erfolgreich zum OpuScanner hinzugefügt');\r\n\r\n    } catch (error) {\r\n      console.error('Fehler beim Hinzufügen des Unternehmens:', error);\r\n      setAddToOpuScanner(false); // Reset toggle on error\r\n    } finally {\r\n      setIsAddingToOpuScanner(false);\r\n    }\r\n  }, [selectedBusiness, user, fetchWithAuth, hasQuickAnalysis, hasDeepAnalysis]);\r\n\r\n  // Remove the current company from OpuScanner\r\n  const removeCompanyFromOpuScanner = useCallback(async () => {\r\n    if (!selectedBusiness || !selectedBusiness.place_id || !user) return;\r\n\r\n    setIsAddingToOpuScanner(true);\r\n\r\n    try {\r\n      const response = await fetchWithAuth(`/api/selected-companies?place_id=${selectedBusiness.place_id}`, {\r\n        method: 'DELETE'\r\n      });\r\n\r\n        if (response.status === 401) {\r\n            window.location.href = '/login';\r\n            return;\r\n        }\r\n\r\n        if (!response.ok) {\r\n          throw new Error('Fehler beim Entfernen des Unternehmens aus der Auswahl');\r\n        }\r\n\r\n      setIsCompanySelected(false);\r\n      console.log('Unternehmen erfolgreich aus dem OpuScanner entfernt');\r\n\r\n    } catch (error) {\r\n      console.error('Fehler beim Entfernen des Unternehmens:', error);\r\n      setAddToOpuScanner(true); // Reset toggle on error\r\n    } finally {\r\n      setIsAddingToOpuScanner(false);\r\n    }\r\n  }, [selectedBusiness, user, fetchWithAuth]);\r\n\r\n  // Update integration selection based on toggle and current selection\r\n  useEffect(() => {\r\n    // Only proceed if a business is selected and user exists\r\n    if (!selectedBusiness?.place_id || !user) {\r\n      return;\r\n    }\r\n\r\n    // Check analysis *before* deciding to add/remove\r\n    const canBeAdded = hasQuickAnalysis || hasDeepAnalysis;\r\n\r\n    if (addToOpuScanner && !isCompanySelected) {\r\n      // Attempting to add\r\n      if (canBeAdded) {\r\n        // Analysis exists, proceed with adding\r\n        addCompanyToOpuScanner();\r\n      } else {\r\n        // Analysis missing - this state ideally shouldn't be reached due to UI checks,\r\n        // but as a safeguard, log and reset the toggle.\r\n        console.warn(\r\n          \"Add attempt detected in useEffect despite missing analysis. Resetting toggle.\"\r\n        );\r\n        setAddToOpuScanner(false); // Force reset\r\n      }\r\n    } else if (!addToOpuScanner && isCompanySelected) {\r\n      // Attempting to remove - no analysis check needed for removal\r\n      removeCompanyFromOpuScanner();\r\n    }\r\n  }, [\r\n    addToOpuScanner,\r\n    selectedBusiness, // Depend on the whole object\r\n    user,\r\n    isCompanySelected,\r\n    hasQuickAnalysis, // Add analysis status to dependencies\r\n    hasDeepAnalysis, // Add analysis status to dependencies\r\n    addCompanyToOpuScanner, // Add callback dependency\r\n    removeCompanyFromOpuScanner, // Add callback dependency\r\n  ]);\r\n\r\n  // Check if the current business is already selected in OpuScanner\r\n  useEffect(() => {\r\n    setAddToOpuScanner(false);\r\n    setIsCompanySelected(false);\r\n    if (selectedBusiness?.place_id && user) {\r\n      checkIfCompanyIsSelected(selectedBusiness.place_id);\r\n    }\r\n  }, [selectedBusiness?.place_id, user, checkIfCompanyIsSelected]);\r\n\r\n  return {\r\n    addToOpuScanner,\r\n    setAddToOpuScanner,\r\n    isCompanySelected,\r\n    isAddingToOpuScanner,\r\n    checkIfCompanyIsSelected,\r\n    addCompanyToOpuScanner,\r\n    removeCompanyFromOpuScanner\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAGA,kQAAwC,iCAAiC;;;AAMlE,MAAM,2BAA2B,CACtC,MACA,kBACA,eACA,kBACA,gBAA2B,gBAAgB;;IAE3C,0CAA0C;IAC1C,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG;YACvB,OAAO;QACT;QACA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1E,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,UAAU;YAC9B,qBAAqB,OAAO,GAAG,iBAAiB,QAAQ;QAC1D,OAAO;YACL,qBAAqB,OAAO,GAAG;QACjC;IACF,GAAG;QAAC,kBAAkB;KAAS;IAE/B,yDAAyD;IACzD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAClD,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,cAAc;YAEnC,IAAI,mBAAmB,WAAW;YACpC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,qBAAqB,OAAO,KAAK,SAAS;gBAC5C;YACF;YACA,MAAM,aAAa,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC,UAA6B,QAAQ,QAAQ,KAAK;YAE1F,qBAAqB;YACrB,mBAAmB,aAAa,wCAAwC;QAE1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;QAC9D;IACF,GAAG;QAAC;QAAM;KAAc;IAExB,wCAAwC;IACxC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,QAAQ,IAAI,CAAC,MAAM;QAE9D,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB;YACzC,QAAQ,IAAI,CAAC;YACb,mJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,+GAA+G;gBACxH,UAAU;gBACV,WAAW;gBACX,iBAAiB;gBACjB,cAAc;gBACd,cAAc;gBACd,WAAW;gBACX,UAAU;YACZ;YACA,mBAAmB,QAAQ,+BAA+B;YAC1D,QAAQ,iBAAiB;QAC3B;QACA,6BAA6B;QAE7B,wBAAwB;QAExB,IAAI;YACF,MAAM,cAAc;gBAClB,UAAU,iBAAiB,QAAQ;gBACnC,MAAM,iBAAiB,IAAI,IAAI;gBAC/B,SAAS,iBAAiB,iBAAiB,IAAI;YACjD;YAEA,MAAM,WAAW,MAAM,cAAc,2BAA2B;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEE,IAAI,SAAS,MAAM,KAAK,KAAK;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEF,qBAAqB;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,mBAAmB,QAAQ,wBAAwB;QACrD,SAAU;YACR,wBAAwB;QAC1B;IACF,GAAG;QAAC;QAAkB;QAAM;QAAe;QAAkB;KAAgB;IAE7E,6CAA6C;IAC7C,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9C,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,QAAQ,IAAI,CAAC,MAAM;QAE9D,wBAAwB;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,EAAE,EAAE;gBACpG,QAAQ;YACV;YAEE,IAAI,SAAS,MAAM,KAAK,KAAK;gBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACvB;YACJ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEF,qBAAqB;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,mBAAmB,OAAO,wBAAwB;QACpD,SAAU;YACR,wBAAwB;QAC1B;IACF,GAAG;QAAC;QAAkB;QAAM;KAAc;IAE1C,qEAAqE;IACrE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,IAAI,CAAC,kBAAkB,YAAY,CAAC,MAAM;YACxC;QACF;QAEA,iDAAiD;QACjD,MAAM,aAAa,oBAAoB;QAEvC,IAAI,mBAAmB,CAAC,mBAAmB;YACzC,oBAAoB;YACpB,IAAI,YAAY;gBACd,uCAAuC;gBACvC;YACF,OAAO;gBACL,+EAA+E;gBAC/E,gDAAgD;gBAChD,QAAQ,IAAI,CACV;gBAEF,mBAAmB,QAAQ,cAAc;YAC3C;QACF,OAAO,IAAI,CAAC,mBAAmB,mBAAmB;YAChD,8DAA8D;YAC9D;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB,qBAAqB;QACrB,IAAI,kBAAkB,YAAY,MAAM;YACtC,yBAAyB,iBAAiB,QAAQ;QACpD;IACF,GAAG;QAAC,kBAAkB;QAAU;QAAM;KAAyB;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5083, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/index.ts"], "sourcesContent": ["export * from './useMapState';\r\nexport * from './useBusinessSelection';\r\nexport * from './useAnalysis';\r\nexport * from './useOpuScannerIntegration';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5110, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 5180, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/formatters.ts"], "sourcesContent": ["import { BusinessData } from \"@/types\";\r\n\r\n/**\r\n * Format date string for display in German locale\r\n */\r\nexport const formatAnalysisDate = (dateString: string | null): string => {\r\n  if (!dateString) return \"Unbekannt\";\r\n  try {\r\n    // Use German locale for formatting\r\n    return new Date(dateString).toLocaleDateString(\"de-DE\", {\r\n      year: \"numeric\",\r\n      month: \"long\",\r\n      day: \"numeric\",\r\n      hour: \"2-digit\",\r\n      minute: \"2-digit\",\r\n    });\r\n  } catch {\r\n    // Handle potential invalid date strings gracefully\r\n    return \"Ungültiges Datum\";\r\n  }\r\n};\r\n\r\n/**\r\n * Helper to get photo URL, providing a fallback\r\n */\r\nexport const getPhotoUrl = (business: BusinessData): string | undefined => {\r\n  if (\"photos\" in business && business.photos && business.photos.length > 0) {\r\n    // Request a reasonably sized image\r\n    return business.photos[0].getUrl?.({ maxWidth: 300, maxHeight: 300 });\r\n  }\r\n  // Fallback placeholder image\r\n  return \"https://via.placeholder.com/150\";\r\n};\r\n\r\n/**\r\n * Format opening hours into German weekday strings\r\n */\r\nexport const formatOpeningHours = (\r\n  periods: google.maps.places.OpeningHoursPeriod[] = []\r\n): string[] => {\r\n  const weekdays = [\r\n    \"Sonntag\",\r\n    \"Montag\",\r\n    \"Dienstag\",\r\n    \"Mittwoch\",\r\n    \"Donnerstag\",\r\n    \"Freitag\",\r\n    \"Samstag\",\r\n  ];\r\n  const grouped: Record<number, { open: string; close: string }[]> = {};\r\n  weekdays.forEach((_, day) => {\r\n    grouped[day] = [];\r\n  });\r\n  const formatPoint = (p: google.maps.places.OpeningHoursPoint | null): string =>\r\n    p\r\n      ? `${p.hour.toString().padStart(2, \"0\")}:${p.minute\r\n          .toString()\r\n          .padStart(2, \"0\")}`\r\n      : \"00:00\";\r\n\r\n  periods.forEach((period) => {\r\n    const { open, close } = period;\r\n    const openStr = formatPoint(open);\r\n    const closeStr = formatPoint(close || null);\r\n    grouped[open.day].push({ open: openStr, close: closeStr });\r\n  });\r\n  const result: string[] = [];\r\n  weekdays.forEach((weekday, day) => {\r\n    const dayPeriods = grouped[day];\r\n    if (dayPeriods.length === 0) {\r\n      result.push(`${weekday}: geschlossen`);\r\n    } else {\r\n      dayPeriods.sort((a, b) => a.open.localeCompare(b.open));\r\n      const ranges = dayPeriods.map((p) => `${p.open}–${p.close}`);\r\n      result.push(`${weekday}: ${ranges.join(\", \")}`);\r\n    }\r\n  });\r\n  return result;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAKO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IACxB,IAAI;QACF,mCAAmC;QACnC,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF,EAAE,OAAM;QACN,mDAAmD;QACnD,OAAO;IACT;AACF;AAKO,MAAM,cAAc,CAAC;IAC1B,IAAI,YAAY,YAAY,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,GAAG;QACzE,mCAAmC;QACnC,OAAO,SAAS,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG;YAAE,UAAU;YAAK,WAAW;QAAI;IACrE;IACA,6BAA6B;IAC7B,OAAO;AACT;AAKO,MAAM,qBAAqB,CAChC,UAAmD,EAAE;IAErD,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,UAA6D,CAAC;IACpE,SAAS,OAAO,CAAC,CAAC,GAAG;QACnB,OAAO,CAAC,IAAI,GAAG,EAAE;IACnB;IACA,MAAM,cAAc,CAAC,IACnB,IACI,GAAG,EAAE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,MAAM,CAC9C,QAAQ,GACR,QAAQ,CAAC,GAAG,MAAM,GACrB;IAEN,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;QACxB,MAAM,UAAU,YAAY;QAC5B,MAAM,WAAW,YAAY,SAAS;QACtC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;YAAE,MAAM;YAAS,OAAO;QAAS;IAC1D;IACA,MAAM,SAAmB,EAAE;IAC3B,SAAS,OAAO,CAAC,CAAC,SAAS;QACzB,MAAM,aAAa,OAAO,CAAC,IAAI;QAC/B,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,OAAO,IAAI,CAAC,GAAG,QAAQ,aAAa,CAAC;QACvC,OAAO;YACL,WAAW,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACrD,MAAM,SAAS,WAAW,GAAG,CAAC,CAAC,IAAM,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE;YAC3D,OAAO,IAAI,CAAC,GAAG,QAAQ,EAAE,EAAE,OAAO,IAAI,CAAC,OAAO;QAChD;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/index.ts"], "sourcesContent": ["export * from './formatters';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 5273, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/BusinessCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { motion } from 'framer-motion';\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { BusinessData } from \"@/types\";\r\nimport { getPhotoUrl } from \"@/utils\";\r\nimport { formatOpeningHours } from \"@/utils/formatters\";\r\nimport { MapPin, Phone, Globe, Clock, Info, Building } from 'lucide-react';\r\n\r\ninterface BusinessCardProps {\r\n  selectedBusiness: BusinessData | null;\r\n  isFlipped: boolean;\r\n  toggleFlip: () => void;\r\n}\r\n\r\n/**\r\n * Revolutionary Business Card with morphing design\r\n */\r\nconst BusinessCard: React.FC<BusinessCardProps> = ({\r\n  selectedBusiness,\r\n  isFlipped,\r\n  toggleFlip\r\n}) => {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  if (!selectedBusiness) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20, scale: 0.95 }}\r\n        animate={{ opacity: 1, y: 0, scale: 1 }}\r\n        transition={{ duration: 0.6, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n        className=\"relative group w-full h-[350px] md:h-[400px]\"\r\n      >\r\n        <div className=\"relative h-full bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-md rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 flex flex-col items-center justify-center\">\r\n          {/* Floating decorative elements */}\r\n          <div className=\"absolute inset-0 overflow-hidden\">\r\n            <motion.div\r\n              className=\"absolute top-4 right-4 w-20 h-20 rounded-full bg-gradient-to-br from-primary/10 to-primary/5\"\r\n              animate={{ rotate: 360, scale: [1, 1.1, 1] }}\r\n              transition={{ duration: 20, repeat: Infinity }}\r\n            />\r\n            <motion.div\r\n              className=\"absolute bottom-6 left-6 w-16 h-16 rounded-full bg-gradient-to-br from-accent/10 to-accent/5\"\r\n              animate={{ rotate: -360, scale: [1, 0.9, 1] }}\r\n              transition={{ duration: 25, repeat: Infinity }}\r\n            />\r\n          </div>\r\n\r\n          {/* Pulsing location icon */}\r\n          <motion.div\r\n            className=\"w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mb-4 relative overflow-hidden\"\r\n            animate={{ scale: [1, 1.05, 1] }}\r\n            transition={{ duration: 3, repeat: Infinity }}\r\n          >\r\n            <MapPin className=\"w-8 h-8 text-primary\" />\r\n            <motion.div\r\n              className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent\"\r\n              animate={{ x: ['-100%', '200%'] }}\r\n              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\r\n            />\r\n          </motion.div>\r\n\r\n          <motion.h3 \r\n            className=\"text-lg font-semibold text-muted-foreground mb-2 text-center\"\r\n            animate={{ opacity: [0.7, 1, 0.7] }}\r\n            transition={{ duration: 2, repeat: Infinity }}\r\n          >\r\n            Wählen Sie ein Unternehmen\r\n          </motion.h3>\r\n          <p className=\"text-sm text-muted-foreground/80 text-center px-4\">\r\n            Klicken Sie auf der Karte oder auf ein POI, um Unternehmensinformationen anzuzeigen.\r\n          </p>\r\n\r\n          {/* Animated border */}\r\n          <motion.div\r\n            className=\"absolute inset-0 rounded-2xl border-2 border-primary/20\"\r\n            animate={{ borderColor: ['hsl(var(--primary) / 0.2)', 'hsl(var(--primary) / 0.4)', 'hsl(var(--primary) / 0.2)'] }}\r\n            transition={{ duration: 3, repeat: Infinity }}\r\n          />\r\n        </div>\r\n      </motion.div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20, scale: 0.95 }}\r\n      animate={{ opacity: 1, y: 0, scale: 1 }}\r\n      transition={{ duration: 0.6, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n      className=\"relative group w-full h-[350px] md:h-[400px] cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-lg\"\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n      onClick={toggleFlip}\r\n      onKeyDown={(e) => {\r\n        if (e.key === 'Enter' || e.key === ' ') {\r\n          e.preventDefault();\r\n          toggleFlip();\r\n        }\r\n      }}\r\n      tabIndex={0}\r\n      role=\"button\"\r\n      aria-pressed={isFlipped}\r\n    >\r\n      <div className=\"relative h-full\" style={{ perspective: '1000px' }}>\r\n        <motion.div\r\n          className=\"relative w-full h-full\"\r\n          style={{ transformStyle: 'preserve-3d' }}\r\n          animate={{ rotateY: isFlipped ? 180 : 0 }}\r\n          transition={{ duration: 0.7, ease: \"easeInOut\" }}\r\n        >\r\n          {/* Front Side */}\r\n          <div className=\"absolute inset-0 w-full h-full\" style={{ backfaceVisibility: 'hidden' }}>\r\n            <div className=\"relative h-full bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-primary/30\">\r\n              {/* Accent gradient on hover */}\r\n              <motion.div\r\n                className=\"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\r\n                animate={isHovered ? { opacity: 0.1 } : { opacity: 0 }}\r\n              />\r\n\r\n              {/* Floating decorative elements */}\r\n              <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n                <motion.div\r\n                  className=\"absolute top-4 right-4 w-12 h-12 rounded-full bg-gradient-to-br from-primary/10 to-primary/5\"\r\n                  animate={{ rotate: 360 }}\r\n                  transition={{ duration: 15, repeat: Infinity }}\r\n                />\r\n                <motion.div\r\n                  className=\"absolute bottom-4 left-4 w-8 h-8 rounded-full bg-gradient-to-br from-accent/10 to-accent/5\"\r\n                  animate={{ rotate: -360 }}\r\n                  transition={{ duration: 20, repeat: Infinity }}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"relative p-6 h-full flex flex-col\">\r\n                {/* Header with business type indicator */}\r\n                <div className=\"flex items-start justify-between mb-4\">\r\n                  <div className=\"flex-1\">\r\n                    <motion.h2 \r\n                      className=\"text-xl font-bold text-foreground leading-tight mb-2\"\r\n                      layoutId={`business-name-${selectedBusiness.place_id}`}\r\n                    >\r\n                      {selectedBusiness.name}\r\n                    </motion.h2>\r\n                    <div className=\"flex items-center text-sm text-muted-foreground\">\r\n                      <MapPin className=\"w-4 h-4 mr-1 text-primary\" />\r\n                      <span className=\"line-clamp-2\">{selectedBusiness.formatted_address}</span>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {/* Business type badge */}\r\n                  <motion.div\r\n                    className=\"px-2 py-1 bg-primary/10 rounded-full text-xs font-medium text-primary\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                  >\r\n                    <Building className=\"w-3 h-3 inline mr-1\" />\r\n                    Business\r\n                  </motion.div>\r\n                </div>\r\n\r\n                {/* Image section with enhanced styling */}\r\n                {((\"photos\" in selectedBusiness && selectedBusiness.photos && selectedBusiness.photos.length > 0) ||\r\n                  (\"description\" in selectedBusiness && selectedBusiness.description)) && (\r\n                    <motion.div \r\n                      className=\"relative mb-4 flex justify-center\"\r\n                      whileHover={{ scale: 1.02 }}\r\n                      transition={{ type: \"spring\", stiffness: 300 }}\r\n                    >\r\n                      <div className=\"relative w-32 h-32 rounded-xl overflow-hidden border-2 border-border/30 shadow-lg\">\r\n                        <Image\r\n                          src={getPhotoUrl(selectedBusiness) || \"https://via.placeholder.com/128\"}\r\n                          alt={selectedBusiness.name || \"Business image\"}\r\n                          width={128}\r\n                          height={128}\r\n                          className=\"object-cover w-full h-full\"\r\n                          unoptimized={getPhotoUrl(selectedBusiness)?.startsWith(\"https://via.placeholder.com\")}\r\n                        />\r\n                        <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n\r\n                {/* Quick info indicators */}\r\n                <div className=\"flex-1 flex flex-col justify-end\">\r\n                  <div className=\"grid grid-cols-2 gap-3 mb-4\">\r\n                    {selectedBusiness.formatted_phone_number && (\r\n                      <div className=\"flex items-center text-xs text-muted-foreground\">\r\n                        <Phone className=\"w-3 h-3 mr-1 text-primary\" />\r\n                        <span>Telefon</span>\r\n                      </div>\r\n                    )}\r\n                    {selectedBusiness.website && (\r\n                      <div className=\"flex items-center text-xs text-muted-foreground\">\r\n                        <Globe className=\"w-3 h-3 mr-1 text-primary\" />\r\n                        <span>Website</span>\r\n                      </div>\r\n                    )}\r\n                    {\"opening_hours\" in selectedBusiness && selectedBusiness.opening_hours && (\r\n                      <div className=\"flex items-center text-xs text-muted-foreground\">\r\n                        <Clock className=\"w-3 h-3 mr-1 text-primary\" />\r\n                        <span>Öffnungszeiten</span>\r\n                      </div>\r\n                    )}\r\n                    {\"description\" in selectedBusiness && selectedBusiness.description && (\r\n                      <div className=\"flex items-center text-xs text-muted-foreground\">\r\n                        <Info className=\"w-3 h-3 mr-1 text-primary\" />\r\n                        <span>Details</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Interaction hint */}\r\n                  <motion.div \r\n                    className=\"text-center py-2 px-4 bg-primary/5 rounded-lg border border-primary/20\"\r\n                    animate={{ opacity: [0.7, 1, 0.7] }}\r\n                    transition={{ duration: 2, repeat: Infinity }}\r\n                  >\r\n                    <span className=\"text-xs font-medium text-primary\">\r\n                      Klicken für Details →\r\n                    </span>\r\n                  </motion.div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Back Side */}\r\n          <div className=\"absolute inset-0 w-full h-full\" style={{ backfaceVisibility: 'hidden', transform: 'rotateY(180deg)' }}>\r\n            <div className=\"relative h-full bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden\">\r\n              {/* Enhanced gradient overlay */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-50\" />\r\n\r\n              <ScrollArea className=\"h-full p-6\">\r\n                <div className=\"space-y-6\">\r\n                  {/* Header */}\r\n                  <div className=\"text-center border-b border-border/30 pb-4\">\r\n                    <h3 className=\"text-lg font-bold text-foreground mb-1\">\r\n                      {selectedBusiness.name}\r\n                    </h3>\r\n                    <p className=\"text-sm text-muted-foreground\">Detailinformationen</p>\r\n                  </div>\r\n\r\n                  {/* Contact Information */}\r\n                  <div className=\"space-y-4\">\r\n                    <h4 className=\"text-sm font-semibold text-foreground flex items-center\">\r\n                      <Phone className=\"w-4 h-4 mr-2 text-primary\" />\r\n                      Kontakt\r\n                    </h4>\r\n                    \r\n                    <div className=\"space-y-3 pl-6\">\r\n                      <div className=\"flex items-start\">\r\n                        <span className=\"text-xs font-medium text-muted-foreground w-16\">Telefon:</span>\r\n                        <span className=\"text-xs text-foreground\">\r\n                          {selectedBusiness.formatted_phone_number || \"Nicht verfügbar\"}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex items-start\">\r\n                        <span className=\"text-xs font-medium text-muted-foreground w-16\">Website:</span>\r\n                        {selectedBusiness.website ? (\r\n                          <a\r\n                            href={selectedBusiness.website}\r\n                            target=\"_blank\"\r\n                            rel=\"noopener noreferrer\"\r\n                            className=\"text-xs text-primary hover:text-primary/80 underline\"\r\n                            onClick={(e) => e.stopPropagation()}\r\n                          >\r\n                            Zur Website →\r\n                          </a>\r\n                        ) : (\r\n                          <span className=\"text-xs text-muted-foreground\">Nicht verfügbar</span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Opening Hours */}\r\n                  {\"opening_hours\" in selectedBusiness && selectedBusiness.opening_hours && (\r\n                    <div className=\"space-y-4\">\r\n                      <h4 className=\"text-sm font-semibold text-foreground flex items-center\">\r\n                        <Clock className=\"w-4 h-4 mr-2 text-primary\" />\r\n                        Öffnungszeiten\r\n                      </h4>\r\n                      <div className=\"space-y-2 pl-6\">\r\n                        {(selectedBusiness.opening_hours.weekday_text && selectedBusiness.opening_hours.weekday_text.length > 0\r\n                          ? selectedBusiness.opening_hours.weekday_text\r\n                          : formatOpeningHours(selectedBusiness.opening_hours.periods ?? [])\r\n                        ).map((day, index) => (\r\n                          <div key={index} className=\"text-xs text-muted-foreground leading-relaxed\">\r\n                            {day}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Description */}\r\n                  {\"description\" in selectedBusiness && selectedBusiness.description && (\r\n                    <div className=\"space-y-4\">\r\n                      <h4 className=\"text-sm font-semibold text-foreground flex items-center\">\r\n                        <Info className=\"w-4 h-4 mr-2 text-primary\" />\r\n                        Beschreibung\r\n                      </h4>\r\n                      <p className=\"text-xs text-muted-foreground leading-relaxed pl-6\">\r\n                        {selectedBusiness.description}\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Back button */}\r\n                  <motion.div \r\n                    className=\"text-center py-2 px-4 bg-primary/5 rounded-lg border border-primary/20 mt-6\"\r\n                    animate={{ opacity: [0.7, 1, 0.7] }}\r\n                    transition={{ duration: 2, repeat: Infinity }}\r\n                  >\r\n                    <span className=\"text-xs font-medium text-primary\">\r\n                      ← Zurück zur Übersicht\r\n                    </span>\r\n                  </motion.div>\r\n                </div>\r\n              </ScrollArea>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Hover border effect */}\r\n      <motion.div\r\n        className=\"absolute inset-0 rounded-2xl border-2 border-primary/0 pointer-events-none\"\r\n        animate={isHovered ? { borderColor: 'hsl(var(--primary) / 0.3)' } : { borderColor: 'hsl(var(--primary) / 0)' }}\r\n        transition={{ duration: 0.3 }}\r\n      />\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default BusinessCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAiBA;;CAEC,GACD,MAAM,eAA4C,CAAC,EACjD,gBAAgB,EAChB,SAAS,EACT,UAAU,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,CAAC,kBAAkB;QACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAK;YAC1C,SAAS;gBAAE,SAAS;gBAAG,GAAG;gBAAG,OAAO;YAAE;YACtC,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAM,CAAC;oBAAM;oBAAO;iBAAK;YAAC;YAC9D,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,QAAQ;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;0CAE/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,QAAQ,CAAC;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC5C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;;;;;;;kCAKjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;gCAAC;gCAAG;gCAAM;6BAAE;wBAAC;wBAC/B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;0CAE5C,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;wCAAC;wCAAS;qCAAO;gCAAC;gCAChC,YAAY;oCAAE,UAAU;oCAAG,QAAQ;oCAAU,aAAa;gCAAE;;;;;;;;;;;;kCAIhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBAAC;wBAClC,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAC7C;;;;;;kCAGD,8OAAC;wBAAE,WAAU;kCAAoD;;;;;;kCAKjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,aAAa;gCAAC;gCAA6B;gCAA6B;6BAA4B;wBAAC;wBAChH,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;;IAKtD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAK;QAC1C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAM,CAAC;gBAAM;gBAAO;aAAK;QAAC;QAC9D,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;QACjC,SAAS;QACT,WAAW,CAAC;YACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;gBACtC,EAAE,cAAc;gBAChB;YACF;QACF;QACA,UAAU;QACV,MAAK;QACL,gBAAc;;0BAEd,8OAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,aAAa;gBAAS;0BAC9D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBAAE,gBAAgB;oBAAc;oBACvC,SAAS;wBAAE,SAAS,YAAY,MAAM;oBAAE;oBACxC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;;sCAG/C,8OAAC;4BAAI,WAAU;4BAAiC,OAAO;gCAAE,oBAAoB;4BAAS;sCACpF,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS,YAAY;4CAAE,SAAS;wCAAI,IAAI;4CAAE,SAAS;wCAAE;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAI,QAAQ;gDAAS;;;;;;0DAE/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ,CAAC;gDAAI;gDACxB,YAAY;oDAAE,UAAU;oDAAI,QAAQ;gDAAS;;;;;;;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,WAAU;gEACV,UAAU,CAAC,cAAc,EAAE,iBAAiB,QAAQ,EAAE;0EAErD,iBAAiB,IAAI;;;;;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFAAgB,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;kEAKtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;;0EAE1B,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;;;;;;;4CAM/C,CAAC,AAAC,YAAY,oBAAoB,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,CAAC,MAAM,GAAG,KAC5F,iBAAiB,oBAAoB,iBAAiB,WAAW,AAAC,mBACjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,YAAY;oDAAE,MAAM;oDAAU,WAAW;gDAAI;0DAE7C,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE,qBAAqB;4DACtC,KAAK,iBAAiB,IAAI,IAAI;4DAC9B,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,aAAa,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB,WAAW;;;;;;sEAEzD,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAMvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,iBAAiB,sBAAsB,kBACtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAK;;;;;;;;;;;;4DAGT,iBAAiB,OAAO,kBACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAK;;;;;;;;;;;;4DAGT,mBAAmB,oBAAoB,iBAAiB,aAAa,kBACpE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;kFAAK;;;;;;;;;;;;4DAGT,iBAAiB,oBAAoB,iBAAiB,WAAW,kBAChE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAMZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,SAAS;gEAAC;gEAAK;gEAAG;6DAAI;wDAAC;wDAClC,YAAY;4DAAE,UAAU;4DAAG,QAAQ;wDAAS;kEAE5C,cAAA,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU7D,8OAAC;4BAAI,WAAU;4BAAiC,OAAO;gCAAE,oBAAoB;gCAAU,WAAW;4BAAkB;sCAClH,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC,0IAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,iBAAiB,IAAI;;;;;;sEAExB,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAI/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAA8B;;;;;;;sEAIjD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAiD;;;;;;sFACjE,8OAAC;4EAAK,WAAU;sFACb,iBAAiB,sBAAsB,IAAI;;;;;;;;;;;;8EAIhD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAiD;;;;;;wEAChE,iBAAiB,OAAO,iBACvB,8OAAC;4EACC,MAAM,iBAAiB,OAAO;4EAC9B,QAAO;4EACP,KAAI;4EACJ,WAAU;4EACV,SAAS,CAAC,IAAM,EAAE,eAAe;sFAClC;;;;;iGAID,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;;gDAOvD,mBAAmB,oBAAoB,iBAAiB,aAAa,kBACpE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAA8B;;;;;;;sEAGjD,8OAAC;4DAAI,WAAU;sEACZ,CAAC,iBAAiB,aAAa,CAAC,YAAY,IAAI,iBAAiB,aAAa,CAAC,YAAY,CAAC,MAAM,GAAG,IAClG,iBAAiB,aAAa,CAAC,YAAY,GAC3C,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB,aAAa,CAAC,OAAO,IAAI,EAAE,CACnE,EAAE,GAAG,CAAC,CAAC,KAAK,sBACV,8OAAC;oEAAgB,WAAU;8EACxB;mEADO;;;;;;;;;;;;;;;;gDASjB,iBAAiB,oBAAoB,iBAAiB,WAAW,kBAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAA8B;;;;;;;sEAGhD,8OAAC;4DAAE,WAAU;sEACV,iBAAiB,WAAW;;;;;;;;;;;;8DAMnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;4DAAC;4DAAK;4DAAG;yDAAI;oDAAC;oDAClC,YAAY;wDAAE,UAAU;wDAAG,QAAQ;oDAAS;8DAE5C,cAAA,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS,YAAY;oBAAE,aAAa;gBAA4B,IAAI;oBAAE,aAAa;gBAA0B;gBAC7G,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 6159, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from \"react\";\r\nimport { motion } from 'framer-motion';\r\nimport { BusinessData } from \"@/types\";\r\nimport { formatAnalysisDate } from \"@/utils\";\r\nimport { Eye, Calendar, RefreshCw, ArrowRight } from 'lucide-react';\r\n\r\nexport interface AnalysisCardProps {\r\n  title: string;\r\n  description: string;\r\n  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;\r\n  analysisData: string | null;\r\n  analysisDate: string | null;\r\n  isLoading: boolean;\r\n  onCreateAnalysis: () => void;\r\n  onViewAnalysis: () => void;\r\n  selectedBusiness: BusinessData | null;\r\n  accentColor: string;\r\n  delay: number;\r\n}\r\n\r\n/**\r\n * Individual Analysis Card with morphing design\r\n */\r\nconst AnalysisCard: React.FC<AnalysisCardProps> = ({\r\n  title,\r\n  description,\r\n  icon: Icon,\r\n  analysisData,\r\n  analysisDate,\r\n  isLoading,\r\n  onCreateAnalysis,\r\n  onViewAnalysis,\r\n  selectedBusiness,\r\n  accentColor,\r\n  delay\r\n}) => {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const hasData = Boolean(analysisData);\r\n  const isDisabled = !selectedBusiness || isLoading;\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 50, rotateY: -15 }}\r\n      animate={{ opacity: 1, y: 0, rotateY: 0 }}\r\n      transition={{ duration: 0.8, delay, ease: [0.68, -0.55, 0.265, 1.55] }}\r\n      className=\"group relative overflow-hidden\"\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n    >\r\n      <div className=\"relative bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60 h-full min-h-[340px] flex flex-col\">\r\n        {/* Accent Border & Background */}\r\n        <motion.div \r\n          className=\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-2xl\"\r\n          style={{ background: `linear-gradient(135deg, ${accentColor}20, transparent)` }}\r\n          animate={isHovered ? { opacity: 0.15 } : { opacity: 0 }}\r\n        />\r\n        \r\n        {/* Floating decorative elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          <motion.div\r\n            className=\"absolute top-4 right-4 w-12 h-12 rounded-full\"\r\n            style={{ backgroundColor: `${accentColor}10` }}\r\n            animate={{ rotate: 360, scale: [1, 1.1, 1] }}\r\n            transition={{ duration: 20, repeat: Infinity }}\r\n          />\r\n          <motion.div\r\n            className=\"absolute bottom-6 left-6 w-8 h-8 rounded-full\"\r\n            style={{ backgroundColor: `${accentColor}05` }}\r\n            animate={{ rotate: -360, scale: [1, 0.9, 1] }}\r\n            transition={{ duration: 25, repeat: Infinity }}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"relative p-6 h-full flex flex-col\">\r\n          {/* Icon with special animation */}\r\n          <motion.div \r\n            className=\"w-14 h-14 rounded-xl flex items-center justify-center mb-4 relative overflow-hidden flex-shrink-0\"\r\n            style={{ backgroundColor: `${accentColor}15` }}\r\n            whileHover={{ scale: 1.1, rotate: 5 }}\r\n            transition={{ type: \"spring\", stiffness: 300 }}\r\n          >\r\n            <Icon \r\n              className=\"w-7 h-7 transition-colors duration-300\" \r\n              style={{ color: accentColor }}\r\n            />\r\n            <motion.div\r\n              className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent\"\r\n              animate={{ x: ['-100%', '200%'] }}\r\n              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\r\n            />\r\n          </motion.div>\r\n\r\n          {/* Title & Description */}\r\n          <h3 className=\"text-xl font-bold mb-3 text-foreground group-hover:text-opacity-90 transition-colors flex-shrink-0\">\r\n            {title}\r\n          </h3>\r\n          \r\n          <p className=\"text-sm text-muted-foreground mb-4 leading-relaxed flex-shrink-0\">\r\n            {description}\r\n          </p>\r\n\r\n          {/* Status Section */}\r\n          <div className=\"flex-1 flex flex-col justify-between\">\r\n            {/* Date Information */}\r\n            <div className=\"mb-4\">\r\n              <div className=\"flex items-center text-xs text-muted-foreground mb-2\">\r\n                <Calendar className=\"w-3 h-3 mr-2\" style={{ color: accentColor }} />\r\n                <span>Letzte Analyse:</span>\r\n              </div>\r\n              <div className=\"text-sm font-medium text-foreground\">\r\n                {analysisDate ? formatAnalysisDate(analysisDate) : \"Noch keine Analyse\"}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"space-y-3\">\r\n              {/* Create New Analysis Button */}\r\n              <motion.button\r\n                onClick={onCreateAnalysis}\r\n                disabled={isDisabled}\r\n                aria-label={isLoading ? 'Analyse wird durchgeführt...' : isDisabled ? 'Analyse nicht verfügbar' : 'Neue Analyse starten'}\r\n                className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium text-sm transition-all duration-300 ${\r\n                  isDisabled \r\n                    ? \"opacity-50 cursor-not-allowed bg-muted text-muted-foreground\" \r\n                    : \"text-white shadow-lg hover:shadow-xl hover:scale-[1.02]\"\r\n                }`}\r\n                style={{ \r\n                  backgroundColor: isDisabled ? undefined : accentColor,\r\n                  boxShadow: isDisabled ? undefined : `0 4px 20px ${accentColor}30`\r\n                }}\r\n                whileHover={!isDisabled ? { scale: 1.02 } : {}}\r\n                whileTap={!isDisabled ? { scale: 0.98 } : {}}\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <motion.div\r\n                      className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full mr-2\"\r\n                      animate={{ rotate: 360 }}\r\n                      transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n                    />\r\n                    <span>Analysiere...</span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <RefreshCw className=\"w-4 h-4 mr-2\" />\r\n                    <span>Neue Analyse</span>\r\n                  </>\r\n                )}\r\n              </motion.button>\r\n\r\n              {/* View Analysis Button */}\r\n              {hasData && (\r\n                <motion.button\r\n                  onClick={onViewAnalysis}\r\n                  disabled={isLoading}\r\n                  initial={{ opacity: 0, height: 0 }}\r\n                  animate={{ opacity: 1, height: 'auto' }}\r\n                  className=\"w-full flex items-center justify-center px-4 py-2 rounded-lg border-2 font-medium text-sm transition-all duration-300 hover:scale-[1.02] disabled:opacity-50\"\r\n                  style={{ \r\n                    borderColor: accentColor,\r\n                    color: accentColor,\r\n                    backgroundColor: `${accentColor}10`\r\n                  }}\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <Eye className=\"w-4 h-4 mr-2\" />\r\n                  <span>Analyse ansehen</span>\r\n                  <ArrowRight className=\"w-3 h-3 ml-2\" />\r\n                </motion.button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Status Indicator */}\r\n            <motion.div \r\n              className=\"mt-4 p-3 rounded-lg border\"\r\n              style={{ \r\n                borderColor: hasData ? `${accentColor}30` : '#d1d5db30',\r\n                backgroundColor: hasData ? `${accentColor}05` : '#f9fafb05'\r\n              }}\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <motion.div\r\n                  className=\"w-2 h-2 rounded-full mr-2\"\r\n                  style={{ backgroundColor: hasData ? accentColor : '#9ca3af' }}\r\n                  animate={{ scale: hasData ? [1, 1.2, 1] : 1 }}\r\n                  transition={{ duration: 2, repeat: hasData ? Infinity : 0 }}\r\n                />\r\n                <span className=\"text-xs font-medium\" style={{ color: hasData ? accentColor : '#9ca3af' }}>\r\n                  {hasData ? \"Daten verfügbar\" : \"Keine Daten\"}\r\n                </span>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hover border effect */}\r\n      <motion.div\r\n        className=\"absolute inset-0 rounded-2xl border-2 pointer-events-none\"\r\n        style={{ borderColor: 'transparent' }}\r\n        animate={isHovered ? { borderColor: `${accentColor}40` } : { borderColor: 'transparent' }}\r\n        transition={{ duration: 0.3 }}\r\n      />\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default AnalysisCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;AAsBA;;CAEC,GACD,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,WAAW,EACX,MAAM,IAAI,EACV,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,WAAW,EACX,KAAK,EACN;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,QAAQ;IACxB,MAAM,aAAa,CAAC,oBAAoB;IAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,SAAS,CAAC;QAAG;QAC3C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,SAAS;QAAE;QACxC,YAAY;YAAE,UAAU;YAAK;YAAO,MAAM;gBAAC;gBAAM,CAAC;gBAAM;gBAAO;aAAK;QAAC;QACrE,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,YAAY,CAAC,wBAAwB,EAAE,YAAY,gBAAgB,CAAC;wBAAC;wBAC9E,SAAS,YAAY;4BAAE,SAAS;wBAAK,IAAI;4BAAE,SAAS;wBAAE;;;;;;kCAIxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,iBAAiB,GAAG,YAAY,EAAE,CAAC;gCAAC;gCAC7C,SAAS;oCAAE,QAAQ;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;0CAE/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,iBAAiB,GAAG,YAAY,EAAE,CAAC;gCAAC;gCAC7C,SAAS;oCAAE,QAAQ,CAAC;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC5C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,iBAAiB,GAAG,YAAY,EAAE,CAAC;gCAAC;gCAC7C,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,MAAM;oCAAU,WAAW;gCAAI;;kDAE7C,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAAY;;;;;;kDAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;gDAAC;gDAAS;6CAAO;wCAAC;wCAChC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,aAAa;wCAAE;;;;;;;;;;;;0CAKhE,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAGH,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAe,OAAO;4DAAE,OAAO;wDAAY;;;;;;kEAC/D,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;0DACZ,eAAe,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;;;;;;;;;;;;kDAKvD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS;gDACT,UAAU;gDACV,cAAY,YAAY,iCAAiC,aAAa,4BAA4B;gDAClG,WAAW,CAAC,6GAA6G,EACvH,aACI,iEACA,2DACJ;gDACF,OAAO;oDACL,iBAAiB,aAAa,YAAY;oDAC1C,WAAW,aAAa,YAAY,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;gDACnE;gDACA,YAAY,CAAC,aAAa;oDAAE,OAAO;gDAAK,IAAI,CAAC;gDAC7C,UAAU,CAAC,aAAa;oDAAE,OAAO;gDAAK,IAAI,CAAC;0DAE1C,0BACC;;sEACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,QAAQ;4DAAI;4DACvB,YAAY;gEAAE,UAAU;gEAAG,QAAQ;gEAAU,MAAM;4DAAS;;;;;;sEAE9D,8OAAC;sEAAK;;;;;;;iFAGR;;sEACE,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;sEAAK;;;;;;;;;;;;;4CAMX,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS;gDACT,UAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDACjC,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAO;gDACtC,WAAU;gDACV,OAAO;oDACL,aAAa;oDACb,OAAO;oDACP,iBAAiB,GAAG,YAAY,EAAE,CAAC;gDACrC;gDACA,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CACL,aAAa,UAAU,GAAG,YAAY,EAAE,CAAC,GAAG;4CAC5C,iBAAiB,UAAU,GAAG,YAAY,EAAE,CAAC,GAAG;wCAClD;kDAEA,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,OAAO;wDAAE,iBAAiB,UAAU,cAAc;oDAAU;oDAC5D,SAAS;wDAAE,OAAO,UAAU;4DAAC;4DAAG;4DAAK;yDAAE,GAAG;oDAAE;oDAC5C,YAAY;wDAAE,UAAU;wDAAG,QAAQ,UAAU,WAAW;oDAAE;;;;;;8DAE5D,8OAAC;oDAAK,WAAU;oDAAsB,OAAO;wDAAE,OAAO,UAAU,cAAc;oDAAU;8DACrF,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,aAAa;gBAAc;gBACpC,SAAS,YAAY;oBAAE,aAAa,GAAG,YAAY,EAAE,CAAC;gBAAC,IAAI;oBAAE,aAAa;gBAAc;gBACxF,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 6617, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from \"react\";\r\nimport { motion } from 'framer-motion';\r\nimport { BusinessData } from \"@/types\";\r\nimport { Zap, Brain } from 'lucide-react';\r\nimport AnalysisCard from './AnalysisCard';\r\n\r\ninterface AnalysisSectionProps {\r\n  selectedBusiness: BusinessData | null;\r\n  quickAnalysisData: string | null;\r\n  quickAnalysisDate: string | null;\r\n  isQuickAnalysisLoading: boolean;\r\n  deepAnalysisData: string | null;\r\n  deepAnalysisDate: string | null;\r\n  isDeepAnalysisLoading: boolean;\r\n  handleCreateQuickAnalysis: (business: BusinessData) => void;\r\n  handleViewQuickAnalysis: () => void;\r\n  handleCreateDeepAnalysis: (business: BusinessData) => void;\r\n  handleViewDeepAnalysis: () => void;\r\n}\r\n\r\n\r\n\r\n/**\r\n * Analysis section component with morphing feature cards\r\n */\r\nconst AnalysisSection: React.FC<AnalysisSectionProps> = ({\r\n  selectedBusiness,\r\n  quickAnalysisData,\r\n  quickAnalysisDate,\r\n  isQuickAnalysisLoading,\r\n  deepAnalysisData,\r\n  deepAnalysisDate,\r\n  isDeepAnalysisLoading,\r\n  handleCreateQuickAnalysis,\r\n  handleViewQuickAnalysis,\r\n  handleCreateDeepAnalysis,\r\n  handleViewDeepAnalysis\r\n}) => {\r\n  return (\r\n    <motion.div \r\n      className=\"w-full\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 0.6 }}\r\n    >\r\n      {/* Section Header */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6 }}\r\n        className=\"text-center mb-8\"\r\n      >\r\n        <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 mb-4\">\r\n          <Brain className=\"w-4 h-4 text-primary mr-2\" />\r\n          <span className=\"font-semibold text-primary text-sm\">\r\n            KI-gestützte Analyse\r\n          </span>\r\n        </div>\r\n        <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\r\n          Intelligente <span className=\"text-primary\">Geschäftsanalyse</span>\r\n        </h2>\r\n        <p className=\"text-muted-foreground\">\r\n          Lassen Sie unsere KI maßgeschneiderte Kooperationsmöglichkeiten für Ihr ausgewähltes Unternehmen identifizieren\r\n        </p>\r\n      </motion.div>\r\n\r\n      {/* Analysis Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto\">\r\n        {/* Quick Analysis Card */}\r\n        <AnalysisCard\r\n          title=\"Schnelle Analyse\"\r\n          description=\"Erste Einschätzung der Kooperationsmöglichkeiten und strategischen Potenziale in wenigen Sekunden.\"\r\n          icon={Zap}\r\n          analysisData={quickAnalysisData}\r\n          analysisDate={quickAnalysisDate}\r\n          isLoading={isQuickAnalysisLoading}\r\n          onCreateAnalysis={() => selectedBusiness && handleCreateQuickAnalysis(selectedBusiness)}\r\n          onViewAnalysis={handleViewQuickAnalysis}\r\n          selectedBusiness={selectedBusiness}\r\n          accentColor=\"#3b82f6\"\r\n          delay={0.1}\r\n        />\r\n\r\n        {/* Deep Analysis Card */}\r\n        <AnalysisCard\r\n          title=\"Tiefe Analyse\"\r\n          description=\"Umfassende strategische Bewertung mit detaillierten Handlungsempfehlungen und Kooperationsszenarien.\"\r\n          icon={Brain}\r\n          analysisData={deepAnalysisData}\r\n          analysisDate={deepAnalysisDate}\r\n          isLoading={isDeepAnalysisLoading}\r\n          onCreateAnalysis={() => selectedBusiness && handleCreateDeepAnalysis(selectedBusiness)}\r\n          onViewAnalysis={handleViewDeepAnalysis}\r\n          selectedBusiness={selectedBusiness}\r\n          accentColor=\"#8b5cf6\"\r\n          delay={0.2}\r\n        />\r\n      </div>\r\n\r\n      {/* Enhancement Notice */}\r\n      {!selectedBusiness && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4, duration: 0.6 }}\r\n          className=\"mt-8 text-center p-6 bg-gradient-to-br from-muted/30 to-muted/10 rounded-2xl border border-border/30 backdrop-blur-sm\"\r\n        >\r\n          <motion.div\r\n            className=\"w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mx-auto mb-3\"\r\n            animate={{ scale: [1, 1.05, 1] }}\r\n            transition={{ duration: 3, repeat: Infinity }}\r\n          >\r\n            <Brain className=\"w-6 h-6 text-primary\" />\r\n          </motion.div>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Wählen Sie zunächst ein Unternehmen aus der Karte, um eine KI-Analyse zu starten.\r\n          </p>\r\n        </motion.div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default AnalysisSection;\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AAAA;AACA;AANA;;;;;AAwBA;;CAEC,GACD,MAAM,kBAAkD,CAAC,EACvD,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,yBAAyB,EACzB,uBAAuB,EACvB,wBAAwB,EACxB,sBAAsB,EACvB;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;kCAIvD,8OAAC;wBAAG,WAAU;;4BAAsC;0CACrC,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAE9C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,qJAAA,CAAA,UAAY;wBACX,OAAM;wBACN,aAAY;wBACZ,MAAM,gMAAA,CAAA,MAAG;wBACT,cAAc;wBACd,cAAc;wBACd,WAAW;wBACX,kBAAkB,IAAM,oBAAoB,0BAA0B;wBACtE,gBAAgB;wBAChB,kBAAkB;wBAClB,aAAY;wBACZ,OAAO;;;;;;kCAIT,8OAAC,qJAAA,CAAA,UAAY;wBACX,OAAM;wBACN,aAAY;wBACZ,MAAM,oMAAA,CAAA,QAAK;wBACX,cAAc;wBACd,cAAc;wBACd,WAAW;wBACX,kBAAkB,IAAM,oBAAoB,yBAAyB;wBACrE,gBAAgB;wBAChB,kBAAkB;wBAClB,aAAY;wBACZ,OAAO;;;;;;;;;;;;YAKV,CAAC,kCACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;gCAAC;gCAAG;gCAAM;6BAAE;wBAAC;wBAC/B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;AAOvD;uCAEe", "debugId": null}}, {"offset": {"line": 6826, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/CustomSwitch.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport styled from 'styled-components';\r\n\r\ninterface SwitchProps {\r\n  checked?: boolean;\r\n  onCheckedChange?: (checked: boolean) => void;\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst Switch: React.FC<SwitchProps> = ({\r\n  checked = false,\r\n  onCheckedChange,\r\n  disabled = false,\r\n  className = \"\"\r\n}) => {\r\n  const [_isDarkMode, setIsDarkMode] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    // Initialize dark mode state\r\n    setIsDarkMode(document.documentElement.classList.contains('dark'));\r\n\r\n    // Set up observer for theme changes\r\n    const observer = new MutationObserver(() => {\r\n      const isDark = document.documentElement.classList.contains('dark');\r\n      setIsDarkMode(isDark);\r\n    });\r\n\r\n    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (!disabled && onCheckedChange) {\r\n      onCheckedChange(e.target.checked);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <StyledWrapper className={className} $isDarkMode={_isDarkMode} $disabled={disabled}>\r\n      <div className=\"toggle-cont\">\r\n        <input\r\n          className=\"toggle-input\"\r\n          id=\"toggle\"\r\n          name=\"toggle\"\r\n          type=\"checkbox\"\r\n          checked={checked}\r\n          onChange={handleChange}\r\n          disabled={disabled}\r\n        />\r\n        <label className=\"toggle-label\" htmlFor=\"toggle\">\r\n          <div className=\"cont-icon\">\r\n            <span style={{'--width': 2, '--deg': 25, '--duration': 11} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 100, '--duration': 18} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 280, '--duration': 5} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 200, '--duration': 3} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 30, '--duration': 20} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 300, '--duration': 9} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 250, '--duration': 4} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 210, '--duration': 8} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 100, '--duration': 9} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 15, '--duration': 13} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 75, '--duration': 18} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 65, '--duration': 6} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 50, '--duration': 7} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 320, '--duration': 5} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 220, '--duration': 5} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 215, '--duration': 2} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 135, '--duration': 9} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 45, '--duration': 4} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 78, '--duration': 16} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 89, '--duration': 19} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 65, '--duration': 14} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 2, '--deg': 97, '--duration': 1} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 174, '--duration': 10} as CustomCSSProperties} className=\"sparkle\" />\r\n            <span style={{'--width': 1, '--deg': 236, '--duration': 5} as CustomCSSProperties} className=\"sparkle\" />\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 30 30\" className=\"icon\">\r\n              <path d=\"M0.96233 28.61C1.36043 29.0081 1.96007 29.1255 2.47555 28.8971L10.4256 25.3552C13.2236 24.11 16.4254 24.1425 19.2107 25.4401L27.4152 29.2747C27.476 29.3044 27.5418 29.3023 27.6047 29.32C27.6563 29.3348 27.7079 29.3497 27.761 29.3574C27.843 29.3687 27.9194 29.3758 28 29.3688C28.1273 29.3617 28.2531 29.3405 28.3726 29.2945C28.4447 29.262 28.5162 29.2287 28.5749 29.1842C28.6399 29.1446 28.6993 29.0994 28.7509 29.0477L28.9008 28.8582C28.9468 28.7995 28.9793 28.7274 29.0112 28.656C29.0599 28.5322 29.0811 28.4036 29.0882 28.2734C29.0939 28.1957 29.0868 28.1207 29.0769 28.0415C29.0705 27.9955 29.0585 27.9524 29.0472 27.9072C29.0295 27.8343 29.0302 27.7601 28.9984 27.6901L25.1638 19.4855C23.8592 16.7073 23.8273 13.5048 25.0726 10.7068L28.6145 2.75679C28.8429 2.24131 28.7318 1.63531 28.3337 1.2372C27.9165 0.820011 27.271 0.721743 26.7491 0.9961L19.8357 4.59596C16.8418 6.15442 13.2879 6.18696 10.2615 4.70062L1.80308 0.520214C1.7055 0.474959 1.60722 0.441742 1.50964 0.421943C1.44459 0.409215 1.37882 0.395769 1.3074 0.402133C1.14406 0.395769 0.981436 0.428275 0.818095 0.499692C0.77284 0.519491 0.719805 0.545671 0.67455 0.578198C0.596061 0.617088 0.524653 0.675786 0.4596 0.74084C0.394546 0.805894 0.335843 0.877306 0.296245 0.956502C0.263718 1.00176 0.237561 1.05477 0.217762 1.10003C0.152708 1.24286 0.126545 1.40058 0.120181 1.54978C0.120181 1.61483 0.126527 1.6735 0.132891 1.73219C0.15269 1.85664 0.178881 1.97332 0.237571 2.08434L4.41798 10.5427C5.91139 13.5621 5.8725 17.1238 4.3204 20.1099L0.720514 27.0233C0.440499 27.5536 0.545137 28.1928 0.96233 28.61Z\" />\r\n            </svg>\r\n          </div>\r\n        </label>\r\n      </div>\r\n    </StyledWrapper>\r\n  );\r\n}\r\n\r\ninterface StyledWrapperProps {\r\n  $isDarkMode: boolean;\r\n  $disabled: boolean;\r\n}\r\n\r\ninterface CustomCSSProperties extends React.CSSProperties {\r\n  '--width'?: number | string;\r\n  '--deg'?: number | string;\r\n  '--duration'?: number | string;\r\n}\r\n\r\nconst StyledWrapper = styled.div<StyledWrapperProps>`\r\n  .toggle-cont {\r\n    --primary: ${props => props.$isDarkMode ? '#54a8fc' : '#0066cc'};\r\n    --light: ${props => props.$isDarkMode ? '#d9d9d9' : '#f0f0f0'};\r\n    --dark: ${props => props.$isDarkMode ? '#121212' : '#1a1a1a'};\r\n    --gray: ${props => props.$isDarkMode ? '#414344' : '#505050'};\r\n    --second: ${props => props.$isDarkMode ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.3)'};\r\n\r\n    position: relative;\r\n    z-index: 10;\r\n\r\n    width: fit-content;\r\n    height: 30px;\r\n    transform: scale(0.65) translateY(-2px);\r\n    margin-left: auto;\r\n\r\n    border-radius: 9999px;\r\n    opacity: ${props => props.$disabled ? '0.5' : '1'};\r\n    cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};\r\n  }\r\n\r\n  .toggle-cont .toggle-input {\r\n    display: none;\r\n  }\r\n\r\n  .toggle-cont .toggle-label {\r\n    --gap: 3px;\r\n    --width: 30px;\r\n\r\n    cursor: ${props => props.$disabled ? 'not-allowed' : 'pointer'};\r\n\r\n    position: relative;\r\n    display: inline-block;\r\n\r\n    padding: 0.25rem;\r\n    width: calc((var(--width) + var(--gap)) * 2);\r\n    height: 100%;\r\n    background-color: var(--dark);\r\n\r\n    border: 1px solid #777777;\r\n    border-bottom: 0;\r\n\r\n    border-radius: 9999px;\r\n    box-sizing: content-box;\r\n    transition: all 0.3s ease-in-out;\r\n  }\r\n  .toggle-label::before {\r\n    content: \"\";\r\n\r\n    position: absolute;\r\n    z-index: -10;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n\r\n    width: calc(100% + 0.75rem);\r\n    height: calc(100% + 0.75rem);\r\n    background-color: var(--gray);\r\n\r\n    border: 1px solid #777777;\r\n    border-bottom: 0;\r\n    border-radius: 9999px;\r\n\r\n    transition: all 0.3s ease-in-out;\r\n  }\r\n  .toggle-label::after {\r\n    content: \"\";\r\n\r\n    position: absolute;\r\n    z-index: -10;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n\r\n    width: 100%;\r\n    height: 100%;\r\n    background-image: radial-gradient(\r\n      circle at 50% -100%,\r\n      rgb(58, 155, 252) 0%,\r\n      rgba(12, 12, 12, 1) 80%\r\n    );\r\n\r\n    border-radius: 9999px;\r\n  }\r\n\r\n  .toggle-cont .toggle-label .cont-icon {\r\n    position: relative;\r\n\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n\r\n    position: relative;\r\n    width: var(--width);\r\n    height: 30px;\r\n    background-image: radial-gradient(\r\n      circle at 50% 0%,\r\n      #666666 0%,\r\n      var(--gray) 100%\r\n    );\r\n\r\n    border: 1px solid #aaaaaa;\r\n    border-bottom: 0;\r\n    border-radius: 9999px;\r\n    box-shadow: inset 0 -0.15rem 0.15rem var(--primary),\r\n      inset 0 0 0.5rem 0.75rem var(--second);\r\n\r\n    transition: transform 0.3s ease-in-out;\r\n  }\r\n\r\n  .cont-icon {\r\n    overflow: clip;\r\n    position: relative;\r\n  }\r\n\r\n  .cont-icon .sparkle {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n\r\n    display: block;\r\n\r\n    width: calc(var(--width) * 0.6px);\r\n    aspect-ratio: 1;\r\n    background-color: var(--light);\r\n\r\n    border-radius: 50%;\r\n    transform-origin: 50% 50%;\r\n    rotate: calc(1deg * var(--deg));\r\n    transform: translate(-50%, -50%);\r\n    animation: sparkle calc(100s / var(--duration)) linear\r\n      calc(0s / var(--duration)) infinite;\r\n  }\r\n\r\n  @keyframes sparkle {\r\n    to {\r\n      width: calc(var(--width) * 0.3px);\r\n      transform: translate(1500%, -50%);\r\n    }\r\n  }\r\n\r\n  .cont-icon .icon {\r\n    width: 0.8rem;\r\n    fill: var(--light);\r\n  }\r\n\r\n  .toggle-cont:has(.toggle-input:checked) {\r\n    --checked: true;\r\n  }\r\n\r\n  .toggle-cont .toggle-input:checked ~ .toggle-label {\r\n    background-color: #41434400;\r\n    border: 1px solid #3d6970;\r\n    border-bottom: 0;\r\n  }\r\n\r\n  .toggle-cont .toggle-input:checked ~ .toggle-label::before {\r\n    box-shadow: 0 1rem 2.5rem -2rem #0080ff;\r\n  }\r\n\r\n  .toggle-cont .toggle-input:checked ~ .toggle-label .cont-icon {\r\n    overflow: visible;\r\n    background-image: radial-gradient(\r\n      circle at 50% 0%,\r\n      #045ab1 0%,\r\n      var(--primary) 100%\r\n    );\r\n    border: 1px solid var(--primary);\r\n    border-bottom: 0;\r\n    transform: translateX(calc((var(--gap) * 2) + 100%)) rotate(-225deg);\r\n  }\r\n\r\n  .toggle-cont .toggle-input:checked ~ .toggle-label .cont-icon .sparkle {\r\n    z-index: -10;\r\n    width: calc(var(--width) * 0.9px);\r\n    background-color: #acacac;\r\n    animation: sparkle-checked calc(100s / var(--duration)) linear\r\n      calc(10s / var(--duration)) infinite;\r\n  }\r\n\r\n  @keyframes sparkle-checked {\r\n    to {\r\n      width: calc(var(--width) * 0.6px);\r\n      transform: translate(3000%, -50%);\r\n    }\r\n  }\r\n`;\r\n\r\nexport default Switch;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,SAAgC,CAAC,EACrC,UAAU,KAAK,EACf,eAAe,EACf,WAAW,KAAK,EAChB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,aAAa,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,cAAc,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;QAE1D,oCAAoC;QACpC,MAAM,WAAW,IAAI,iBAAiB;YACpC,MAAM,SAAS,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;YAC3D,cAAc;QAChB;QAEA,SAAS,OAAO,CAAC,SAAS,eAAe,EAAE;YAAE,YAAY;YAAM,iBAAiB;gBAAC;aAAQ;QAAC;QAE1F,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,YAAY,iBAAiB;YAChC,gBAAgB,EAAE,MAAM,CAAC,OAAO;QAClC;IACF;IAEA,qBACE,8OAAC;QAAc,WAAW;QAAW,aAAa;QAAa,WAAW;kBACxE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,MAAK;oBACL,MAAK;oBACL,SAAS;oBACT,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC;oBAAM,WAAU;oBAAe,SAAQ;8BACtC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC9F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC5F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC5F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC5F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAI,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC5F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAE;gCAA0B,WAAU;;;;;;0CAC9F,8OAAC;gCAAK,OAAO;oCAAC,WAAW;oCAAG,SAAS;oCAAK,cAAc;gCAAC;gCAA0B,WAAU;;;;;;0CAC7F,8OAAC;gCAAI,OAAM;gCAA6B,MAAK;gCAAO,SAAQ;gCAAY,WAAU;0CAChF,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;AAaA,MAAM,gBAAgB,2KAAA,CAAA,UAAM,CAAC,GAAG,AAAoB,CAAC;;eAEtC,EAAE,CAAA,QAAS,MAAM,WAAW,GAAG,YAAY,UAAU;aACvD,EAAE,CAAA,QAAS,MAAM,WAAW,GAAG,YAAY,UAAU;YACtD,EAAE,CAAA,QAAS,MAAM,WAAW,GAAG,YAAY,UAAU;YACrD,EAAE,CAAA,QAAS,MAAM,WAAW,GAAG,YAAY,UAAU;cACnD,EAAE,CAAA,QAAS,MAAM,WAAW,GAAG,uBAAuB,qBAAqB;;;;;;;;;;;aAW5E,EAAE,CAAA,QAAS,MAAM,SAAS,GAAG,QAAQ,IAAI;YAC1C,EAAE,CAAA,QAAS,MAAM,SAAS,GAAG,gBAAgB,UAAU;;;;;;;;;;;YAWvD,EAAE,CAAA,QAAS,MAAM,SAAS,GAAG,gBAAgB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JnE,CAAC;uCAEc", "debugId": null}}, {"offset": {"line": 7406, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/OpuScannerSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from \"react\";\nimport { motion } from 'framer-motion';\nimport { BusinessData } from \"@/types\";\nimport Switch from \"./CustomSwitch\";\nimport type { User } from \"@/contexts/AuthContext\";\nimport { Network, Zap, CheckCircle, AlertCircle, Sparkles, User as UserIcon } from 'lucide-react';\n\ninterface OpuScannerSectionProps {\n  selectedBusiness: BusinessData | null;\n  user: User | null;\n  addToOpuScanner: boolean;\n  setAddToOpuScanner: (value: boolean) => void;\n  isAddingToOpuScanner: boolean;\n  hasQuickAnalysis: boolean;\n  hasDeepAnalysis: boolean;\n}\n\n/**\n * OpuScanner integration section with revolutionary design\n */\nconst OpuScannerSection: React.FC<OpuScannerSectionProps> = ({\n  selectedBusiness,\n  user,\n  addToOpuScanner,\n  setAddToOpuScanner,\n  isAddingToOpuScanner,\n  hasQuickAnalysis,\n  hasDeepAnalysis\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n  \n  const analysisMissing = selectedBusiness && !hasQuickAnalysis && !hasDeepAnalysis;\n  const isDisabled = Boolean(!selectedBusiness || isAddingToOpuScanner || !user || analysisMissing);\n  const isActive = addToOpuScanner && !analysisMissing;\n\n  let statusConfig = {\n    icon: AlertCircle,\n    text: \"Wählen Sie ein Unternehmen aus\",\n    color: \"#9ca3af\",\n    bgColor: \"#f9fafb\"\n  };\n\n  if (!user) {\n    statusConfig = {\n      icon: UserIcon,\n      text: \"Bitte einloggen, um diese Funktion zu nutzen\",\n      color: \"#f59e0b\",\n      bgColor: \"#fef3c7\"\n    };\n  } else if (!selectedBusiness) {\n    statusConfig = {\n      icon: Network,\n      text: \"Wählen Sie ein Unternehmen aus\",\n      color: \"#6b7280\",\n      bgColor: \"#f9fafb\"\n    };\n  } else if (analysisMissing) {\n    statusConfig = {\n      icon: AlertCircle,\n      text: \"Analyse (Schnell oder Tief) erforderlich\",\n      color: \"#f59e0b\",\n      bgColor: \"#fef3c7\"\n    };\n  } else if (isAddingToOpuScanner) {\n    statusConfig = {\n      icon: Sparkles,\n      text: \"Wird bearbeitet...\",\n      color: \"#3b82f6\",\n      bgColor: \"#dbeafe\"\n    };\n  } else if (isActive) {\n    statusConfig = {\n      icon: CheckCircle,\n      text: \"Unternehmen wird im OpuScanner angezeigt\",\n      color: \"#10b981\",\n      bgColor: \"#d1fae5\"\n    };\n  } else {\n    statusConfig = {\n      icon: Network,\n      text: \"Unternehmen wird nicht im OpuScanner angezeigt\",\n      color: \"#6b7280\",\n      bgColor: \"#f9fafb\"\n    };\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50, scale: 0.95 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      transition={{ duration: 0.8, delay: 0.3, ease: [0.68, -0.55, 0.265, 1.55] }}\n      className=\"relative group w-full mt-6\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <div className=\"relative bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60\">\n        {/* Dynamic accent border based on status */}\n        <motion.div \n          className=\"absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-15 transition-opacity duration-500 rounded-2xl\"\n          style={{ background: `linear-gradient(135deg, ${statusConfig.color}20, transparent)` }}\n          animate={isHovered ? { opacity: 0.1 } : { opacity: 0 }}\n        />\n\n        {/* Floating decorative elements */}\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <motion.div\n            className=\"absolute top-4 right-4 w-16 h-16 rounded-full\"\n            style={{ backgroundColor: `${statusConfig.color}10` }}\n            animate={{ rotate: 360, scale: [1, 1.1, 1] }}\n            transition={{ duration: 20, repeat: Infinity }}\n          />\n          <motion.div\n            className=\"absolute bottom-6 left-6 w-12 h-12 rounded-full\"\n            style={{ backgroundColor: `${statusConfig.color}05` }}\n            animate={{ rotate: -360, scale: [1, 0.9, 1] }}\n            transition={{ duration: 25, repeat: Infinity }}\n          />\n          \n          {/* Network connection lines */}\n          <svg className=\"absolute inset-0 w-full h-full opacity-20\" viewBox=\"0 0 400 200\">\n            <motion.path\n              d=\"M 50 100 Q 150 50, 250 100 T 350 100\"\n              stroke={statusConfig.color}\n              strokeWidth=\"2\"\n              fill=\"none\"\n              strokeDasharray=\"5,10\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 2, repeat: Infinity, repeatType: \"loop\" }}\n            />\n            <motion.path\n              d=\"M 100 150 Q 200 120, 300 150\"\n              stroke={statusConfig.color}\n              strokeWidth=\"1.5\"\n              fill=\"none\"\n              strokeDasharray=\"3,8\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 2.5, repeat: Infinity, repeatType: \"loop\", delay: 0.5 }}\n            />\n          </svg>\n        </div>\n\n        <div className=\"relative p-6\">\n          {/* Header with enhanced icon */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <motion.div \n                className=\"w-14 h-14 rounded-xl flex items-center justify-center relative overflow-hidden\"\n                style={{ backgroundColor: `${statusConfig.color}15` }}\n                whileHover={{ scale: 1.1, rotate: 5 }}\n                transition={{ type: \"spring\", stiffness: 300 }}\n              >\n                <Network \n                  className=\"w-7 h-7 transition-colors duration-300\" \n                  style={{ color: statusConfig.color }}\n                />\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent\"\n                  animate={{ x: ['-100%', '200%'] }}\n                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\n                />\n              </motion.div>\n              \n              <div>\n                <h3 className=\"text-xl font-bold text-foreground mb-1\">\n                  OpuScanner Integration\n                </h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Strategische Partnerschaften verwalten\n                </p>\n              </div>\n            </div>\n\n            {/* Status indicator badge */}\n            <motion.div\n              className=\"px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-2\"\n              style={{ \n                backgroundColor: `${statusConfig.color}10`,\n                color: statusConfig.color\n              }}\n              animate={isActive ? { scale: [1, 1.05, 1] } : {}}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              <statusConfig.icon className=\"w-3 h-3\" />\n              <span>\n                {isActive ? \"Aktiv\" : analysisMissing ? \"Analyse fehlt\" : \"Inaktiv\"}\n              </span>\n            </motion.div>\n          </div>\n\n          {/* Main content area */}\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {/* Status Display */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-semibold text-foreground flex items-center\">\n                <statusConfig.icon className=\"w-4 h-4 mr-2\" style={{ color: statusConfig.color }} />\n                Status\n              </h4>\n              \n              <motion.div \n                className=\"p-4 rounded-xl border transition-all duration-300\"\n                style={{ \n                  borderColor: `${statusConfig.color}30`,\n                  backgroundColor: `${statusConfig.color}05`\n                }}\n                whileHover={{ scale: 1.02 }}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <motion.div\n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: statusConfig.color }}\n                    animate={isActive ? { scale: [1, 1.3, 1] } : {}}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  />\n                  <span className=\"text-sm font-medium text-foreground\">\n                    {statusConfig.text}\n                  </span>\n                </div>\n                \n                {/* Progress indicator for processing */}\n                {isAddingToOpuScanner && (\n                  <motion.div\n                    className=\"mt-3 w-full bg-gray-200 rounded-full h-1.5\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <motion.div\n                      className=\"h-1.5 rounded-full\"\n                      style={{ backgroundColor: statusConfig.color }}\n                      animate={{ width: ['0%', '100%'] }}\n                      transition={{ duration: 2, repeat: Infinity }}\n                    />\n                  </motion.div>\n                )}\n              </motion.div>\n            </div>\n\n            {/* Control Panel */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-semibold text-foreground flex items-center\">\n                <Zap className=\"w-4 h-4 mr-2 text-primary\" />\n                Steuerung\n              </h4>\n              \n              <motion.div \n                className=\"p-4 rounded-xl border border-border/30 bg-gradient-to-br from-card/50 to-card/20\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ type: \"spring\", stiffness: 300 }}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1 mr-4\">\n                    <div className=\"text-sm font-medium text-foreground mb-1\">\n                      Zum OpuScanner hinzufügen\n                    </div>\n                    <div className=\"text-xs text-muted-foreground\">\n                      {analysisMissing \n                        ? \"Führen Sie zuerst eine Analyse durch\"\n                        : \"Aktivieren Sie die Integration für strategische Planung\"\n                      }\n                    </div>\n                  </div>\n                  \n                  <div className=\"relative\">\n                    <Switch\n                      checked={addToOpuScanner}\n                      onCheckedChange={(checked) => {\n                        if (checked && analysisMissing) {\n                          return;\n                        }\n                        setAddToOpuScanner(checked);\n                      }}\n                      disabled={isDisabled}\n                      className={isDisabled ? \"opacity-50 cursor-not-allowed\" : \"\"}\n                    />\n                    \n                    {/* Visual feedback for toggle action */}\n                    {addToOpuScanner && !analysisMissing && (\n                      <motion.div\n                        className=\"absolute -inset-2 rounded-full border-2 border-green-400/30\"\n                        animate={{ scale: [1, 1.2, 1], opacity: [0.3, 0.6, 0.3] }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                      />\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n\n          {/* Enhanced features notice */}\n          {isActive && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              className=\"mt-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800\"\n            >\n              <div className=\"flex items-start space-x-3\">\n                <CheckCircle className=\"w-5 h-5 text-green-600 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <h5 className=\"text-sm font-semibold text-green-800 dark:text-green-200 mb-1\">\n                    Integration aktiv\n                  </h5>\n                  <p className=\"text-xs text-green-700 dark:text-green-300 leading-relaxed\">\n                    {selectedBusiness?.name} ist nun in Ihrer OpuScanner-Strategieplanung verfügbar. \n                    Sie können detaillierte Kooperationsanalysen erstellen und Partnerschaften initiieren.\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Hover border effect */}\n      <motion.div\n        className=\"absolute inset-0 rounded-2xl border-2 pointer-events-none\"\n        style={{ borderColor: 'transparent' }}\n        animate={isHovered ? { borderColor: `${statusConfig.color}40` } : { borderColor: 'transparent' }}\n        transition={{ duration: 0.3 }}\n      />\n    </motion.div>\n  );\n};\n\nexport default OpuScannerSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;AAmBA;;CAEC,GACD,MAAM,oBAAsD,CAAC,EAC3D,gBAAgB,EAChB,IAAI,EACJ,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,gBAAgB,EAChB,eAAe,EAChB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,oBAAoB,CAAC,oBAAoB,CAAC;IAClE,MAAM,aAAa,QAAQ,CAAC,oBAAoB,wBAAwB,CAAC,QAAQ;IACjF,MAAM,WAAW,mBAAmB,CAAC;IAErC,IAAI,eAAe;QACjB,MAAM,oNAAA,CAAA,cAAW;QACjB,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,IAAI,CAAC,MAAM;QACT,eAAe;YACb,MAAM,kMAAA,CAAA,OAAQ;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF,OAAO,IAAI,CAAC,kBAAkB;QAC5B,eAAe;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF,OAAO,IAAI,iBAAiB;QAC1B,eAAe;YACb,MAAM,oNAAA,CAAA,cAAW;YACjB,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF,OAAO,IAAI,sBAAsB;QAC/B,eAAe;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF,OAAO,IAAI,UAAU;QACnB,eAAe;YACb,MAAM,2NAAA,CAAA,cAAW;YACjB,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF,OAAO;QACL,eAAe;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAK;QAC1C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,YAAY;YAAE,UAAU;YAAK,OAAO;YAAK,MAAM;gBAAC;gBAAM,CAAC;gBAAM;gBAAO;aAAK;QAAC;QAC1E,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,YAAY,CAAC,wBAAwB,EAAE,aAAa,KAAK,CAAC,gBAAgB,CAAC;wBAAC;wBACrF,SAAS,YAAY;4BAAE,SAAS;wBAAI,IAAI;4BAAE,SAAS;wBAAE;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,iBAAiB,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;gCAAC;gCACpD,SAAS;oCAAE,QAAQ;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;0CAE/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,iBAAiB,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;gCAAC;gCACpD,SAAS;oCAAE,QAAQ,CAAC;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC5C,YAAY;oCAAE,UAAU;oCAAI,QAAQ;gCAAS;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;gCAA4C,SAAQ;;kDACjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,GAAE;wCACF,QAAQ,aAAa,KAAK;wCAC1B,aAAY;wCACZ,MAAK;wCACL,iBAAgB;wCAChB,SAAS;4CAAE,YAAY;wCAAE;wCACzB,SAAS;4CAAE,YAAY;wCAAE;wCACzB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,YAAY;wCAAO;;;;;;kDAElE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,GAAE;wCACF,QAAQ,aAAa,KAAK;wCAC1B,aAAY;wCACZ,MAAK;wCACL,iBAAgB;wCAChB,SAAS;4CAAE,YAAY;wCAAE;wCACzB,SAAS;4CAAE,YAAY;wCAAE;wCACzB,YAAY;4CAAE,UAAU;4CAAK,QAAQ;4CAAU,YAAY;4CAAQ,OAAO;wCAAI;;;;;;;;;;;;;;;;;;kCAKpF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,OAAO;oDAAE,iBAAiB,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;gDAAC;gDACpD,YAAY;oDAAE,OAAO;oDAAK,QAAQ;gDAAE;gDACpC,YAAY;oDAAE,MAAM;oDAAU,WAAW;gDAAI;;kEAE7C,8OAAC,wMAAA,CAAA,UAAO;wDACN,WAAU;wDACV,OAAO;4DAAE,OAAO,aAAa,KAAK;wDAAC;;;;;;kEAErC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,GAAG;gEAAC;gEAAS;6DAAO;wDAAC;wDAChC,YAAY;4DAAE,UAAU;4DAAG,QAAQ;4DAAU,aAAa;wDAAE;;;;;;;;;;;;0DAIhE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEAGvD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAOjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CACL,iBAAiB,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;4CAC1C,OAAO,aAAa,KAAK;wCAC3B;wCACA,SAAS,WAAW;4CAAE,OAAO;gDAAC;gDAAG;gDAAM;6CAAE;wCAAC,IAAI,CAAC;wCAC/C,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;;0DAE5C,8OAAC,aAAa,IAAI;gDAAC,WAAU;;;;;;0DAC7B,8OAAC;0DACE,WAAW,UAAU,kBAAkB,kBAAkB;;;;;;;;;;;;;;;;;;0CAMhE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,aAAa,IAAI;wDAAC,WAAU;wDAAe,OAAO;4DAAE,OAAO,aAAa,KAAK;wDAAC;;;;;;oDAAK;;;;;;;0DAItF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,OAAO;oDACL,aAAa,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;oDACtC,iBAAiB,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;gDAC5C;gDACA,YAAY;oDAAE,OAAO;gDAAK;;kEAE1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,KAAK;gEAAC;gEAC7C,SAAS,WAAW;oEAAE,OAAO;wEAAC;wEAAG;wEAAK;qEAAE;gEAAC,IAAI,CAAC;gEAC9C,YAAY;oEAAE,UAAU;oEAAG,QAAQ;gEAAS;;;;;;0EAE9C,8OAAC;gEAAK,WAAU;0EACb,aAAa,IAAI;;;;;;;;;;;;oDAKrB,sCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,SAAS;wDAAE;wDACtB,SAAS;4DAAE,SAAS;wDAAE;kEAEtB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,OAAO;gEAAE,iBAAiB,aAAa,KAAK;4DAAC;4DAC7C,SAAS;gEAAE,OAAO;oEAAC;oEAAM;iEAAO;4DAAC;4DACjC,YAAY;gEAAE,UAAU;gEAAG,QAAQ;4DAAS;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAI/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,YAAY;oDAAE,MAAM;oDAAU,WAAW;gDAAI;0DAE7C,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA2C;;;;;;8EAG1D,8OAAC;oEAAI,WAAU;8EACZ,kBACG,yCACA;;;;;;;;;;;;sEAKR,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,qJAAA,CAAA,UAAM;oEACL,SAAS;oEACT,iBAAiB,CAAC;wEAChB,IAAI,WAAW,iBAAiB;4EAC9B;wEACF;wEACA,mBAAmB;oEACrB;oEACA,UAAU;oEACV,WAAW,aAAa,kCAAkC;;;;;;gEAI3D,mBAAmB,CAAC,iCACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEAAE,OAAO;4EAAC;4EAAG;4EAAK;yEAAE;wEAAE,SAAS;4EAAC;4EAAK;4EAAK;yEAAI;oEAAC;oEACxD,YAAY;wEAAE,UAAU;wEAAG,QAAQ;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUzD,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAgE;;;;;;8DAG9E,8OAAC;oDAAE,WAAU;;wDACV,kBAAkB;wDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,aAAa;gBAAc;gBACpC,SAAS,YAAY;oBAAE,aAAa,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC;gBAAC,IAAI;oBAAE,aAAa;gBAAc;gBAC/F,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 8115, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/iconPaths.ts"], "sourcesContent": ["// Icon SVG paths\nexport const iconPaths = {\n  refresh: {\n    path1: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8',\n    path2: 'M21 3v5h-5',\n    path3: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16',\n    path4: 'M3 21v-5h5'\n  },\n  close: {\n    path: 'M6 18L18 6M6 6l12 12'\n  },\n  location: {\n    path1: 'M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z',\n    path2: 'M15 11a3 3 0 11-6 0 3 3 0 016 0z'\n  }\n};\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;AACV,MAAM,YAAY;IACvB,SAAS;QACP,OAAO;QACP,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,OAAO;QACL,MAAM;IACR;IACA,UAAU;QACR,OAAO;QACP,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 8140, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/index.ts"], "sourcesContent": ["export * from './iconPaths';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 8236, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisModal.tsx"], "sourcesContent": ["import React from \"react\";\nimport { BusinessData } from \"@/types\";\nimport {\n  modalStyles,\n  analysisAdditionalStyles\n} from \"../styles\";\nimport { iconPaths } from \"@/styles\";\nimport { formatAnalysisDate } from \"@/utils\";\nimport ReactMarkdown from 'react-markdown';\n\ninterface AnalysisModalProps {\n  showModal: boolean;\n  setShowModal: (show: boolean) => void;\n  analysisType: \"schnell\" | \"tief\";\n  analysisData: string | null;\n  analysisDate: string | null;\n  selectedBusiness: BusinessData | null;\n}\n\n/**\n * Modal component to display analysis results\n */\nconst AnalysisModal: React.FC<AnalysisModalProps> = ({\n  showModal,\n  setShowModal,\n  analysisType,\n  analysisData,\n  analysisDate,\n  selectedBusiness\n}) => {\n  if (!showModal || !analysisData) return null;\n\n  return (\n    // Modal backdrop\n    <div className={modalStyles.backdrop}>\n      {/* Modal content */}\n      <div className={modalStyles.container}>\n        {/* Modal header */}\n        <div className={modalStyles.header}>\n          <h2 className={modalStyles.title}>\n            {analysisType === \"schnell\" ? \"Schnelle\" : \"Tiefe\"} Analyse: {selectedBusiness?.name}\n          </h2>\n          {/* Close button */}\n          <button onClick={() => setShowModal(false)} className={modalStyles.closeButton}>\n            <svg className={analysisAdditionalStyles.modalCloseIcon} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d={iconPaths.close.path}></path>\n            </svg>\n          </button>\n        </div>\n        {/* Modal body - scrollable */}\n        <div className={modalStyles.body}>\n          <div className={modalStyles.dateInfo}>\n            Analyse vom: {formatAnalysisDate(analysisDate)}\n          </div>\n          {/* Use prose for basic markdown-like formatting if analysis content is text */}\n          <div className={`${modalStyles.content} prose dark:prose-invert`}>\n            {/* Use ReactMarkdown to render analysisData */}\n            <ReactMarkdown>{analysisData}</ReactMarkdown>\n          </div>\n        </div>\n        {/* Modal footer */}\n        <div className={modalStyles.footer}>\n          <button\n            onClick={() => setShowModal(false)}\n            className={modalStyles.closeButtonLarge}\n          >\n            Schließen\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnalysisModal;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAIA;AAAA;AACA;AAAA;AACA;;;;;;AAWA;;CAEC,GACD,MAAM,gBAA8C,CAAC,EACnD,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,gBAAgB,EACjB;IACC,IAAI,CAAC,aAAa,CAAC,cAAc,OAAO;IAExC,OACE,iBAAiB;kBACjB,8OAAC;QAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,QAAQ;kBAElC,cAAA,8OAAC;YAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,SAAS;;8BAEnC,8OAAC;oBAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,MAAM;;sCAChC,8OAAC;4BAAG,WAAW,kJAAA,CAAA,cAAW,CAAC,KAAK;;gCAC7B,iBAAiB,YAAY,aAAa;gCAAQ;gCAAW,kBAAkB;;;;;;;sCAGlF,8OAAC;4BAAO,SAAS,IAAM,aAAa;4BAAQ,WAAW,kJAAA,CAAA,cAAW,CAAC,WAAW;sCAC5E,cAAA,8OAAC;gCAAI,WAAW,kJAAA,CAAA,2BAAwB,CAAC,cAAc;gCAAE,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjG,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAY;oCAAI,GAAG,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAKhG,8OAAC;oBAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,IAAI;;sCAC9B,8OAAC;4BAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,QAAQ;;gCAAE;gCACtB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;sCAGnC,8OAAC;4BAAI,WAAW,GAAG,kJAAA,CAAA,cAAW,CAAC,OAAO,CAAC,wBAAwB,CAAC;sCAE9D,cAAA,8OAAC,wLAAA,CAAA,UAAa;0CAAE;;;;;;;;;;;;;;;;;8BAIpB,8OAAC;oBAAI,WAAW,kJAAA,CAAA,cAAW,CAAC,MAAM;8BAChC,cAAA,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,kJAAA,CAAA,cAAW,CAAC,gBAAgB;kCACxC;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 8380, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AdvancedMarker.tsx"], "sourcesContent": ["// Based on https://github.dev/visgl/react-google-maps because the original package is not maintained anymore and misses advanced markers\r\nimport React, {\r\n    Children,\r\n    forwardRef,\r\n    useCallback,\r\n    useContext,\r\n    useEffect,\r\n    useImperativeHandle,\r\n    useMemo,\r\n    useState\r\n} from 'react';\r\nimport { createPortal } from 'react-dom';\r\nimport type { Ref, PropsWithChildren } from 'react';\r\nimport { MapContext } from '@react-google-maps/api';\r\n\r\nexport interface AdvancedMarkerContextValue {\r\n    marker: google.maps.marker.AdvancedMarkerElement;\r\n}\r\n\r\nexport const AdvancedMarkerContext =\r\n    React.createContext<AdvancedMarkerContextValue | null>(null);\r\n\r\ntype AdvancedMarkerEventProps = {\r\n    onClick?: (e: google.maps.MapMouseEvent) => void;\r\n    onRightClick?: (e: MouseEvent) => void;\r\n    onDrag?: (e: google.maps.MapMouseEvent) => void;\r\n    onDragStart?: (e: google.maps.MapMouseEvent) => void;\r\n    onDragEnd?: (e: google.maps.MapMouseEvent) => void;\r\n};\r\n\r\nexport type AdvancedMarkerProps = PropsWithChildren<\r\n    Omit<google.maps.marker.AdvancedMarkerElementOptions, 'gmpDraggable'> &\r\n    AdvancedMarkerEventProps & {\r\n        /**\r\n         * className to add a class to the advanced marker element\r\n         * Can only be used with HTML Marker content\r\n         */\r\n        className?: string;\r\n        anchorAbove?: boolean;\r\n        draggable?: boolean;\r\n        clickable?: boolean; // right now this just deactivates the onClick handler but does not change the google maps marker behaviour\r\n    }\r\n>;\r\n\r\nexport type AdvancedMarkerRef = google.maps.marker.AdvancedMarkerElement | null;\r\n\r\nfunction useAdvancedMarker(props: AdvancedMarkerProps) {\r\n    const [marker, setMarker] =\r\n        useState<google.maps.marker.AdvancedMarkerElement | null>(null);\r\n    const [contentContainer, setContentContainer] =\r\n        useState<HTMLDivElement | null>(null);\r\n\r\n    const map = useContext(MapContext);\r\n    const markerLibrary = google.maps.marker\r\n\r\n    const {\r\n        children,\r\n        className,\r\n        anchorAbove,\r\n        onClick,\r\n        onRightClick,\r\n        onDrag,\r\n        onDragStart,\r\n        onDragEnd,\r\n        collisionBehavior,\r\n        draggable,\r\n        clickable,\r\n        position,\r\n        title,\r\n        zIndex\r\n    } = props;\r\n\r\n    const numChilds = Children.count(children);\r\n\r\n    // create marker instance and add it to the map when map becomes available\r\n    useEffect(() => {\r\n        if (!map || !markerLibrary) return;\r\n\r\n        const newMarker = new markerLibrary.AdvancedMarkerElement();\r\n        newMarker.map = map;\r\n        setMarker(newMarker);\r\n\r\n        // create container for marker content if there are children\r\n        if (numChilds > 0) {\r\n            const el = document.createElement('div');\r\n            if (!anchorAbove) el.style.transform = 'translate(0, 50%)';\r\n            if (className) el.className = className;\r\n            newMarker.content = el;\r\n            setContentContainer(el);\r\n        }\r\n\r\n        return () => {\r\n            newMarker.map = null;\r\n            setMarker(null);\r\n            setContentContainer(null);\r\n        };\r\n\r\n        // We do not want to re-render the whole marker when the className changes\r\n        // because that causes a short flickering of the marker.\r\n        // The className update is handled in the useEffect below.\r\n        // Excluding the className from the dependency array onm purpose here\r\n        // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [map, markerLibrary, numChilds]);\r\n\r\n    useEffect(() => {\r\n        if ((className?.includes('translate') || className?.includes('transform')) && !anchorAbove) console.warn(\"not setting anchorAbove automatically set 'transform: translate(0, 50%)' to center the marker on the position. Set this to false to be able to use custom translate values.\");\r\n    }, [className, anchorAbove]);\r\n\r\n    // update className of advanced marker element\r\n    useEffect(() => {\r\n        if (!contentContainer) return;\r\n        contentContainer.className = className ?? '';\r\n    }, [contentContainer, className]);\r\n\r\n    // bind all marker events\r\n    useEffect(() => {\r\n        if (!marker) return;\r\n\r\n        const gme = google.maps.event;\r\n        const controller = new AbortController();\r\n        const { signal } = controller;\r\n\r\n        // TODO fix the type mismatch for click and contextmenu events\r\n        // marker.addEventListener(\"gmp-click\", (e: google.maps.marker.AdvancedMarkerClickEvent) => { console.log(e) }); // only available in beta and misses some properties, check https://issuetracker.google.com/issues/331684436\r\n\r\n        if (onClick && clickable) gme.addListener(marker, 'click', (e: google.maps.MapMouseEvent) => onClick(e));\r\n        if (onRightClick && clickable) marker.content?.addEventListener('contextmenu', onRightClick as EventListener, { signal }); // setting marker.addEventListener directly onces this is out of beta\r\n        if (onDrag && draggable) gme.addListener(marker, 'drag', onDrag);\r\n        if (onDragStart && draggable) gme.addListener(marker, 'dragstart', onDragStart);\r\n        if (onDragEnd && draggable) gme.addListener(marker, 'dragend', onDragEnd);\r\n\r\n        if ((onDrag || onDragStart || onDragEnd) && !draggable) {\r\n            console.warn(\r\n                'You need to set the marker to draggable to listen to drag-events.'\r\n            );\r\n        }\r\n\r\n        if ((onClick && !clickable) || (onRightClick && !clickable)) {\r\n            console.warn(\r\n                'You need to set the marker to clickable to listen to click-events.'\r\n            );\r\n        }\r\n\r\n        const m = marker;\r\n\r\n        return () => {\r\n            gme.clearInstanceListeners(m); // for addListener\r\n            controller.abort(); // for addEventListener\r\n        };\r\n    }, [marker, draggable, onClick, onDragStart, onDrag, onDragEnd, onRightClick, clickable]);\r\n\r\n    // update other marker props when changed\r\n    useEffect(() => {\r\n        if (!marker) return;\r\n\r\n        if (position !== undefined) marker.position = position;\r\n        if (draggable !== undefined) marker.gmpDraggable = draggable;\r\n\r\n        // use marker.gmpClickable once this is resolved https://issuetracker.google.com/issues/331684436\r\n        if (collisionBehavior !== undefined)\r\n            marker.collisionBehavior = collisionBehavior;\r\n\r\n        if (zIndex !== undefined) marker.zIndex = zIndex;\r\n\r\n        if (typeof title === 'string') marker.title = title;\r\n\r\n    }, [marker, position, draggable, collisionBehavior, zIndex, title, clickable]);\r\n\r\n    return [marker, contentContainer] as const;\r\n}\r\n\r\nconst AdvancedMarkerComponent = forwardRef(\r\n    (props: AdvancedMarkerProps, ref: Ref<AdvancedMarkerRef>) => {\r\n        const { children } = props;\r\n\r\n        const [marker, contentContainer] = useAdvancedMarker(props);\r\n\r\n        const advancedMarkerContextValue: AdvancedMarkerContextValue | null =\r\n            useMemo(() => (marker ? { marker } : null), [marker]);\r\n\r\n        // @ts-expect-error: Type 'AdvancedMarkerElement | null' is not assignable to type 'AdvancedMarkerElement'.\r\n        // This is likely a type definition issue. The runtime logic handles the null case.\r\n        useImperativeHandle(ref, () => marker, [marker]);\r\n\r\n        if (!marker) {\r\n            return null;\r\n        }\r\n\r\n        // we could add other props here to the context, but lets try to achieve this with tailwind group or other means first\r\n        return (\r\n            <AdvancedMarkerContext.Provider value={advancedMarkerContextValue}>\r\n                {contentContainer !== null && createPortal(children, contentContainer)}\r\n            </AdvancedMarkerContext.Provider>\r\n        );\r\n    }\r\n);\r\n\r\nAdvancedMarkerComponent.displayName = 'AdvancedMarker';\r\n\r\nexport const AdvancedMarker = AdvancedMarkerComponent;\r\n\r\nexport function useAdvancedMarkerRef() {\r\n    const [marker, setMarker] =\r\n        useState<google.maps.marker.AdvancedMarkerElement | null>(null);\r\n\r\n    const refCallback = useCallback((m: AdvancedMarkerRef | null) => {\r\n        setMarker(m);\r\n    }, []);\r\n\r\n    return [refCallback, marker] as const;\r\n}\r\n"], "names": [], "mappings": "AAAA,yIAAyI;;;;;;;AACzI;AAUA;AAEA;;;;;AAMO,MAAM,sCACT,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAoC;AA0B3D,SAAS,kBAAkB,KAA0B;IACjD,MAAM,CAAC,QAAQ,UAAU,GACrB,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GACzC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAEpC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,+JAAA,CAAA,aAAU;IACjC,MAAM,gBAAgB,OAAO,IAAI,CAAC,MAAM;IAExC,MAAM,EACF,QAAQ,EACR,SAAS,EACT,WAAW,EACX,OAAO,EACP,YAAY,EACZ,MAAM,EACN,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,SAAS,EACT,QAAQ,EACR,KAAK,EACL,MAAM,EACT,GAAG;IAEJ,MAAM,YAAY,qMAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;IAEjC,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,OAAO,CAAC,eAAe;QAE5B,MAAM,YAAY,IAAI,cAAc,qBAAqB;QACzD,UAAU,GAAG,GAAG;QAChB,UAAU;QAEV,4DAA4D;QAC5D,IAAI,YAAY,GAAG;YACf,MAAM,KAAK,SAAS,aAAa,CAAC;YAClC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,SAAS,GAAG;YACvC,IAAI,WAAW,GAAG,SAAS,GAAG;YAC9B,UAAU,OAAO,GAAG;YACpB,oBAAoB;QACxB;QAEA,OAAO;YACH,UAAU,GAAG,GAAG;YAChB,UAAU;YACV,oBAAoB;QACxB;IAEA,0EAA0E;IAC1E,wDAAwD;IACxD,0DAA0D;IAC1D,qEAAqE;IACrE,uDAAuD;IAC3D,GAAG;QAAC;QAAK;QAAe;KAAU;IAElC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,WAAW,SAAS,gBAAgB,WAAW,SAAS,YAAY,KAAK,CAAC,aAAa,QAAQ,IAAI,CAAC;IAC7G,GAAG;QAAC;QAAW;KAAY;IAE3B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,kBAAkB;QACvB,iBAAiB,SAAS,GAAG,aAAa;IAC9C,GAAG;QAAC;QAAkB;KAAU;IAEhC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,IAAI,CAAC,KAAK;QAC7B,MAAM,aAAa,IAAI;QACvB,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,8DAA8D;QAC9D,6NAA6N;QAE7N,IAAI,WAAW,WAAW,IAAI,WAAW,CAAC,QAAQ,SAAS,CAAC,IAAiC,QAAQ;QACrG,IAAI,gBAAgB,WAAW,OAAO,OAAO,EAAE,iBAAiB,eAAe,cAA+B;YAAE;QAAO,IAAI,qEAAqE;QAChM,IAAI,UAAU,WAAW,IAAI,WAAW,CAAC,QAAQ,QAAQ;QACzD,IAAI,eAAe,WAAW,IAAI,WAAW,CAAC,QAAQ,aAAa;QACnE,IAAI,aAAa,WAAW,IAAI,WAAW,CAAC,QAAQ,WAAW;QAE/D,IAAI,CAAC,UAAU,eAAe,SAAS,KAAK,CAAC,WAAW;YACpD,QAAQ,IAAI,CACR;QAER;QAEA,IAAI,AAAC,WAAW,CAAC,aAAe,gBAAgB,CAAC,WAAY;YACzD,QAAQ,IAAI,CACR;QAER;QAEA,MAAM,IAAI;QAEV,OAAO;YACH,IAAI,sBAAsB,CAAC,IAAI,kBAAkB;YACjD,WAAW,KAAK,IAAI,uBAAuB;QAC/C;IACJ,GAAG;QAAC;QAAQ;QAAW;QAAS;QAAa;QAAQ;QAAW;QAAc;KAAU;IAExF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,QAAQ;QAEb,IAAI,aAAa,WAAW,OAAO,QAAQ,GAAG;QAC9C,IAAI,cAAc,WAAW,OAAO,YAAY,GAAG;QAEnD,iGAAiG;QACjG,IAAI,sBAAsB,WACtB,OAAO,iBAAiB,GAAG;QAE/B,IAAI,WAAW,WAAW,OAAO,MAAM,GAAG;QAE1C,IAAI,OAAO,UAAU,UAAU,OAAO,KAAK,GAAG;IAElD,GAAG;QAAC;QAAQ;QAAU;QAAW;QAAmB;QAAQ;QAAO;KAAU;IAE7E,OAAO;QAAC;QAAQ;KAAiB;AACrC;AAEA,MAAM,wCAA0B,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrC,CAAC,OAA4B;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,MAAM,CAAC,QAAQ,iBAAiB,GAAG,kBAAkB;IAErD,MAAM,6BACF,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAO,SAAS;YAAE;QAAO,IAAI,MAAO;QAAC;KAAO;IAExD,2GAA2G;IAC3G,mFAAmF;IACnF,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,QAAQ;QAAC;KAAO;IAE/C,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IAEA,sHAAsH;IACtH,qBACI,8OAAC,sBAAsB,QAAQ;QAAC,OAAO;kBAClC,qBAAqB,sBAAQ,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,UAAU;;;;;;AAGjE;AAGJ,wBAAwB,WAAW,GAAG;AAE/B,MAAM,iBAAiB;AAEvB,SAAS;IACZ,MAAM,CAAC,QAAQ,UAAU,GACrB,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IAE9D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,UAAU;IACd,GAAG,EAAE;IAEL,OAAO;QAAC;QAAa;KAAO;AAChC", "debugId": null}}, {"offset": {"line": 8548, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/index.ts"], "sourcesContent": ["export { default as BusinessCard } from './BusinessCard';\nexport { default as AnalysisSection } from './AnalysisSection';\nexport { default as OpuScannerSection } from './OpuScannerSection';\nexport { default as AnalysisModal } from './AnalysisModal';\nexport { AdvancedMarker } from './AdvancedMarker';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 8618, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/MapComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback } from \"react\";\nimport { GoogleMap, useLoadScript } from \"@react-google-maps/api\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { fetchWithAuth } from \"@/lib/supabaseClient\";\nimport {\n  mapLayoutStyles,\n  mapOptions,\n  mapDefaults,\n  statusStyles,\n  businessCardAdditionalStyles,\n  DEMO_MARKERS\n} from \"./styles\";\n\nimport {\n  useMapState,\n  useBusinessSelection,\n  useAnalysis,\n  useOpuScannerIntegration\n} from \"./hooks\";\nimport {\n  BusinessCard,\n  AnalysisSection,\n  OpuScannerSection,\n  AnalysisModal,\n  AdvancedMarker\n} from \"./components\";\n\n/**\n * Main MapComponent that serves as the homepage when logged in\n */\nconst MapComponent: React.FC = () => {\n  // Get authentication context\n  const { user } = useAuth();\n\n  // Custom hooks for state management\n  const {\n    mapCenter,\n    mapZoom,\n    isDarkMode,\n    mapRef,\n    saveMapState,\n    loadMapState,\n    resetMapToDefaults\n  } = useMapState(user);\n\n  const {\n    quickAnalysisData,\n    quickAnalysisDate,\n    isQuickAnalysisLoading,\n    deepAnalysisData,\n    deepAnalysisDate,\n    isDeepAnalysisLoading,\n    showAnalysisModal,\n    setShowAnalysisModal,\n    currentAnalysisType,\n    resetAnalysisState,\n    checkExistingAnalysis,\n    handleCreateQuickAnalysis,\n    handleViewQuickAnalysis,\n    handleCreateDeepAnalysis,\n    handleViewDeepAnalysis\n  } = useAnalysis(user);\n\n  const {\n    selectedBusiness,\n    isFlipped,\n    handleMarkerClick,\n    handleMapClick,\n    toggleFlip,\n    lastSelectedBusinessRef\n  } = useBusinessSelection(user, saveMapState, checkExistingAnalysis, resetAnalysisState);\n\n  const {\n    addToOpuScanner,\n    setAddToOpuScanner,\n    isAddingToOpuScanner\n  } = useOpuScannerIntegration(\n    user,\n    selectedBusiness,\n    fetchWithAuth,\n    !!quickAnalysisData, // Pass quick analysis status\n    !!deepAnalysisData // Pass deep analysis status\n  );\n\n  // Load Google Maps API\n  const { isLoaded, loadError } = useLoadScript({\n    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || \"\",\n    libraries: mapDefaults.libraries,\n  });\n\n  // Map load handler\n  const onMapLoad = useCallback((map: google.maps.Map) => {\n    mapRef.current = map;\n\n    // Apply saved map state if available and user is logged in\n    if (user) {\n      const savedMapState = loadMapState();\n      if (savedMapState) {\n        console.log(\"Map loaded, applying saved state\");\n        map.panTo(savedMapState.center);\n        map.setZoom(savedMapState.zoom);\n\n        // Restore selected business if available\n        if (savedMapState.selectedBusinessPlaceId && !selectedBusiness) {\n          console.log(\"Restoring selected business from saved state:\", savedMapState.selectedBusinessPlaceId);\n          // We'll restore the business once the Places service is available\n          lastSelectedBusinessRef.current = savedMapState.selectedBusinessPlaceId;\n        }\n      } else {\n        // No saved state, use defaults\n        resetMapToDefaults();\n      }\n    } else {\n      // User not logged in, use defaults\n      resetMapToDefaults();\n    }\n\n    // Add event listeners for map changes\n    map.addListener('idle', () => {\n      if (mapRef.current && user) {\n        saveMapState(selectedBusiness?.place_id);\n      }\n    });\n  }, [user, loadMapState, resetMapToDefaults, saveMapState, selectedBusiness, lastSelectedBusinessRef, mapRef]);\n\n  // Render loading/error states for map\n  if (loadError)\n    return <div className={statusStyles.errorMessage}>Error loading maps</div>;\n  if (!isLoaded)\n    return <div className={statusStyles.loadingMessage}>Loading Maps...</div>;\n\n  // Main component render\n  return (\n    <div className={mapLayoutStyles.container}>\n      {/* Map Section */}\n      <div className={mapLayoutStyles.mapSection}>\n        <GoogleMap\n          mapContainerClassName={mapLayoutStyles.mapContainer}\n          center={mapCenter}\n          zoom={mapZoom}\n          options={mapOptions(isDarkMode)}\n          onLoad={onMapLoad}\n          onClick={handleMapClick} // Handle clicks on the map (including POIs)\n        >\n          {/* Render demo markers only if they're not the selected business */}\n          {DEMO_MARKERS.map((marker) => (\n            selectedBusiness?.place_id !== marker.place_id && (\n              <AdvancedMarker\n                key={marker.id}\n                position={marker.position}\n                onClick={() => handleMarkerClick(marker)} // Handle clicks on demo markers\n              />\n            )\n          ))}\n          {/* Render marker for the selected business */}\n          {selectedBusiness && (\n            <AdvancedMarker\n              key={`selected-${selectedBusiness.place_id}`}\n              position={\n                \"geometry\" in selectedBusiness && selectedBusiness.geometry?.location\n                  ? selectedBusiness.geometry.location\n                  : \"position\" in selectedBusiness\n                    ? selectedBusiness.position\n                    : mapCenter // Fallback to map center if no position available\n              }\n            />\n          )}\n        </GoogleMap>\n      </div>\n\n      {/* Content Section: Business Info + Analysis */}\n      <div className={mapLayoutStyles.contentSection}>\n        {/* Business Info Card (Flip Card) */}\n        <div className={businessCardAdditionalStyles.businessCardContainer}>\n          <BusinessCard\n            selectedBusiness={selectedBusiness}\n            isFlipped={isFlipped}\n            toggleFlip={toggleFlip}\n          />\n        </div>\n\n        {/* Right side column with Analysis and OpuScanner */}\n        <div className=\"flex flex-col w-full md:w-auto mt-4 md:mt-0\">\n          {/* Analysis Sections */}\n          <AnalysisSection\n            selectedBusiness={selectedBusiness}\n            quickAnalysisData={quickAnalysisData}\n            quickAnalysisDate={quickAnalysisDate}\n            isQuickAnalysisLoading={isQuickAnalysisLoading}\n            deepAnalysisData={deepAnalysisData}\n            deepAnalysisDate={deepAnalysisDate}\n            isDeepAnalysisLoading={isDeepAnalysisLoading}\n            handleCreateQuickAnalysis={handleCreateQuickAnalysis}\n            handleViewQuickAnalysis={handleViewQuickAnalysis}\n            handleCreateDeepAnalysis={handleCreateDeepAnalysis}\n            handleViewDeepAnalysis={handleViewDeepAnalysis}\n          />\n\n          {/* OpuScanner Integration */}\n          <OpuScannerSection\n            selectedBusiness={selectedBusiness}\n            user={user}\n            addToOpuScanner={addToOpuScanner}\n            setAddToOpuScanner={setAddToOpuScanner}\n            isAddingToOpuScanner={isAddingToOpuScanner}\n            // Pass analysis status down\n            hasQuickAnalysis={!!quickAnalysisData}\n            hasDeepAnalysis={!!deepAnalysisData}\n          />\n        </div>\n      </div>\n\n      {/* Analysis Modal */}\n      <AnalysisModal\n        showModal={showAnalysisModal}\n        setShowModal={setShowAnalysisModal}\n        analysisType={currentAnalysisType}\n        analysisData={currentAnalysisType === \"schnell\" ? quickAnalysisData : deepAnalysisData}\n        analysisDate={currentAnalysisType === \"schnell\" ? quickAnalysisDate : deepAnalysisDate}\n        selectedBusiness={selectedBusiness}\n      />\n    </div>\n  );\n};\n\nexport default MapComponent;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AArBA;;;;;;;;;AA6BA;;CAEC,GACD,MAAM,eAAyB;IAC7B,6BAA6B;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,oCAAoC;IACpC,MAAM,EACJ,SAAS,EACT,OAAO,EACP,UAAU,EACV,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,kBAAkB,EACnB,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE;IAEhB,MAAM,EACJ,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,iBAAiB,EACjB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,qBAAqB,EACrB,yBAAyB,EACzB,uBAAuB,EACvB,wBAAwB,EACxB,sBAAsB,EACvB,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE;IAEhB,MAAM,EACJ,gBAAgB,EAChB,SAAS,EACT,iBAAiB,EACjB,cAAc,EACd,UAAU,EACV,uBAAuB,EACxB,GAAG,CAAA,GAAA,uJAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,cAAc,uBAAuB;IAEpE,MAAM,EACJ,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACrB,GAAG,CAAA,GAAA,2JAAA,CAAA,2BAAwB,AAAD,EACzB,MACA,kBACA,4HAAA,CAAA,gBAAa,EACb,CAAC,CAAC,mBACF,CAAC,CAAC,iBAAiB,4BAA4B;;IAGjD,uBAAuB;IACvB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;QAC5C,kBAAkB,+EAA+C;QACjE,WAAW,6IAAA,CAAA,cAAW,CAAC,SAAS;IAClC;IAEA,mBAAmB;IACnB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,OAAO,OAAO,GAAG;QAEjB,2DAA2D;QAC3D,IAAI,MAAM;YACR,MAAM,gBAAgB;YACtB,IAAI,eAAe;gBACjB,QAAQ,GAAG,CAAC;gBACZ,IAAI,KAAK,CAAC,cAAc,MAAM;gBAC9B,IAAI,OAAO,CAAC,cAAc,IAAI;gBAE9B,yCAAyC;gBACzC,IAAI,cAAc,uBAAuB,IAAI,CAAC,kBAAkB;oBAC9D,QAAQ,GAAG,CAAC,iDAAiD,cAAc,uBAAuB;oBAClG,kEAAkE;oBAClE,wBAAwB,OAAO,GAAG,cAAc,uBAAuB;gBACzE;YACF,OAAO;gBACL,+BAA+B;gBAC/B;YACF;QACF,OAAO;YACL,mCAAmC;YACnC;QACF;QAEA,sCAAsC;QACtC,IAAI,WAAW,CAAC,QAAQ;YACtB,IAAI,OAAO,OAAO,IAAI,MAAM;gBAC1B,aAAa,kBAAkB;YACjC;QACF;IACF,GAAG;QAAC;QAAM;QAAc;QAAoB;QAAc;QAAkB;QAAyB;KAAO;IAE5G,sCAAsC;IACtC,IAAI,WACF,qBAAO,8OAAC;QAAI,WAAW,6IAAA,CAAA,eAAY,CAAC,YAAY;kBAAE;;;;;;IACpD,IAAI,CAAC,UACH,qBAAO,8OAAC;QAAI,WAAW,6IAAA,CAAA,eAAY,CAAC,cAAc;kBAAE;;;;;;IAEtD,wBAAwB;IACxB,qBACE,8OAAC;QAAI,WAAW,6IAAA,CAAA,kBAAe,CAAC,SAAS;;0BAEvC,8OAAC;gBAAI,WAAW,6IAAA,CAAA,kBAAe,CAAC,UAAU;0BACxC,cAAA,8OAAC,+JAAA,CAAA,YAAS;oBACR,uBAAuB,6IAAA,CAAA,kBAAe,CAAC,YAAY;oBACnD,QAAQ;oBACR,MAAM;oBACN,SAAS,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;oBACpB,QAAQ;oBACR,SAAS;;wBAGR,6IAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,SACjB,kBAAkB,aAAa,OAAO,QAAQ,kBAC5C,8OAAC,uJAAA,CAAA,iBAAc;gCAEb,UAAU,OAAO,QAAQ;gCACzB,SAAS,IAAM,kBAAkB;+BAF5B,OAAO,EAAE;;;;;wBAOnB,kCACC,8OAAC,uJAAA,CAAA,iBAAc;4BAEb,UACE,cAAc,oBAAoB,iBAAiB,QAAQ,EAAE,WACzD,iBAAiB,QAAQ,CAAC,QAAQ,GAClC,cAAc,mBACZ,iBAAiB,QAAQ,GACzB,UAAU,kDAAkD;2BAN/D,CAAC,SAAS,EAAE,iBAAiB,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAcpD,8OAAC;gBAAI,WAAW,6IAAA,CAAA,kBAAe,CAAC,cAAc;;kCAE5C,8OAAC;wBAAI,WAAW,sJAAA,CAAA,+BAA4B,CAAC,qBAAqB;kCAChE,cAAA,8OAAC,gMAAA,CAAA,eAAY;4BACX,kBAAkB;4BAClB,WAAW;4BACX,YAAY;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,sMAAA,CAAA,kBAAe;gCACd,kBAAkB;gCAClB,mBAAmB;gCACnB,mBAAmB;gCACnB,wBAAwB;gCACxB,kBAAkB;gCAClB,kBAAkB;gCAClB,uBAAuB;gCACvB,2BAA2B;gCAC3B,yBAAyB;gCACzB,0BAA0B;gCAC1B,wBAAwB;;;;;;0CAI1B,8OAAC,0MAAA,CAAA,oBAAiB;gCAChB,kBAAkB;gCAClB,MAAM;gCACN,iBAAiB;gCACjB,oBAAoB;gCACpB,sBAAsB;gCACtB,4BAA4B;gCAC5B,kBAAkB,CAAC,CAAC;gCACpB,iBAAiB,CAAC,CAAC;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC,kMAAA,CAAA,gBAAa;gBACZ,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,cAAc,wBAAwB,YAAY,oBAAoB;gBACtE,cAAc,wBAAwB,YAAY,oBAAoB;gBACtE,kBAAkB;;;;;;;;;;;;AAI1B;uCAEe", "debugId": null}}, {"offset": {"line": 8852, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/index.tsx"], "sourcesContent": ["export { default } from './MapComponent';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 8870, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/page.tsx"], "sourcesContent": ["'use client'; // Needs to be client component for auth check & conditional rendering\r\n\r\nimport React, { useState, useEffect } from 'react'; // Import useState and useEffect\r\nimport LandingPage from '@/app/Landingpage';\r\nimport MapComponent from '@/app/Homepage';\r\n// Import the useAuth hook instead of the context\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\nexport default function HomePage() {\r\n  // Use the useAuth hook\r\n  const { user, isLoading: isLoadingAuth, initialAuthDone } = useAuth();\r\n  const isAuthenticated = !!user; // Simple check for user existence\r\n\r\n  // State to track if component has mounted on the client\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Set isClient to true only after mounting\r\n  useEffect(() => {\r\n    console.log(\"HomePage: Component mounted on client.\");\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Log the values before conditional rendering\r\n  console.log(\"HomePage: Rendering - isClient:\", isClient, \"isLoadingAuth:\", isLoadingAuth, \"isAuthenticated:\", isAuthenticated, \"initialAuthDone:\", initialAuthDone);\r\n\r\n  // Show loading spinner only while the initial authentication determination is in progress.\r\n  // Once initialAuthDone is true, we rely on the user state for rendering.\r\n  if (!isClient || !initialAuthDone) {\r\n    console.log(\"HomePage: Displaying initial loading spinner.\");\r\n    // Zeige den gleichen Ladeindikator wie in der ProtectedRoute\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-[var(--color-background)]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto\"></div>\r\n          <p className=\"mt-4 text-[var(--color-foreground)]\">Laden...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  console.log(\"HomePage: Rendering main content based on authentication state.\");\r\n  // Conditionally render based on authentication state only after mount and initial auth check are complete\r\n  return isAuthenticated ? <MapComponent /> : <LandingPage />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,oVAAoD,gCAAgC;AACpF;AAAA;AACA;AAAA;AACA,iDAAiD;AACjD;AANA,cAAc,sEAAsE;;;;;;AAQrE,SAAS;IACtB,uBAAuB;IACvB,MAAM,EAAE,IAAI,EAAE,WAAW,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClE,MAAM,kBAAkB,CAAC,CAAC,MAAM,kCAAkC;IAElE,wDAAwD;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QACZ,YAAY;IACd,GAAG,EAAE;IAEL,8CAA8C;IAC9C,QAAQ,GAAG,CAAC,mCAAmC,UAAU,kBAAkB,eAAe,oBAAoB,iBAAiB,oBAAoB;IAEnJ,2FAA2F;IAC3F,yEAAyE;IACzE,IAAI,CAAC,YAAY,CAAC,iBAAiB;QACjC,QAAQ,GAAG,CAAC;QACZ,6DAA6D;QAC7D,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAI3D;IAEA,QAAQ,GAAG,CAAC;IACZ,0GAA0G;IAC1G,OAAO,gCAAkB,8OAAC,uIAAA,CAAA,UAAY;;;;6BAAM,8OAAC,mJAAA,CAAA,UAAW;;;;;AAC1D", "debugId": null}}]}