'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface SectionHeaderProps {
  badge?: {
    icon: LucideIcon;
    text: string;
  };
  title: React.ReactNode;
  description?: string;
  centered?: boolean;
  maxWidth?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  badge,
  title,
  description,
  centered = true,
  maxWidth = "max-w-4xl"
}) => {
  const containerClasses = centered ? "text-center mx-auto" : "";

  return (
    <div className={`${maxWidth} ${containerClasses} mb-12 sm:mb-16 lg:mb-20`}>
      {badge && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 mb-4 sm:mb-6"
        >
          <badge.icon className="w-4 h-4 sm:w-5 sm:h-5 text-primary mr-2 sm:mr-3" />
          <span className="font-semibold text-primary text-sm sm:text-base">
            {badge.text}
          </span>
        </motion.div>
      )}
      
      <motion.h2 
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 leading-tight"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: badge ? 0.1 : 0 }}
      >
        {title}
      </motion.h2>
      
      {description && (
        <motion.p 
          className="text-base sm:text-lg text-muted-foreground leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: badge ? 0.2 : 0.1 }}
        >
          {description}
        </motion.p>
      )}
    </div>
  );
};