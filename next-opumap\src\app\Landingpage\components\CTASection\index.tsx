'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Route, Check, ArrowRight } from 'lucide-react';
import { AnimatedButton } from '../shared';
import { CTA_FEATURES } from '../../utils';

interface CTASectionProps {
  onGetStarted: () => void;
}

export const CTASection: React.FC<CTASectionProps> = ({ onGetStarted }) => {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-32 relative">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10"></div>
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          whileInView={{ opacity: 1, y: 0, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto text-center bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl p-8 sm:p-12 md:p-16 rounded-2xl sm:rounded-3xl border border-border/30 shadow-2xl"
        >
          {/* Icon */}
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-6 sm:mb-8 rounded-full bg-gradient-to-br from-primary to-primary/70 flex items-center justify-center"
          >
            <Route className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </motion.div>
          
          {/* Heading */}
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
            Revolutionieren Sie Ihre <span className="text-primary">Geschäftsentwicklung</span>
          </h2>
          
          {/* Description */}
          <p className="text-base sm:text-lg lg:text-xl text-muted-foreground mb-8 sm:mb-10 max-w-2xl mx-auto leading-relaxed">
            Schließen Sie sich den Unternehmen an, die bereits mit OpuMap ihre lokalen 
            Geschäftspotenziale systematisch erschließen und nachhaltige Partnerschaften aufbauen.
          </p>
          
          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
            <AnimatedButton
              onClick={onGetStarted}
              variant="primary"
              size="large"
              className="w-full sm:w-auto group relative overflow-hidden"
            >
              <span className="relative z-10 flex items-center justify-center gap-2 sm:gap-3">
                <span className="hidden sm:inline">Jetzt starten - kostenfrei</span>
                <span className="sm:hidden">Jetzt starten</span>
                <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform" />
              </span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary opacity-0 group-hover:opacity-100"
                transition={{ duration: 0.3 }}
              />
            </AnimatedButton>
            
            <div className="text-center">
              <div className="text-sm text-muted-foreground mb-1">Oder erst mal</div>
              <button className="text-primary font-semibold hover:underline text-sm sm:text-base">
                Live Demo ansehen →
              </button>
            </div>
          </div>
          
          {/* Feature List */}
          <div className="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-8 mt-8 sm:mt-10 text-xs sm:text-sm text-muted-foreground">
            {CTA_FEATURES.map((feature, index) => (
              <div key={index} className="flex items-center justify-center space-x-2">
                <Check className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};