{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/unternehmensanalyse.ts"], "sourcesContent": ["// Define the expected input data structure\r\ninterface CompanyAnalysisData {\r\n    companyName?: string;\r\n    name?: string;\r\n    address?: string;\r\n    phone?: string;\r\n    website?: string;\r\n    employeeCount?: string | number;\r\n    // Add other fields if necessary, but email is excluded based on original comment\r\n}\r\n\r\n/**\r\n * Runs an analysis on company data using the Perplexity API.\r\n * IMPORTANT: Calling Perplexity API directly from the client exposes the API key.\r\n * Consider moving this logic to a Next.js API route for better security.\r\n * @param companyData - Data of the company to analyze.\r\n * @returns The analysis content as a string.\r\n */\r\nexport async function runAnalysis(companyData: CompanyAnalysisData): Promise<string> {\r\n    // Construct prompt safely\r\n    let prompt = \"Gebe mir in Stichpunkten alle Informationen Über das folgende Unternehmen. Lege dabei den Fokus auf Key metriken die für eine Unternehmensanalyse wichtig sind:\\n\\n\";\r\n    if (companyData.companyName) prompt += `- Unternehmensname: ${companyData.companyName}\\n`;\r\n    if (companyData.name) prompt += `- Name (Ansprechpartner): ${companyData.name}\\n`; // Clarified label\r\n    if (companyData.address) prompt += `- Adresse: ${companyData.address}\\n`;\r\n    if (companyData.phone) prompt += `- Telefonnummer: ${companyData.phone}\\n`;\r\n    if (companyData.website) prompt += `- Webseite: ${companyData.website}\\n`;\r\n    if (companyData.employeeCount) prompt += `- Mitarbeiteranzahl: ${companyData.employeeCount}\\n`;\r\n\r\n    // Get API key\r\n    const token = process.env.NEXT_PUBLIC_PERPLEXITY_API_KEY;\r\n    if (!token) {\r\n        console.error(\"Perplexity API Token (NEXT_PUBLIC_PERPLEXITY_API_KEY) is not defined.\");\r\n        throw new Error(\"Perplexity API Token ist nicht konfiguriert.\");\r\n    }\r\n\r\n    const options = {\r\n        method: 'POST',\r\n        headers: {\r\n            Authorization: `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n            accept: 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n            model: \"sonar\", // Using sonar-small-online (similar to original 'sonar')\r\n            messages: [\r\n                { role: \"system\", content: \"Be precise and concise. Provide key metrics relevant for company analysis in bullet points. No explanations or extra formatting.\" },\r\n                { role: \"user\", content: prompt }\r\n            ],\r\n            // Adjust parameters as needed\r\n            // max_tokens: 3000,\r\n            // temperature: 0.2,\r\n        })\r\n    };\r\n\r\n    try {\r\n        const response = await fetch('https://api.perplexity.ai/chat/completions', options);\r\n        if (!response.ok) {\r\n            const errorText = await response.text();\r\n             console.error(\"Perplexity API Error Response:\", errorText);\r\n            throw new Error(`Perplexity API Fehler (${response.status}): ${errorText.substring(0, 100)}...`);\r\n        }\r\n\r\n        const data = await response.json();\r\n\r\n        // Extract analysis content\r\n        if (data?.choices?.[0]?.message?.content) {\r\n            return data.choices[0].message.content;\r\n        } else {\r\n             console.warn(\"Could not extract analysis content from Perplexity response:\", data);\r\n             // Return a default message or stringified data on failure\r\n             return \"Analyseinformationen konnten nicht extrahiert werden.\";\r\n             // return JSON.stringify(data, null, 2); // Alternative: return raw data string\r\n        }\r\n    } catch (error) { // Use unknown type for error\r\n        console.error(\"Fehler in runAnalysis:\", error);\r\n        const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n        throw new Error(`Fehler bei der Unternehmensanalyse: ${message}`);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;AA6BzB;AAXX,eAAe,YAAY,WAAgC;IAC9D,0BAA0B;IAC1B,IAAI,SAAS;IACb,IAAI,YAAY,WAAW,EAAE,UAAU,CAAC,oBAAoB,EAAE,YAAY,WAAW,CAAC,EAAE,CAAC;IACzF,IAAI,YAAY,IAAI,EAAE,UAAU,CAAC,0BAA0B,EAAE,YAAY,IAAI,CAAC,EAAE,CAAC,EAAE,kBAAkB;IACrG,IAAI,YAAY,OAAO,EAAE,UAAU,CAAC,WAAW,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC;IACxE,IAAI,YAAY,KAAK,EAAE,UAAU,CAAC,iBAAiB,EAAE,YAAY,KAAK,CAAC,EAAE,CAAC;IAC1E,IAAI,YAAY,OAAO,EAAE,UAAU,CAAC,YAAY,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC;IACzE,IAAI,YAAY,aAAa,EAAE,UAAU,CAAC,qBAAqB,EAAE,YAAY,aAAa,CAAC,EAAE,CAAC;IAE9F,cAAc;IACd,MAAM;IACN,uCAAY;;IAGZ;IAEA,MAAM,UAAU;QACZ,QAAQ;QACR,SAAS;YACL,eAAe,CAAC,OAAO,EAAE,OAAO;YAChC,gBAAgB;YAChB,QAAQ;QACZ;QACA,MAAM,KAAK,SAAS,CAAC;YACjB,OAAO;YACP,UAAU;gBACN;oBAAE,MAAM;oBAAU,SAAS;gBAAmI;gBAC9J;oBAAE,MAAM;oBAAQ,SAAS;gBAAO;aACnC;QAIL;IACJ;IAEA,IAAI;QACA,MAAM,WAAW,MAAM,MAAM,8CAA8C;QAC3E,IAAI,CAAC,SAAS,EAAE,EAAE;YACd,MAAM,YAAY,MAAM,SAAS,IAAI;YACpC,QAAQ,KAAK,CAAC,kCAAkC;YACjD,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,UAAU,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;QACnG;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,2BAA2B;QAC3B,IAAI,MAAM,SAAS,CAAC,EAAE,EAAE,SAAS,SAAS;YACtC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;QAC1C,OAAO;YACF,QAAQ,IAAI,CAAC,gEAAgE;YAC7E,0DAA0D;YAC1D,OAAO;QACP,+EAA+E;QACpF;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,SAAS;IACpE;AACJ", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/actions/update-profile.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { User } from '@/contexts/AuthContext';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\nexport async function updateProfile(formData: FormData) {\r\n  try {\r\n    const supabase = await createClient();\r\n\r\n    // Get the current authenticated user (more secure than getSession)\r\n    const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n    if (userError || !user) {\r\n      console.error('Authentication error:', userError);\r\n      return { error: 'Nicht authentifiziert' };\r\n    }\r\n\r\n    const userId = user.id;\r\n\r\n    // Extract profile data from form\r\n    const profileData: Partial<User> & { updated_at: string } = {\r\n      name: formData.get('name') as string,\r\n      company_name: formData.get('company_name') as string,\r\n      address: formData.get('address') as string,\r\n      phone: formData.get('phone') as string,\r\n      website: formData.get('website') as string,\r\n      employee_count: formData.get('employee_count') as string,\r\n      company_info_points: formData.get('company_info_points') as string,\r\n      updated_at: new Date().toISOString() // Add updated_at timestamp\r\n    };\r\n\r\n    console.log('Updating profile for user:', userId);\r\n    console.log('Profile data:', profileData);\r\n\r\n    // Update the profile in the database\r\n    const { error, data } = await supabase\r\n      .from('profiles')\r\n      .update(profileData)\r\n      .eq('id', userId)\r\n      .select();\r\n\r\n    if (error) {\r\n      console.error('Error updating profile:', error);\r\n      return { error: error.message };\r\n    }\r\n\r\n    console.log('Profile updated successfully:', data);\r\n\r\n    // Revalidate the profile page to show updated data\r\n    revalidatePath('/profile');\r\n\r\n    // Force cache invalidation for the profile data\r\n    revalidatePath('/', 'layout');\r\n\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('Unexpected error in updateProfile:', error);\r\n    const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n    return { error: `Fehler beim Aktualisieren des Profils: ${message}` };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAMsB,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/card.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAEA;AAJA;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/input.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport type InputProps = React.InputHTMLAttributes<HTMLInputElement>;\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  src?: string;\r\n  alt?: string;\r\n  initials?: string;\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n}\r\n\r\nexport const Avatar: React.FC<AvatarProps> = ({ src, alt, initials, size = \"md\", className, ...props }) => {\r\n  const sizeClasses =\r\n    size === \"sm\"\r\n      ? \"w-8 h-8 text-base\"\r\n      : size === \"lg\"\r\n      ? \"w-20 h-20 text-3xl\"\r\n      : \"w-16 h-16 text-xl\";\r\n  \r\n  const dimensions = size === \"sm\" \r\n    ? { width: 32, height: 32 } \r\n    : size === \"lg\" \r\n      ? { width: 80, height: 80 } \r\n      : { width: 64, height: 64 };\r\n      \r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"rounded-full bg-muted flex items-center justify-center font-bold text-muted-foreground overflow-hidden\",\r\n        sizeClasses,\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {src ? (\r\n        <Image \r\n          src={src} \r\n          alt={alt || \"\"} \r\n          width={dimensions.width}\r\n          height={dimensions.height}\r\n          className=\"object-cover w-full h-full rounded-full\"\r\n        />\r\n      ) : (\r\n        <span>{initials}</span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,MAAM,SAAgC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO;IACpG,MAAM,cACJ,SAAS,OACL,sBACA,SAAS,OACT,uBACA;IAEN,MAAM,aAAa,SAAS,OACxB;QAAE,OAAO;QAAI,QAAQ;IAAG,IACxB,SAAS,OACP;QAAE,OAAO;QAAI,QAAQ;IAAG,IACxB;QAAE,OAAO;QAAI,QAAQ;IAAG;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA,aACA;QAED,GAAG,KAAK;kBAER,oBACC,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK;YACL,KAAK,OAAO;YACZ,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,WAAU;;;;;iCAGZ,6LAAC;sBAAM;;;;;;;;;;;AAIf;KApCa", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/notification.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface NotificationProps {\r\n  type: 'success' | 'error';\r\n  message: string;\r\n  details?: string;\r\n  isVisible: boolean;\r\n  onDismiss: () => void;\r\n}\r\n\r\nexport const Notification: React.FC<NotificationProps> = ({\r\n  type,\r\n  message,\r\n  details,\r\n  isVisible,\r\n  onDismiss,\r\n}) => {\r\n  const baseClasses = 'fixed top-4 left-1/2 transform -translate-x-1/2 p-4 rounded shadow-lg z-50 flex items-center space-x-4 transition-all duration-300 ease-in-out';\r\n  const typeClasses = type === 'error'\r\n    ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'\r\n    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';\r\n  const visibilityClasses = isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-5 pointer-events-none';\r\n\r\n  return (\r\n    <div className={`${baseClasses} ${typeClasses} ${visibilityClasses}`}>\r\n      <span className=\"flex-grow\">\r\n        {message}\r\n        {details && (\r\n            <span className=\"block text-xs italic mt-1\">(Details: {details})</span>\r\n        )}\r\n      </span>\r\n      <button\r\n        onClick={onDismiss}\r\n        aria-label=\"Benachrichtigung schließen\"\r\n        className=\"text-xl font-bold leading-none flex-shrink-0 focus:outline-none hover:text-gray-700 dark:hover:text-gray-300\"\r\n      >\r\n        &times;\r\n      </button>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAAA;;AAYO,MAAM,eAA4C,CAAC,EACxD,IAAI,EACJ,OAAO,EACP,OAAO,EACP,SAAS,EACT,SAAS,EACV;IACC,MAAM,cAAc;IACpB,MAAM,cAAc,SAAS,UACzB,iEACA;IACJ,MAAM,oBAAoB,YAAY,8BAA8B;IAEpE,qBACE,6LAAC;QAAI,WAAW,GAAG,YAAY,CAAC,EAAE,YAAY,CAAC,EAAE,mBAAmB;;0BAClE,6LAAC;gBAAK,WAAU;;oBACb;oBACA,yBACG,6LAAC;wBAAK,WAAU;;4BAA4B;4BAAW;4BAAQ;;;;;;;;;;;;;0BAGrE,6LAAC;gBACC,SAAS;gBACT,cAAW;gBACX,WAAU;0BACX;;;;;;;;;;;;AAKP;KA9Ba", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/NotificationState.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\n\r\nexport type NotificationType = 'success' | 'error';\r\n\r\nexport function useNotification(initialState: boolean = false) {\r\n  const [isVisible, setIsVisible] = useState(initialState);\r\n  const [message, setMessage] = useState<string>('');\r\n  const [type, setType] = useState<NotificationType>('success');\r\n  const [details, setDetails] = useState<string | undefined>(undefined);\r\n  const showNotification = useCallback((msg: string, type: NotificationType = 'success', details?: string) => {\r\n    setMessage(msg);\r\n    setType(type);\r\n    setDetails(details);\r\n    setIsVisible(true);\r\n  }, []);\r\n  const hideNotification = useCallback(() => setIsVisible(false), []);\r\n  return { isVisible, message, type, details, showNotification, hideNotification };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAIO,SAAS,gBAAgB,eAAwB,KAAK;;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC,KAAa,OAAyB,SAAS,EAAE;YACrF,WAAW;YACX,QAAQ;YACR,WAAW;YACX,aAAa;QACf;wDAAG,EAAE;IACL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,IAAM,aAAa;wDAAQ,EAAE;IAClE,OAAO;QAAE;QAAW;QAAS;QAAM;QAAS;QAAkB;IAAiB;AACjF;GAbgB", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/MarkdownEditor.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport '@uiw/react-md-editor/markdown-editor.css';\r\nimport '@uiw/react-markdown-preview/markdown.css';\r\nimport MdEditor from '@uiw/react-md-editor';\r\nimport { useTheme } from 'next-themes';  // Corrected import based on codebase search\r\n\r\ninterface MarkdownEditorProps {\r\n  value: string;\r\n  onChange: (value?: string) => void;\r\n  height?: number;\r\n  preview?: 'edit' | 'preview' | 'live';\r\n  className?: string;\r\n}\r\n\r\nconst MarkdownEditor: React.FC<MarkdownEditorProps> = ({ value, onChange, height = 400, preview = 'edit', className }) => {\r\n  const { theme } = useTheme();  // Get the current theme from next-themes\r\n  const themeClass = theme === 'dark' ? 'dark-theme' : 'light-theme';  // Dynamically set a class for styling\r\n  \r\n  const combinedClassName = [themeClass, className].filter(Boolean).join(' ');\r\n\r\n  return (\r\n    <MdEditor\r\n      value={value}\r\n      onChange={onChange}\r\n      height={height}\r\n      preview={preview}\r\n      className={combinedClassName}  // Apply class for theme-based styling; ensure corresponding CSS is defined\r\n    />\r\n  );\r\n};\r\n\r\nexport default MarkdownEditor;\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA,kQAAyC,4CAA4C;;;AALrF;;;;;AAeA,MAAM,iBAAgD,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,GAAG,EAAE,UAAU,MAAM,EAAE,SAAS,EAAE;;IACnH,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD,KAAM,yCAAyC;IACxE,MAAM,aAAa,UAAU,SAAS,eAAe,eAAgB,sCAAsC;IAE3G,MAAM,oBAAoB;QAAC;QAAY;KAAU,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvE,qBACE,6LAAC,iLAAA,CAAA,UAAQ;QACP,OAAO;QACP,UAAU;QACV,QAAQ;QACR,SAAS;QACT,WAAW;;;;;;AAGjB;GAfM;;QACc,mJAAA,CAAA,WAAQ;;;KADtB;uCAiBS", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/FloatingBackground.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styled from 'styled-components';\r\n\r\nconst FloatingBackground = () => {\r\n  return (\r\n    <StyledWrapper>\r\n      <svg id=\"background_svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 800\" fill=\"none\" preserveAspectRatio=\"xMidYMid slice\">\r\n        <defs>\r\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--primary-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--primary-light)\" />\r\n          </linearGradient>\r\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"100%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"var(--accent-dark)\" />\r\n            <stop offset=\"100%\" stopColor=\"var(--accent-light)\" />\r\n          </linearGradient>\r\n        </defs>\r\n\r\n        {/* Dynamic Shapes inspired by loader.tsx geometric patterns */}\r\n        <path\r\n          className=\"shape float-animation\"\r\n          d=\"M 500 0 C 400 150, 600 250, 500 400 S 700 550, 800 400 S 900 250, 800 100 S 600 0, 500 0 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <circle\r\n          className=\"shape float-animation-alt\"\r\n          cx=\"1000\"\r\n          cy=\"200\"\r\n          r=\"150\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n        <path\r\n          className=\"shape float-animation-delay\"\r\n          d=\"M 200 600 Q 300 750, 500 700 T 700 650 Q 600 500, 400 550 Z\"\r\n          fill=\"url(#gradient1)\"\r\n          fillOpacity=\"0.15\"\r\n        />\r\n        <ellipse\r\n          className=\"shape float-animation-longer\"\r\n          cx=\"300\"\r\n          cy=\"100\"\r\n          rx=\"100\"\r\n          ry=\"50\"\r\n          transform=\"rotate(45 300 100)\"\r\n          fill=\"url(#gradient2)\"\r\n          fillOpacity=\"0.1\"\r\n        />\r\n\r\n        {/* Pulsating dots representing connection points */}\r\n        <circle className=\"dot pulse-animation-1\" cx=\"400\" cy=\"200\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-2\" cx=\"700\" cy=\"500\" r=\"8\" fill=\"var(--primary)\" />\r\n        <circle className=\"dot pulse-animation-3\" cx=\"1100\" cy=\"100\" r=\"8\" fill=\"var(--primary)\" />\r\n\r\n        {/* Connecting lines between dots */}\r\n        <line\r\n          className=\"connecting-line draw-animation-1\"\r\n          x1=\"400\"\r\n          y1=\"200\"\r\n          x2=\"700\"\r\n          y2=\"500\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n        <line\r\n          className=\"connecting-line draw-animation-2\"\r\n          x1=\"700\"\r\n          y1=\"500\"\r\n          x2=\"1100\"\r\n          y2=\"100\"\r\n          stroke=\"var(--primary)\"\r\n          strokeWidth=\"2\"\r\n          strokeDasharray=\"1000\"\r\n          strokeDashoffset=\"1000\"\r\n        />\r\n      </svg>\r\n    </StyledWrapper>\r\n  );\r\n};\r\n\r\nconst StyledWrapper = styled.div`\r\n  position: fixed;\r\n  inset: 0;\r\n  overflow: hidden;\r\n  z-index: 0;\r\n  pointer-events: none;\r\n  width: 100vw;\r\n  height: 100vh;\r\n\r\n  #background_svg {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .shape {\r\n    transform-origin: center center;\r\n  }\r\n\r\n  /* Custom CSS Variables for gradients and colors, assuming these exist in the theme */\r\n  /* If not, these would need to be defined or mapped to existing Tailwind colors */\r\n  --primary-dark: hsl(var(--primary));\r\n  --primary-light: hsl(var(--primary) / 0.7);\r\n  --accent-dark: hsl(var(--accent));\r\n  --accent-light: hsl(var(--accent) / 0.7);\r\n\r\n\r\n  @keyframes floatAndBounce {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(10px, -20px) rotate(5deg); }\r\n    50% { transform: translate(-10px, 20px) rotate(-5deg); }\r\n    75% { transform: translate(5px, -10px) rotate(2deg); }\r\n  }\r\n\r\n  @keyframes floatAndBounceAlt {\r\n    0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n    25% { transform: translate(-15px, 10px) rotate(-3deg); }\r\n    50% { transform: translate(15px, -10px) rotate(3deg); }\r\n    75% { transform: translate(-5px, 5px) rotate(-1deg); }\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0%, 100% { transform: scale(1); opacity: 0.8; }\r\n    50% { transform: scale(1.2); opacity: 1; }\r\n  }\r\n\r\n  @keyframes drawLine {\r\n    to {\r\n      stroke-dashoffset: 0;\r\n    }\r\n  }\r\n\r\n  .float-animation {\r\n    animation: floatAndBounce 20s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-alt {\r\n    animation: floatAndBounceAlt 25s infinite ease-in-out;\r\n  }\r\n\r\n  .float-animation-delay {\r\n    animation: floatAndBounce 22s infinite ease-in-out 2s; /* Add a delay */\r\n  }\r\n\r\n  .float-animation-longer {\r\n    animation: floatAndBounceAlt 28s infinite ease-in-out 4s; /* Longer duration, more delay */\r\n  }\r\n\r\n  .pulse-animation-1 {\r\n    animation: pulse 2s infinite ease-in-out;\r\n  }\r\n\r\n  .pulse-animation-2 {\r\n    animation: pulse 2s infinite ease-in-out 0.5s;\r\n  }\r\n\r\n  .pulse-animation-3 {\r\n    animation: pulse 2s infinite ease-in-out 1s;\r\n  }\r\n\r\n  .draw-animation-1 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 1s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n\r\n  .draw-animation-2 {\r\n    animation: drawLine 3s ease-out forwards;\r\n    animation-delay: 3s; /* Delay for sequential drawing */\r\n    animation-fill-mode: forwards;\r\n  }\r\n`;\r\n\r\nexport default FloatingBackground; "], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAAqB;IACzB,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAI,IAAG;YAAiB,OAAM;YAA6B,SAAQ;YAAe,MAAK;YAAO,qBAAoB;;8BACjH,6LAAC;;sCACC,6LAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC1D,6LAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,6LAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;sCAEhC,6LAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAO,IAAG;4BAAO,IAAG;;8CAC5D,6LAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,6LAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,aAAY;;;;;;8BAId,6LAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,6LAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BACvE,6LAAC;oBAAO,WAAU;oBAAwB,IAAG;oBAAO,IAAG;oBAAM,GAAE;oBAAI,MAAK;;;;;;8BAGxE,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;8BAEnB,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,kBAAiB;;;;;;;;;;;;;;;;;AAK3B;KA9EM;AAgFN,MAAM,gBAAgB,yLAAA,CAAA,UAAM,CAAC,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FjC,CAAC;MA1FK;uCA4FS", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/Profile.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, FormEvent } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { runAnalysis } from '@/api/unternehmensanalyse';\r\nimport { updateProfile } from './actions/update-profile';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Edit, Settings, Activity, Loader, User, Building, ArrowRight, CheckCircle, BrainCircuit } from 'lucide-react';\r\nimport { Avatar } from '@/components/ui/avatar';\r\nimport { Notification } from '@/components/ui/notification';\r\nimport { useNotification } from './NotificationState';\r\nimport MarkdownEditor from '@/components/MarkdownEditor';\r\nimport FloatingBackground from '../Landingpage/components/shared/FloatingBackground'; // Import FloatingBackground\r\n\r\nconst Profile: React.FC = () => {\r\n  const [activeSection, setActiveSection] = useState('profile');\r\n  // Use the auth hook\r\n  const { user, isLoading, refreshUserProfile } = useAuth();\r\n\r\n  // State with types\r\n  const [companyName, setCompanyName] = useState<string>('');\r\n  const [name, setName] = useState<string>('');\r\n  const [email, setEmail] = useState<string>('');\r\n  const [address, setAddress] = useState<string>('');\r\n  const [phone, setPhone] = useState<string>('');\r\n  const [website, setWebsite] = useState<string>('');\r\n  // Ensure employeeCount is treated as string for input, but potentially number elsewhere\r\n  const [employeeCount, setEmployeeCount] = useState<string | number>('');\r\n  const [companyInfoPoints, setCompanyInfoPoints] = useState<string>('');\r\n\r\n  const { isVisible, message, type, details, showNotification, hideNotification } = useNotification();\r\n  const notificationTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);\r\n  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);\r\n  const [isSaving, setIsSaving] = useState<boolean>(false);\r\n\r\n  // Update local form state when user data loads or changes\r\n  useEffect(() => {\r\n    if (!isLoading && user) {\r\n      setCompanyName(user.company_name ?? '');\r\n      setName(user.name ?? '');\r\n      setEmail(user.email ?? '');\r\n      setAddress(user.address ?? '');\r\n      setPhone(user.phone ?? '');\r\n      setWebsite(user.website ?? '');\r\n      setEmployeeCount(user.employee_count ?? '');\r\n      setCompanyInfoPoints(user.company_info_points ?? '');\r\n    }\r\n     // Clear form if user logs out (user becomes null) and loading is done\r\n     else if (!isLoading && !user) {\r\n        setCompanyName('');\r\n        setName('');\r\n        setEmail('');\r\n        setAddress('');\r\n        setPhone('');\r\n        setWebsite('');\r\n        setEmployeeCount('');\r\n        setCompanyInfoPoints('');\r\n     }\r\n  }, [user, isLoading]);\r\n\r\n  // Type the event\r\n  const handleSave = async (e: FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n\r\n    // Set saving state to show loading indicator\r\n    setIsSaving(true);\r\n\r\n    // Create a FormData object from the form\r\n    const formData = new FormData();\r\n    formData.append('name', name);\r\n    formData.append('email', email || '');\r\n    formData.append('company_name', companyName);\r\n    formData.append('address', address);\r\n    formData.append('phone', phone);\r\n    formData.append('website', website);\r\n    formData.append('employee_count', employeeCount.toString());\r\n    formData.append('company_info_points', companyInfoPoints);\r\n\r\n    try {\r\n      // Use the server action to update the profile\r\n      const result = await updateProfile(formData);\r\n\r\n      // Check if result is undefined or has an error property\r\n      if (!result || result.error) {\r\n        // If result is undefined, throw a generic error, otherwise throw the result.error\r\n        throw new Error(result ? result.error : 'Unbekannter Fehler beim Speichern des Profils');\r\n      }\r\n\r\n      // Refresh the user profile data to get the latest changes\r\n      const refreshSuccess = await refreshUserProfile();\r\n\r\n      console.log(\"Profile updated and refreshed successfully:\", refreshSuccess);\r\n\r\n      // Show success notification\r\n      showNotification('Profil erfolgreich aktualisiert.', 'success');\r\n      if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);\r\n      notificationTimeoutRef.current = setTimeout(hideNotification, 5000);\r\n\r\n    } catch (error) { // Use unknown type for error\r\n      console.error(\"Profile update error:\", error);\r\n      const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n      showNotification(`Fehler: ${message}`, 'error');\r\n      if (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);\r\n      notificationTimeoutRef.current = setTimeout(hideNotification, 5000);\r\n    } finally {\r\n      // Reset saving state\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleAnalyse = async () => {\r\n    // removed obsolete setMessage('');\r\n    setIsAnalyzing(true);\r\n\r\n    // Define type for analysis input data\r\n    interface CompanyAnalysisData {\r\n        companyName: string;\r\n        name: string;\r\n        address: string;\r\n        phone: string;\r\n        website: string;\r\n        employeeCount: string | number;\r\n    }\r\n\r\n    const companyData: CompanyAnalysisData = {\r\n      companyName,\r\n      name,\r\n      address,\r\n      phone,\r\n      website,\r\n      employeeCount\r\n    };\r\n    showNotification('Unternehmensanalyse wird durchgeführt, bitte warten...', 'success');\r\n    try {\r\n      // Assuming runAnalysis returns a string\r\n      const analysisResult: string = await runAnalysis(companyData);\r\n      setCompanyInfoPoints(analysisResult);\r\n      showNotification('Analyse abgeschlossen.', 'success');\r\nif (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);\r\nnotificationTimeoutRef.current = setTimeout(hideNotification, 3000);\r\n    } catch (error) { // Use unknown type for error\r\n      const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n      showNotification(`Analyse fehlgeschlagen: ${message}`, 'error');\r\nif (notificationTimeoutRef.current) clearTimeout(notificationTimeoutRef.current);\r\nnotificationTimeoutRef.current = setTimeout(hideNotification, 3000);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  // Loading state from context\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"max-w-4xl mx-auto p-4\">\r\n        <p className=\"text-center text-gray-500\">Lade Profil...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // No user state (either not logged in or context failed)\r\n  if (!user) {\r\n    return (\r\n      <div className=\"max-w-xl mx-auto mt-10 p-6 border rounded shadow-lg text-center\">\r\n        <h2 className=\"text-xl font-semibold mb-4\">Zugriff verweigert</h2>\r\n        <p>Sie müssen eingeloggt sein, um Ihr Profil anzuzeigen und zu bearbeiten.</p>\r\n        {/* Optional: Add a link to the login page */}\r\n         {/* <Link href=\"/login\"><a className=\"text-blue-500 hover:underline mt-4 inline-block\">Zum Login</a></Link> */}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // User is loaded and exists\r\n  return (\r\n    <div className=\"relative min-h-screen w-full bg-background text-foreground overflow-hidden\">\r\n      <FloatingBackground />\r\n      <Notification\r\n        type={type}\r\n        message={message}\r\n        details={details}\r\n        isVisible={isVisible && !!message}\r\n        onDismiss={hideNotification}\r\n      />\r\n      <main className=\"relative z-10 max-w-6xl mx-auto my-8 p-4\">\r\n        <form className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\" onSubmit={handleSave}>\r\n          {/* Left Column: Navigation & Status */}\r\n          <motion.div \r\n            className=\"lg:col-span-1 space-y-6\"\r\n            initial={{ opacity: 0, x: -20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6, ease: 'easeOut' }}\r\n          >\r\n            <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6 text-center\">\r\n              <div className=\"relative inline-block mb-4\">\r\n                <Avatar\r\n                  src={user.avatarUrl ?? undefined}\r\n                  initials={user.name ? user.name.split(' ').map((n: string) => n[0]).join('').slice(0,2).toUpperCase() : '?'}\r\n                  alt={user.name ?? undefined}\r\n                  className=\"w-24 h-24 md:w-32 md:h-32 border-4 border-primary/30\"\r\n                />\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"icon\"\r\n                  aria-label=\"Profilbild ändern\"\r\n                  className=\"absolute bottom-0 right-0 bg-card border-2 border-primary/50 shadow-md rounded-full w-10 h-10 flex items-center justify-center hover:bg-primary/10 transition-colors duration-300\"\r\n                >\r\n                  <Edit className=\"w-5 h-5\" />\r\n                </Button>\r\n              </div>\r\n              <h1 className=\"text-2xl font-bold text-foreground truncate\">{user.name}</h1>\r\n              <p className=\"text-base text-muted-foreground truncate mt-1\">{user.email}</p>\r\n            </Card>\r\n\r\n            <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6\">\r\n              <h2 className=\"text-lg font-semibold mb-4\">Navigation</h2>\r\n              <nav className=\"space-y-2\">\r\n                {['profile', 'company', 'analysis'].map(section => (\r\n                  <button \r\n                    key={section}\r\n                    type=\"button\"\r\n                    onClick={() => setActiveSection(section)}\r\n                    className={`w-full text-left flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 ${activeSection === section ? 'bg-primary/20 text-primary font-semibold shadow-inner' : 'hover:bg-muted/50'}`}>\r\n                    {section === 'profile' && <User className=\"w-5 h-5\" />}\r\n                    {section === 'company' && <Building className=\"w-5 h-5\" />}\r\n                    {section === 'analysis' && <BrainCircuit className=\"w-5 h-5\" />}\r\n                    <span className=\"capitalize\">{section === 'analysis' ? 'KI-Analyse' : `${section} Details`}</span>\r\n                    {activeSection === section && \r\n                      <motion.div layoutId=\"active-underline\" className=\"ml-auto\">\r\n                        <ArrowRight className=\"w-5 h-5\" />\r\n                      </motion.div>\r\n                    }\r\n                  </button>\r\n                ))}\r\n              </nav>\r\n            </Card>\r\n\r\n             <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6\">\r\n                <div className=\"flex justify-center md:justify-start gap-4 flex-wrap\">\r\n                    <span className=\"inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-secondary/20 text-secondary text-sm font-semibold shadow-sm\">\r\n                      <Activity className=\"w-4 h-4\" /> Analysen: <span className=\"font-bold\">{user.analysisCount ?? 0}</span>\r\n                    </span>\r\n                    <span className=\"inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-accent/20 text-accent text-sm font-semibold shadow-sm\">\r\n                      <Settings className=\"w-4 h-4\" /> Aktivitäten: <span className=\"font-bold\">{user.activityCount ?? 0}</span>\r\n                    </span>\r\n                </div>\r\n            </Card>\r\n          </motion.div>\r\n\r\n          {/* Right Column: Content Sections */}\r\n          <div className=\"lg:col-span-2\">\r\n            <AnimatePresence mode=\"wait\">\r\n              <motion.div\r\n                key={activeSection}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -20 }}\r\n                transition={{ duration: 0.4, ease: 'easeInOut' }}\r\n              >\r\n                {activeSection === 'profile' && (\r\n                  <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6\">\r\n                    <CardContent className=\"pt-6 pb-0 space-y-6\">\r\n                      <div className=\"flex items-center gap-3 mb-6\">\r\n                         <User className=\"w-6 h-6 text-primary\" />\r\n                         <h2 className=\"text-2xl font-semibold text-foreground\">Persönliche Daten</h2>\r\n                      </div>\r\n                      <div className=\"grid md:grid-cols-2 gap-6\">\r\n                        <div>\r\n                          <Label htmlFor=\"name\">Ansprechpartner Name</Label>\r\n                          <Input id=\"name\" value={name} onChange={(e) => setName(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                        </div>\r\n                        <div>\r\n                          <Label htmlFor=\"email\">Email</Label>\r\n                          <Input id=\"email\" type=\"email\" value={email} onChange={(e) => setEmail(e.target.value)} className=\"mt-1 bg-background/50\" disabled readOnly />\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n\r\n                {activeSection === 'company' && (\r\n                  <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6\">\r\n                    <CardContent className=\"pt-6 pb-0 space-y-6\">\r\n                      <div className=\"flex items-center gap-3 mb-6\">\r\n                        <Building className=\"w-6 h-6 text-primary\" />\r\n                        <h2 className=\"text-2xl font-semibold text-foreground\">Unternehmensprofil</h2>\r\n                      </div>\r\n                      <div className=\"grid md:grid-cols-2 gap-6\">\r\n                         <div>\r\n                           <Label htmlFor=\"companyName\">Unternehmensname</Label>\r\n                           <Input id=\"companyName\" value={companyName} onChange={(e) => setCompanyName(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                         </div>\r\n                         <div>\r\n                           <Label htmlFor=\"address\">Adresse</Label>\r\n                           <Input id=\"address\" value={address} onChange={(e) => setAddress(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                         </div>\r\n                         <div>\r\n                           <Label htmlFor=\"phone\">Telefonnummer</Label>\r\n                           <Input id=\"phone\" type=\"tel\" value={phone} onChange={(e) => setPhone(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                         </div>\r\n                         <div>\r\n                           <Label htmlFor=\"website\">Webseite</Label>\r\n                           <Input id=\"website\" type=\"url\" placeholder=\"https://beispiel.de\" value={website} onChange={(e) => setWebsite(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                         </div>\r\n                         <div>\r\n                           <Label htmlFor=\"employeeCount\">Mitarbeiteranzahl</Label>\r\n                           <Input id=\"employeeCount\" type=\"number\" min=\"0\" value={employeeCount} onChange={(e) => setEmployeeCount(e.target.value)} className=\"mt-1 bg-background/50\" />\r\n                         </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n                {activeSection === 'analysis' && (\r\n                  <Card className=\"bg-background/80 backdrop-blur-md border-border/20 shadow-lg rounded-2xl p-6\">\r\n                    <CardContent className=\"pt-6 pb-0 space-y-6\">\r\n                      <div className=\"flex items-center gap-3 mb-6\">\r\n                        <BrainCircuit className=\"w-6 h-6 text-primary\" />\r\n                        <h2 className=\"text-2xl font-semibold text-foreground\">Unternehmensinformation & KI-Analyse</h2>\r\n                      </div>\r\n                      <div>\r\n                        <Label htmlFor=\"companyInfoPoints\">Beschreiben Sie Ihr Unternehmen, Ihre Ziele und was Sie einzigartig macht. Unsere KI wird dies analysieren, um die besten Partner für Sie zu finden.</Label>\r\n                        <div className='mt-2 rounded-lg border border-border/20 overflow-hidden'>\r\n                          <MarkdownEditor\r\n                            value={companyInfoPoints}\r\n                            onChange={(value?: string) => setCompanyInfoPoints(value || '')}\r\n                            height={400}\r\n                            preview='edit'\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-end pt-4\">\r\n                        <Button variant=\"default\" onClick={handleAnalyse} disabled={isAnalyzing} className=\"bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg transition-transform transform hover:scale-105\">\r\n                          {isAnalyzing ? (\r\n                            <>\r\n                              <Loader className=\"mr-2 animate-spin\" /> Analyse läuft...\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <BrainCircuit className=\"mr-2\" /> KI-Analyse durchführen\r\n                            </>\r\n                          )}\r\n                        </Button>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )}\r\n              </motion.div>\r\n            </AnimatePresence>\r\n          </div>\r\n\r\n          {/* Save Button - always visible */}\r\n          <motion.div \r\n            className=\"lg:col-span-3 mt-6 flex justify-end\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.3, ease: 'easeOut' }}\r\n          >\r\n            <Button type=\"submit\" size=\"lg\" variant=\"default\" disabled={isSaving} className=\"bg-green-600 hover:bg-green-700 text-white shadow-lg transition-transform transform hover:scale-105\">\r\n              {isSaving ? (\r\n                <>\r\n                  <Loader className=\"mr-2 animate-spin\" /> Speichern...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <CheckCircle className=\"mr-2\" /> Änderungen speichern\r\n                </>\r\n              )}\r\n            </Button>\r\n          </motion.div>\r\n        </form>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,+SAAsF,4BAA4B;;;AAhBlH;;;;;;;;;;;;;;;;AAkBA,MAAM,UAAoB;;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,oBAAoB;IACpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEtD,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,wFAAwF;IACxF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,kBAAe,AAAD;IAChG,MAAM,yBAAyB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAwB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAElD,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,aAAa,MAAM;gBACtB,eAAe,KAAK,YAAY,IAAI;gBACpC,QAAQ,KAAK,IAAI,IAAI;gBACrB,SAAS,KAAK,KAAK,IAAI;gBACvB,WAAW,KAAK,OAAO,IAAI;gBAC3B,SAAS,KAAK,KAAK,IAAI;gBACvB,WAAW,KAAK,OAAO,IAAI;gBAC3B,iBAAiB,KAAK,cAAc,IAAI;gBACxC,qBAAqB,KAAK,mBAAmB,IAAI;YACnD,OAEM,IAAI,CAAC,aAAa,CAAC,MAAM;gBAC3B,eAAe;gBACf,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,qBAAqB;YACxB;QACH;4BAAG;QAAC;QAAM;KAAU;IAEpB,iBAAiB;IACjB,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,6CAA6C;QAC7C,YAAY;QAEZ,yCAAyC;QACzC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,SAAS,SAAS;QAClC,SAAS,MAAM,CAAC,gBAAgB;QAChC,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,SAAS;QACzB,SAAS,MAAM,CAAC,WAAW;QAC3B,SAAS,MAAM,CAAC,kBAAkB,cAAc,QAAQ;QACxD,SAAS,MAAM,CAAC,uBAAuB;QAEvC,IAAI;YACF,8CAA8C;YAC9C,MAAM,SAAS,MAAM,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE;YAEnC,wDAAwD;YACxD,IAAI,CAAC,UAAU,OAAO,KAAK,EAAE;gBAC3B,kFAAkF;gBAClF,MAAM,IAAI,MAAM,SAAS,OAAO,KAAK,GAAG;YAC1C;YAEA,0DAA0D;YAC1D,MAAM,iBAAiB,MAAM;YAE7B,QAAQ,GAAG,CAAC,+CAA+C;YAE3D,4BAA4B;YAC5B,iBAAiB,oCAAoC;YACrD,IAAI,uBAAuB,OAAO,EAAE,aAAa,uBAAuB,OAAO;YAC/E,uBAAuB,OAAO,GAAG,WAAW,kBAAkB;QAEhE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,iBAAiB,CAAC,QAAQ,EAAE,SAAS,EAAE;YACvC,IAAI,uBAAuB,OAAO,EAAE,aAAa,uBAAuB,OAAO;YAC/E,uBAAuB,OAAO,GAAG,WAAW,kBAAkB;QAChE,SAAU;YACR,qBAAqB;YACrB,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,mCAAmC;QACnC,eAAe;QAYf,MAAM,cAAmC;YACvC;YACA;YACA;YACA;YACA;YACA;QACF;QACA,iBAAiB,0DAA0D;QAC3E,IAAI;YACF,wCAAwC;YACxC,MAAM,iBAAyB,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE;YACjD,qBAAqB;YACrB,iBAAiB,0BAA0B;YACjD,IAAI,uBAAuB,OAAO,EAAE,aAAa,uBAAuB,OAAO;YAC/E,uBAAuB,OAAO,GAAG,WAAW,kBAAkB;QAC1D,EAAE,OAAO,OAAO;YACd,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACzD,iBAAiB,CAAC,wBAAwB,EAAE,SAAS,EAAE;YAC7D,IAAI,uBAAuB,OAAO,EAAE,aAAa,uBAAuB,OAAO;YAC/E,uBAAuB,OAAO,GAAG,WAAW,kBAAkB;QAC1D,SAAU;YACR,eAAe;QACjB;IACF;IAEA,6BAA6B;IAC7B,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;IAG/C;IAEA,yDAAyD;IACzD,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;8BAAE;;;;;;;;;;;;IAKT;IAEA,4BAA4B;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2KAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC,2IAAA,CAAA,eAAY;gBACX,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,WAAW,aAAa,CAAC,CAAC;gBAC1B,WAAW;;;;;;0BAEb,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAK,WAAU;oBAAwC,UAAU;;sCAEhE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;;8CAE7C,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,KAAK,KAAK,SAAS,IAAI;oDACvB,UAAU,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,GAAE,GAAG,WAAW,KAAK;oDACxG,KAAK,KAAK,IAAI,IAAI;oDAClB,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,cAAW;oDACX,WAAU;8DAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,6LAAC;4CAAG,WAAU;sDAA+C,KAAK,IAAI;;;;;;sDACtE,6LAAC;4CAAE,WAAU;sDAAiD,KAAK,KAAK;;;;;;;;;;;;8CAG1E,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAW;gDAAW;6CAAW,CAAC,GAAG,CAAC,CAAA,wBACtC,6LAAC;oDAEC,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,WAAW,CAAC,0FAA0F,EAAE,kBAAkB,UAAU,0DAA0D,qBAAqB;;wDAClN,YAAY,2BAAa,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACzC,YAAY,2BAAa,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAC7C,YAAY,4BAAc,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACnD,6LAAC;4DAAK,WAAU;sEAAc,YAAY,aAAa,eAAe,GAAG,QAAQ,QAAQ,CAAC;;;;;;wDACzF,kBAAkB,yBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DAAC,UAAS;4DAAmB,WAAU;sEAChD,cAAA,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;mDAVrB;;;;;;;;;;;;;;;;8CAkBZ,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;kEAAW,6LAAC;wDAAK,WAAU;kEAAa,KAAK,aAAa,IAAI;;;;;;;;;;;;0DAEhG,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;kEAAc,6LAAC;wDAAK,WAAU;kEAAa,KAAK,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAY;;wCAE9C,kBAAkB,2BACjB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACZ,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;;;;;;;kEAE1D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAO;;;;;;kFACtB,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAO,OAAO;wEAAM,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;0EAEpF,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;kFACvB,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAQ,MAAK;wEAAQ,OAAO;wEAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;wEAAwB,QAAQ;wEAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAOpJ,kBAAkB,2BACjB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;;0EACZ,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAc;;;;;;kFAC7B,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAc,OAAO;wEAAa,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;0EAEzG,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;kFACzB,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAU,OAAO;wEAAS,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;0EAE7F,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;kFACvB,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAQ,MAAK;wEAAM,OAAO;wEAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;0EAElG,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;kFACzB,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAU,MAAK;wEAAM,aAAY;wEAAsB,OAAO;wEAAS,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;0EAE1I,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAgB;;;;;;kFAC/B,6LAAC,oIAAA,CAAA,QAAK;wEAAC,IAAG;wEAAgB,MAAK;wEAAS,KAAI;wEAAI,OAAO;wEAAe,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wEAAG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAM7I,kBAAkB,4BACjB,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;0EACxB,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;;;;;;;kEAEzD,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAoB;;;;;;0EACnC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;oEACb,OAAO;oEACP,UAAU,CAAC,QAAmB,qBAAqB,SAAS;oEAC5D,QAAQ;oEACR,SAAQ;;;;;;;;;;;;;;;;;kEAId,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,SAAS;4DAAe,UAAU;4DAAa,WAAU;sEAChF,4BACC;;kFACE,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAsB;;6FAG1C;;kFACE,6LAAC,yNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;;mCArF1C;;;;;;;;;;;;;;;sCAkGX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;gCAAK,MAAM;4BAAU;sCAEzD,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,MAAK;gCAAK,SAAQ;gCAAU,UAAU;gCAAU,WAAU;0CAC7E,yBACC;;sDACE,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAsB;;iEAG1C;;sDACE,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;GAtWM;;QAG4C,kIAAA,CAAA,UAAO;QAa2B,6IAAA,CAAA,kBAAe;;;KAhB7F;uCAwWS", "debugId": null}}, {"offset": {"line": 1794, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useEffect } from 'react';\r\n\r\ninterface ProtectedRouteProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\r\n  const { user, isLoading, initialAuthDone } = useAuth();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    console.log(`ProtectedRoute Effect: initialAuthDone=${initialAuthDone}, isLoading=${isLoading}, user=${!!user}`);\r\n    // Only redirect if initial auth determination is complete, not currently loading, and no user\r\n    if (initialAuthDone && !isLoading && !user) {\r\n      console.log(\"ProtectedRoute: Redirecting to /login\");\r\n      router.push('/login');\r\n    }\r\n  }, [user, isLoading, initialAuthDone, router]);\r\n\r\n  // Show loading spinner only if the initial auth determination hasn't finished yet.\r\n  // Temporary isLoading changes after initial auth is done (e.g., from Fast Refresh) should not show spinner.\r\n  if (!initialAuthDone) {\r\n    console.log(`ProtectedRoute Render: Showing initial loading spinner (initialAuthDone=${initialAuthDone})`);\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-[var(--color-background)]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--color-primary)] mx-auto\"></div>\r\n          <p className=\"mt-4 text-[var(--color-foreground)]\">Laden...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If initial auth is done, not loading, but still no user:\r\n  // The useEffect will handle the redirect. Returning null prevents a flash of content.\r\n  if (!user) {\r\n    console.log(\"ProtectedRoute Render: No user, and redirect effect should handle it. Returning null.\");\r\n    return null;\r\n  }\r\n\r\n  // If initial auth is done, not loading, and user exists: render children.\r\n  console.log(\"ProtectedRoute Render: User authenticated, rendering children.\");\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,gBAAgB,YAAY,EAAE,UAAU,OAAO,EAAE,CAAC,CAAC,MAAM;YAC/G,8FAA8F;YAC9F,IAAI,mBAAmB,CAAC,aAAa,CAAC,MAAM;gBAC1C,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAW;QAAiB;KAAO;IAE7C,mFAAmF;IACnF,4GAA4G;IAC5G,IAAI,CAAC,iBAAiB;QACpB,QAAQ,GAAG,CAAC,CAAC,wEAAwE,EAAE,gBAAgB,CAAC,CAAC;QACzG,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;IAI3D;IAEA,2DAA2D;IAC3D,sFAAsF;IACtF,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,0EAA0E;IAC1E,QAAQ,GAAG,CAAC;IACZ,qBAAO;kBAAG;;AACZ;GArCwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Profile from './Profile'; // Import from local directory\r\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\r\n\r\nexport default function ProfilePage() {\r\n  return (\r\n    <ProtectedRoute>\r\n      <Profile />\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,uOAAiC,8BAA8B;AAC/D;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,oIAAA,CAAA,UAAO;;;;;;;;;;AAGd;KANwB", "debugId": null}}]}