{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function createClient() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            );\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/actions/update-profile.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { User } from '@/contexts/AuthContext';\r\nimport { revalidatePath } from 'next/cache';\r\n\r\nexport async function updateProfile(formData: FormData) {\r\n  try {\r\n    const supabase = await createClient();\r\n\r\n    // Get the current authenticated user (more secure than getSession)\r\n    const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n    if (userError || !user) {\r\n      console.error('Authentication error:', userError);\r\n      return { error: 'Nicht authentifiziert' };\r\n    }\r\n\r\n    const userId = user.id;\r\n\r\n    // Extract profile data from form\r\n    const profileData: Partial<User> & { updated_at: string } = {\r\n      name: formData.get('name') as string,\r\n      company_name: formData.get('company_name') as string,\r\n      address: formData.get('address') as string,\r\n      phone: formData.get('phone') as string,\r\n      website: formData.get('website') as string,\r\n      employee_count: formData.get('employee_count') as string,\r\n      company_info_points: formData.get('company_info_points') as string,\r\n      updated_at: new Date().toISOString() // Add updated_at timestamp\r\n    };\r\n\r\n    console.log('Updating profile for user:', userId);\r\n    console.log('Profile data:', profileData);\r\n\r\n    // Update the profile in the database\r\n    const { error, data } = await supabase\r\n      .from('profiles')\r\n      .update(profileData)\r\n      .eq('id', userId)\r\n      .select();\r\n\r\n    if (error) {\r\n      console.error('Error updating profile:', error);\r\n      return { error: error.message };\r\n    }\r\n\r\n    console.log('Profile updated successfully:', data);\r\n\r\n    // Revalidate the profile page to show updated data\r\n    revalidatePath('/profile');\r\n\r\n    // Force cache invalidation for the profile data\r\n    revalidatePath('/', 'layout');\r\n\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('Unexpected error in updateProfile:', error);\r\n    const message = error instanceof Error ? error.message : 'Unbekannter Fehler';\r\n    return { error: `Fehler beim Aktualisieren des Profils: ${message}` };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;;;AAEO,eAAe,cAAc,QAAkB;IACpD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,mEAAmE;QACnE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBAAE,OAAO;YAAwB;QAC1C;QAEA,MAAM,SAAS,KAAK,EAAE;QAEtB,iCAAiC;QACjC,MAAM,cAAsD;YAC1D,MAAM,SAAS,GAAG,CAAC;YACnB,cAAc,SAAS,GAAG,CAAC;YAC3B,SAAS,SAAS,GAAG,CAAC;YACtB,OAAO,SAAS,GAAG,CAAC;YACpB,SAAS,SAAS,GAAG,CAAC;YACtB,gBAAgB,SAAS,GAAG,CAAC;YAC7B,qBAAqB,SAAS,GAAG,CAAC;YAClC,YAAY,IAAI,OAAO,WAAW,GAAG,2BAA2B;QAClE;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,qCAAqC;QACrC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAChC;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,mDAAmD;QACnD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,gDAAgD;QAChD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAEpB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACzD,OAAO;YAAE,OAAO,CAAC,uCAAuC,EAAE,SAAS;QAAC;IACtE;AACF;;;IAvDsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/.next-internal/server/app/profile/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {updateProfile as '402fc347d9dd2b1b15867c0f1c996e02fcd1c8aea1'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/profile/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/profile/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/profile/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/profile/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}