{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/client.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createBrowserClient } from '@supabase/ssr';\r\n\r\nexport function createClient() {\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAMI;AAJJ;AAAA;AAFA;;AAIO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'; // Provider needs to be a client component to use hooks\r\n\r\nimport React, { createContext, useState, useEffect, useContext, ReactNode, useMemo } from 'react';\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { Session, User as SupabaseUser } from '@supabase/supabase-js';\r\n\r\n// Define the User type based on fields used in Profile.tsx\r\n// Ensure this matches the actual user data structure from your API\r\nexport interface User {\r\n  id?: string;\r\n  company_name?: string | null;\r\n  name?: string | null;\r\n  email?: string | null;\r\n  address?: string | null;\r\n  phone?: string | null;\r\n  website?: string | null;\r\n  employee_count?: number | string | null;\r\n  company_info_points?: string | null;\r\n  // Zusätzliche Felder für Profil-Statistiken und Darstellung\r\n  analysisCount?: number;\r\n  activityCount?: number;\r\n  createdAt?: string;\r\n  avatarUrl?: string;\r\n}\r\n\r\n// Define the shape of the context value\r\nexport interface AuthContextType { // Export the interface\r\n  user: User | null;\r\n  supabaseUser: SupabaseUser | null;\r\n  session: Session | null;\r\n  isLoading: boolean;\r\n  initialAuthDone: boolean;\r\n  isAuthReadyForDataFetch: boolean; // Add new state for data fetching readiness\r\n  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;\r\n  signUp: (email: string, password: string) => Promise<{ error: Error | null }>;\r\n  signOut: () => Promise<void>;\r\n  signInWithGoogle: () => Promise<{ error: Error | null }>;\r\n  resetPassword: (email: string) => Promise<{ error: Error | null }>;\r\n  updatePassword: (newPassword: string) => Promise<{ error: Error | null }>;\r\n  refreshSession: () => Promise<void>;\r\n  refreshUserProfile: () => Promise<void>;\r\n}\r\n\r\n// Create the context with a default value (can be null or a default object)\r\n// Using null requires careful checks in consumers, but is often cleaner.\r\nexport const AuthContext = createContext<AuthContextType | null>(null);\r\n\r\n// Define props for the provider\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);\r\n  const [session, setSession] = useState<Session | null>(null);\r\n  const [isLoading, setIsLoading] = useState<boolean>(true);\r\n  const [initialAuthDone, setInitialAuthDone] = useState<boolean>(false);\r\n  const [isPasswordResetFlow, setIsPasswordResetFlow] = useState<boolean>(false);\r\n\r\n  const supabase = useMemo(() => createClient(), []);\r\n\r\n  // Derived state to indicate if auth is ready for data fetching\r\n  const isAuthReadyForDataFetch = !isLoading && initialAuthDone && !!user;\r\n\r\n  // Handle auth state changes\r\n  useEffect(() => {\r\n    console.log(\"AuthContext: useEffect setting up auth listener. Initial state: isLoading=true, initialAuthDone=false\");\r\n    setIsLoading(true);\r\n    setInitialAuthDone(false);\r\n\r\n    const { data: authListener } = supabase.auth.onAuthStateChange(\r\n      (event, currentSession) => {\r\n        console.log(`AuthContext: onAuthStateChange triggered - event: ${event}`, \"session:\", currentSession ? 'present' : 'null');\r\n\r\n        // Handle password recovery flow\r\n        if (event === 'PASSWORD_RECOVERY') {\r\n          console.log('AuthContext: Password recovery flow detected');\r\n          setIsPasswordResetFlow(true);\r\n          return;\r\n        }\r\n\r\n        // If we're in password reset flow, don't update the user state\r\n        if (isPasswordResetFlow && (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED')) {\r\n          console.log('AuthContext: Suppressing auth state update during password reset flow');\r\n          return;\r\n        }\r\n\r\n        const currentSupabaseAuthUser = currentSession?.user || null;\r\n        setSupabaseUser(currentSupabaseAuthUser);\r\n        setSession(currentSession);\r\n\r\n        if (currentSupabaseAuthUser) {\r\n          // Set basic user information as soon as session is known\r\n          setUser({ id: currentSupabaseAuthUser.id, email: currentSupabaseAuthUser.email });\r\n          // Mark initial authentication (session check) as done\r\n          setInitialAuthDone(true);\r\n          \r\n          // DEADLOCK FIX: Move async operations out of onAuthStateChange using setTimeout\r\n          setTimeout(async () => {\r\n            try {\r\n              console.log(\"AuthContext: Fetching profile for user ID:\", currentSupabaseAuthUser.id);\r\n              const { data: profileData, error: profileError } = await supabase\r\n                .from('profiles')\r\n                .select('*')\r\n                .eq('id', currentSupabaseAuthUser.id)\r\n                .single();\r\n\r\n              if (profileError && profileError.code !== 'PGRST116') {\r\n                console.error(\"AuthContext: Error fetching user profile:\", profileError);\r\n                // User remains with basic info set above\r\n              } else if (profileData) {\r\n                console.log(\"AuthContext: Profile found:\", profileData);\r\n                const fullUser: User = {\r\n                  id: currentSupabaseAuthUser.id,\r\n                  email: currentSupabaseAuthUser.email,\r\n                  name: profileData.name,\r\n                  company_name: profileData.company_name,\r\n                  address: profileData.address,\r\n                  phone: profileData.phone,\r\n                  website: profileData.website,\r\n                  employee_count: profileData.employee_count,\r\n                  company_info_points: profileData.company_info_points,\r\n                  createdAt: profileData.created_at,\r\n                  avatarUrl: profileData.avatar_url\r\n                };\r\n                setUser(fullUser); // Update user with full profile data\r\n              } else {\r\n                console.log(\"AuthContext: No profile data found. User remains with basic info.\");\r\n                // User remains with basic info set above\r\n              }\r\n            } catch (profileCatchError) {\r\n              console.error(\"AuthContext: Error fetching profile:\", profileCatchError);\r\n              // User remains with basic info set above\r\n            }\r\n          }, 0);\r\n          \r\n        } else {\r\n          // No Supabase auth user in session - handle session refresh with setTimeout\r\n          console.log(\"AuthContext: No user in session, attempting session refresh...\");\r\n          \r\n          setTimeout(async () => {\r\n            try {\r\n              const { data: { session: refreshedSession } } = await supabase.auth.getSession();\r\n              if (refreshedSession?.user) {\r\n                console.log(\"AuthContext: Session refresh found user:\", refreshedSession.user.id);\r\n                // Don't recursively call, just update state directly\r\n                setSupabaseUser(refreshedSession.user);\r\n                setSession(refreshedSession);\r\n                setUser({ id: refreshedSession.user.id, email: refreshedSession.user.email });\r\n              } else {\r\n                console.log(\"AuthContext: Session refresh found no user\");\r\n                setUser(null);\r\n              }\r\n            } catch (refreshError) {\r\n              console.error(\"AuthContext: Error during session refresh:\", refreshError);\r\n              setUser(null);\r\n            }\r\n          }, 0);\r\n          \r\n          setInitialAuthDone(true); // Mark initial auth as done regardless\r\n        }\r\n\r\n        // Regardless of profile fetch outcome, initial loading (session check) is now complete\r\n        setIsLoading(false);\r\n        // Log final states for this event processing cycle\r\n        console.log(`AuthContext: Finished onAuthStateChange processing. Event: ${event}, User: ${!!currentSupabaseAuthUser}, InitialAuthDone: ${true}, IsLoading: ${false}`);\r\n      }\r\n    );\r\n\r\n    return () => {\r\n      console.log(\"AuthContext: Unsubscribing from onAuthStateChange.\");\r\n      authListener.subscription.unsubscribe();\r\n    };\r\n  }, [supabase, isPasswordResetFlow]); // Include isPasswordResetFlow since it's used in the callback\r\n\r\n  // Add logging for state changes\r\n  useEffect(() => {\r\n    console.log(\"AuthContext State Updated:\", { user, supabaseUser, session, isLoading, initialAuthDone });\r\n  }, [user, supabaseUser, session, isLoading, initialAuthDone]);\r\n\r\n  const signIn = async (email: string, password: string) => {\r\n    console.log(\"AuthContext: signIn called. Setting isLoading=true\");\r\n    setIsLoading(true); // Ensure loading is true before async operation\r\n    const { error } = await supabase.auth.signInWithPassword({ email, password });\r\n    // onAuthStateChange will handle setting isLoading to false and updating user\r\n    if (error) {\r\n      console.error(\"Sign-in error:\", error);\r\n      setIsLoading(false); // Explicitly set false if error and onAuthStateChange might not fire as expected\r\n    }\r\n    return { error };\r\n  };\r\n\r\n  const signUp = async (email: string, password: string) => {\r\n    console.log(\"AuthContext: signUp called. Setting isLoading=true\");\r\n    setIsLoading(true); // Ensure loading is true\r\n    const { error } = await supabase.auth.signUp({\r\n      email,\r\n      password,\r\n      options: {\r\n        // emailRedirectTo: `${window.location.origin}/auth/callback`,\r\n      },\r\n    });\r\n    // onAuthStateChange will handle setting isLoading to false\r\n    if (error) {\r\n      console.error(\"Sign-up error:\", error);\r\n      setIsLoading(false); // Explicitly set false if error\r\n    }\r\n    return { error };\r\n  };\r\n\r\n  const signOut = async () => {\r\n    console.log(\"AuthContext: signOut called. Resetting auth state\");\r\n    // Skip if we're in password reset flow\r\n    if (isPasswordResetFlow) {\r\n      console.log('AuthContext: Skipping sign out during password reset flow');\r\n      return;\r\n    }\r\n    \r\n    // First clear the current state\r\n    setUser(null);\r\n    setSupabaseUser(null);\r\n    setSession(null);\r\n    setInitialAuthDone(false);\r\n    setIsLoading(true);\r\n    setIsPasswordResetFlow(false);\r\n\r\n    try {\r\n      // Then sign out from Supabase\r\n      await supabase.auth.signOut();\r\n\r\n      // Clear any local storage items\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.removeItem('opumap_map_state');\r\n        // Clear any auth-related items from localStorage\r\n        Object.keys(localStorage).forEach(key => {\r\n          if (key.startsWith('sb-')) {\r\n            localStorage.removeItem(key);\r\n          }\r\n        });\r\n      }\r\n\r\n      // Reset initial auth state after a short delay to ensure cleanup\r\n      setTimeout(() => {\r\n        setInitialAuthDone(true);\r\n        setIsLoading(false);\r\n      }, 100);\r\n\r\n    } catch (error) {\r\n      console.error('Error during sign out:', error);\r\n      setInitialAuthDone(true);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const signInWithGoogle = async () => {\r\n    console.log(\"AuthContext: signInWithGoogle called. Resetting auth state\");\r\n    // Reset state before starting the OAuth flow\r\n    setUser(null);\r\n    setSupabaseUser(null);\r\n    setSession(null);\r\n    setInitialAuthDone(false);\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const { error } = await supabase.auth.signInWithOAuth({\r\n        provider: 'google',\r\n        options: {\r\n          redirectTo: `${window.location.origin}/auth/callback`,\r\n          queryParams: {\r\n            // Force the consent screen to show every time\r\n            access_type: 'offline',\r\n            prompt: 'consent',\r\n          },\r\n        },\r\n      });\r\n\r\n      if (error) {\r\n        console.error(\"Google Sign-in error:\", error);\r\n        setInitialAuthDone(true);\r\n        setIsLoading(false);\r\n        return { error };\r\n      }\r\n\r\n      // The actual authentication will be handled by the onAuthStateChange listener\r\n      // which will be triggered after the OAuth flow completes\r\n      return { error: null };\r\n    } catch (error) {\r\n      console.error(\"Unexpected error during Google sign-in:\", error);\r\n      setInitialAuthDone(true);\r\n      setIsLoading(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const resetPassword = async (email: string) => {\r\n    console.log(\"AuthContext: resetPassword called for email:\", email);\r\n    try {\r\n      // Set password reset flow flag\r\n      setIsPasswordResetFlow(true);\r\n      \r\n      // Explicitly set the recovery flow type for better handling in the callback\r\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n        redirectTo: `${window.location.origin}/auth/callback?next=/reset-password&type=recovery`,\r\n      });\r\n\r\n      if (error) {\r\n        console.error(\"Password reset error in AuthContext:\", error);\r\n        setIsPasswordResetFlow(false);\r\n        throw error;\r\n      }\r\n\r\n      console.log(\"Password reset email sent successfully\");\r\n      return { error: null };\r\n    } catch (error) {\r\n      console.error(\"Password reset error:\", error);\r\n      setIsPasswordResetFlow(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const updatePassword = async (newPassword: string) => {\r\n    console.log(\"AuthContext: updatePassword called\");\r\n    try {\r\n      // Set password reset flow flag\r\n      setIsPasswordResetFlow(true);\r\n      \r\n      const { error } = await supabase.auth.updateUser({\r\n        password: newPassword,\r\n      });\r\n      \r\n      if (error) throw error;\r\n      \r\n      // Reset the password reset flow flag after successful update\r\n      setIsPasswordResetFlow(false);\r\n      return { error: null };\r\n      \r\n    } catch (error) {\r\n      console.error(\"Update password error:\", error);\r\n      setIsPasswordResetFlow(false);\r\n      return { error: error as Error };\r\n    }\r\n  };\r\n\r\n  const refreshSession = async () => {\r\n    // Skip refresh if we're in password reset flow\r\n    if (isPasswordResetFlow) {\r\n      console.log('AuthContext: Skipping session refresh during password reset flow');\r\n      return;\r\n    }\r\n    \r\n    console.log(\"AuthContext: refreshSession called. Setting isLoading=true\");\r\n    setIsLoading(true); // Indicate loading state\r\n\r\n    try {\r\n      const { data, error } = await supabase.auth.refreshSession();\r\n      // onAuthStateChange should ideally handle the session update and subsequent profile fetch.\r\n      // However, if the session hasn't actually changed (e.g., it was already valid and just got refreshed with the same details),\r\n      // onAuthStateChange might not fire. In such cases, we might need to manually trigger a profile update or ensure isLoading is reset.\r\n\r\n      if (error) {\r\n        console.error(\"AuthContext: Error refreshing session:\", error);\r\n        // Potentially handle specific errors, e.g., redirect to login if token is invalid\r\n        // setUser(null); // Or based on error type\r\n        // setSession(null);\r\n      } else {\r\n        console.log(\"AuthContext: Session refreshed successfully.\", data);\r\n        // If onAuthStateChange doesn't fire because the user/session ID remains the same,\r\n        // we might need to manually update the session state here or re-fetch profile if necessary.\r\n        // For now, relying on onAuthStateChange, but this is a point of attention.\r\n        // If session object itself is new, update it:\r\n        if (data.session) {\r\n          setSession(data.session);\r\n          if (data.user) {\r\n            setSupabaseUser(data.user); // Ensure supabaseUser is also updated\r\n            // If user data might have changed on the server, consider a profile refresh\r\n            // await refreshUserProfile(); // This could be an option if needed\r\n          }\r\n        }\r\n      }\r\n    } catch (e) {\r\n      console.error(\"AuthContext: Exception during refreshSession:\", e);\r\n      // setUser(null);\r\n      // setSession(null);\r\n    } finally {\r\n      // Fallback – ensure UI is not stuck\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const refreshUserProfile = async () => {\r\n    if (!supabaseUser) {\r\n      console.log(\"AuthContext: refreshUserProfile called but no supabaseUser.\");\r\n      return;\r\n    }\r\n    console.log(\"AuthContext: refreshUserProfile called for user ID:\", supabaseUser.id);\r\n    setIsLoading(true);\r\n    try {\r\n      const { data: profileData, error: profileError } = await supabase\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', supabaseUser.id)\r\n        .single();\r\n\r\n      if (profileError && profileError.code !== 'PGRST116') {\r\n        console.error(\"AuthContext: Error refreshing user profile:\", profileError);\r\n        // Potentially keep existing user data or set to basic\r\n      } else if (profileData) {\r\n        console.log(\"AuthContext: Profile refreshed successfully:\", profileData);\r\n        const fullUser: User = {\r\n          id: supabaseUser.id,\r\n          email: supabaseUser.email,\r\n          name: profileData.name,\r\n          company_name: profileData.company_name,\r\n          address: profileData.address,\r\n          phone: profileData.phone,\r\n          website: profileData.website,\r\n          employee_count: profileData.employee_count,\r\n          company_info_points: profileData.company_info_points,\r\n          createdAt: profileData.created_at,\r\n          avatarUrl: profileData.avatar_url\r\n        };\r\n        setUser(fullUser);\r\n      } else {\r\n        console.log(\"AuthContext: No profile data found on refresh. User might not have a profile yet.\");\r\n        // Keep existing user data or set to basic if appropriate\r\n        const basicUser = { id: supabaseUser.id, email: supabaseUser.email };\r\n        setUser(basicUser);\r\n      }\r\n    } catch (e) {\r\n      console.error(\"AuthContext: Exception during refreshUserProfile:\", e);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <AuthContext.Provider value={{\r\n      user,\r\n      supabaseUser,\r\n      session,\r\n      isLoading,\r\n      initialAuthDone,\r\n      isAuthReadyForDataFetch,\r\n      signIn,\r\n      signUp,\r\n      signOut,\r\n      signInWithGoogle,\r\n      resetPassword,\r\n      updatePassword,\r\n      refreshSession,\r\n      refreshUserProfile\r\n    }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\n// Custom hook to use the auth context\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === null) { // Check for null specifically\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA,cAAc,uDAAuD;;;AA6C9D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA0B;AAO1D,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE,IAAM,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;yCAAK,EAAE;IAEjD,+DAA+D;IAC/D,MAAM,0BAA0B,CAAC,aAAa,mBAAmB,CAAC,CAAC;IAEnE,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,mBAAmB;YAEnB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAC5D,CAAC,OAAO;oBACN,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,OAAO,EAAE,YAAY,iBAAiB,YAAY;oBAEnH,gCAAgC;oBAChC,IAAI,UAAU,qBAAqB;wBACjC,QAAQ,GAAG,CAAC;wBACZ,uBAAuB;wBACvB;oBACF;oBAEA,+DAA+D;oBAC/D,IAAI,uBAAuB,CAAC,UAAU,gBAAgB,UAAU,iBAAiB,GAAG;wBAClF,QAAQ,GAAG,CAAC;wBACZ;oBACF;oBAEA,MAAM,0BAA0B,gBAAgB,QAAQ;oBACxD,gBAAgB;oBAChB,WAAW;oBAEX,IAAI,yBAAyB;wBAC3B,yDAAyD;wBACzD,QAAQ;4BAAE,IAAI,wBAAwB,EAAE;4BAAE,OAAO,wBAAwB,KAAK;wBAAC;wBAC/E,sDAAsD;wBACtD,mBAAmB;wBAEnB,gFAAgF;wBAChF;sDAAW;gCACT,IAAI;oCACF,QAAQ,GAAG,CAAC,8CAA8C,wBAAwB,EAAE;oCACpF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,wBAAwB,EAAE,EACnC,MAAM;oCAET,IAAI,gBAAgB,aAAa,IAAI,KAAK,YAAY;wCACpD,QAAQ,KAAK,CAAC,6CAA6C;oCAC3D,yCAAyC;oCAC3C,OAAO,IAAI,aAAa;wCACtB,QAAQ,GAAG,CAAC,+BAA+B;wCAC3C,MAAM,WAAiB;4CACrB,IAAI,wBAAwB,EAAE;4CAC9B,OAAO,wBAAwB,KAAK;4CACpC,MAAM,YAAY,IAAI;4CACtB,cAAc,YAAY,YAAY;4CACtC,SAAS,YAAY,OAAO;4CAC5B,OAAO,YAAY,KAAK;4CACxB,SAAS,YAAY,OAAO;4CAC5B,gBAAgB,YAAY,cAAc;4CAC1C,qBAAqB,YAAY,mBAAmB;4CACpD,WAAW,YAAY,UAAU;4CACjC,WAAW,YAAY,UAAU;wCACnC;wCACA,QAAQ,WAAW,qCAAqC;oCAC1D,OAAO;wCACL,QAAQ,GAAG,CAAC;oCACZ,yCAAyC;oCAC3C;gCACF,EAAE,OAAO,mBAAmB;oCAC1B,QAAQ,KAAK,CAAC,wCAAwC;gCACtD,yCAAyC;gCAC3C;4BACF;qDAAG;oBAEL,OAAO;wBACL,4EAA4E;wBAC5E,QAAQ,GAAG,CAAC;wBAEZ;sDAAW;gCACT,IAAI;oCACF,MAAM,EAAE,MAAM,EAAE,SAAS,gBAAgB,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oCAC9E,IAAI,kBAAkB,MAAM;wCAC1B,QAAQ,GAAG,CAAC,4CAA4C,iBAAiB,IAAI,CAAC,EAAE;wCAChF,qDAAqD;wCACrD,gBAAgB,iBAAiB,IAAI;wCACrC,WAAW;wCACX,QAAQ;4CAAE,IAAI,iBAAiB,IAAI,CAAC,EAAE;4CAAE,OAAO,iBAAiB,IAAI,CAAC,KAAK;wCAAC;oCAC7E,OAAO;wCACL,QAAQ,GAAG,CAAC;wCACZ,QAAQ;oCACV;gCACF,EAAE,OAAO,cAAc;oCACrB,QAAQ,KAAK,CAAC,8CAA8C;oCAC5D,QAAQ;gCACV;4BACF;qDAAG;wBAEH,mBAAmB,OAAO,uCAAuC;oBACnE;oBAEA,uFAAuF;oBACvF,aAAa;oBACb,mDAAmD;oBACnD,QAAQ,GAAG,CAAC,CAAC,2DAA2D,EAAE,MAAM,QAAQ,EAAE,CAAC,CAAC,wBAAwB,mBAAmB,EAAE,KAAK,aAAa,EAAE,OAAO;gBACtK;;YAGF;0CAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa,YAAY,CAAC,WAAW;gBACvC;;QACF;iCAAG;QAAC;QAAU;KAAoB,GAAG,8DAA8D;IAEnG,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,QAAQ,GAAG,CAAC,8BAA8B;gBAAE;gBAAM;gBAAc;gBAAS;gBAAW;YAAgB;QACtG;iCAAG;QAAC;QAAM;QAAc;QAAS;QAAW;KAAgB;IAE5D,MAAM,SAAS,OAAO,OAAe;QACnC,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,gDAAgD;QACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAAE;YAAO;QAAS;QAC3E,6EAA6E;QAC7E,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kBAAkB;YAChC,aAAa,QAAQ,iFAAiF;QACxG;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,yBAAyB;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;YAET;QACF;QACA,2DAA2D;QAC3D,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,kBAAkB;YAChC,aAAa,QAAQ,gCAAgC;QACvD;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,QAAQ,GAAG,CAAC;QACZ,uCAAuC;QACvC,IAAI,qBAAqB;YACvB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,gCAAgC;QAChC,QAAQ;QACR,gBAAgB;QAChB,WAAW;QACX,mBAAmB;QACnB,aAAa;QACb,uBAAuB;QAEvB,IAAI;YACF,8BAA8B;YAC9B,MAAM,SAAS,IAAI,CAAC,OAAO;YAE3B,gCAAgC;YAChC,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,iDAAiD;gBACjD,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;oBAChC,IAAI,IAAI,UAAU,CAAC,QAAQ;wBACzB,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF;YAEA,iEAAiE;YACjE,WAAW;gBACT,mBAAmB;gBACnB,aAAa;YACf,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mBAAmB;YACnB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,6CAA6C;QAC7C,QAAQ;QACR,gBAAgB;QAChB,WAAW;QACX,mBAAmB;QACnB,aAAa;QAEb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;oBACrD,aAAa;wBACX,8CAA8C;wBAC9C,aAAa;wBACb,QAAQ;oBACV;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,mBAAmB;gBACnB,aAAa;gBACb,OAAO;oBAAE;gBAAM;YACjB;YAEA,8EAA8E;YAC9E,yDAAyD;YACzD,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,mBAAmB;YACnB,aAAa;YACb,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,QAAQ,GAAG,CAAC,gDAAgD;QAC5D,IAAI;YACF,+BAA+B;YAC/B,uBAAuB;YAEvB,4EAA4E;YAC5E,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,iDAAiD,CAAC;YAC1F;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,uBAAuB;gBACvB,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,QAAQ,GAAG,CAAC;QACZ,IAAI;YACF,+BAA+B;YAC/B,uBAAuB;YAEvB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;gBAC/C,UAAU;YACZ;YAEA,IAAI,OAAO,MAAM;YAEjB,6DAA6D;YAC7D,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAK;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uBAAuB;YACvB,OAAO;gBAAE,OAAO;YAAe;QACjC;IACF;IAEA,MAAM,iBAAiB;QACrB,+CAA+C;QAC/C,IAAI,qBAAqB;YACvB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,yBAAyB;QAE7C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,cAAc;YAC1D,2FAA2F;YAC3F,6HAA6H;YAC7H,oIAAoI;YAEpI,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0CAA0C;YACxD,kFAAkF;YAClF,2CAA2C;YAC3C,oBAAoB;YACtB,OAAO;gBACL,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,kFAAkF;gBAClF,4FAA4F;gBAC5F,2EAA2E;gBAC3E,8CAA8C;gBAC9C,IAAI,KAAK,OAAO,EAAE;oBAChB,WAAW,KAAK,OAAO;oBACvB,IAAI,KAAK,IAAI,EAAE;wBACb,gBAAgB,KAAK,IAAI,GAAG,sCAAsC;oBAClE,4EAA4E;oBAC5E,mEAAmE;oBACrE;gBACF;YACF;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,iBAAiB;QACjB,oBAAoB;QACtB,SAAU;YACR,oCAAoC;YACpC,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,QAAQ,GAAG,CAAC,uDAAuD,aAAa,EAAE;QAClF,aAAa;QACb,IAAI;YACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;YAET,IAAI,gBAAgB,aAAa,IAAI,KAAK,YAAY;gBACpD,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,sDAAsD;YACxD,OAAO,IAAI,aAAa;gBACtB,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,MAAM,WAAiB;oBACrB,IAAI,aAAa,EAAE;oBACnB,OAAO,aAAa,KAAK;oBACzB,MAAM,YAAY,IAAI;oBACtB,cAAc,YAAY,YAAY;oBACtC,SAAS,YAAY,OAAO;oBAC5B,OAAO,YAAY,KAAK;oBACxB,SAAS,YAAY,OAAO;oBAC5B,gBAAgB,YAAY,cAAc;oBAC1C,qBAAqB,YAAY,mBAAmB;oBACpD,WAAW,YAAY,UAAU;oBACjC,WAAW,YAAY,UAAU;gBACnC;gBACA,QAAQ;YACV,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,yDAAyD;gBACzD,MAAM,YAAY;oBAAE,IAAI,aAAa,EAAE;oBAAE,OAAO,aAAa,KAAK;gBAAC;gBACnE,QAAQ;YACV;QACF,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,qDAAqD;QACrE,SAAU;YACR,aAAa;QACf;IACF;IAGA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GArZa;KAAA;AAwZN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,MAAM;QACpB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState, useEffect } from 'react'\r\n\r\n/**\r\n * Komponente, die ihre Kinder nur clientseitig rendert\r\n * Dies verhindert Hydration-Fehler bei SVGs und anderen komplexen Komponenten\r\n */\r\ntype ClientOnlyProps = {\r\n  children: React.ReactNode\r\n}\r\n\r\nexport const ClientOnly = ({ children }: ClientOnlyProps) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return <>{children}</>\r\n}\r\n\r\n/**\r\n * SVG-spezifische Variante der ClientOnly Komponente \r\n * Leitet alle SVG-Props direkt weiter\r\n */\r\nexport const ClientOnlySVG = (props: React.SVGProps<SVGSVGElement>) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  // Nichts rendern, bis wir auf dem Client sind\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return <svg {...props} />\r\n}\r\n\r\n/**\r\n * Icon-spezifische Variante der ClientOnly Komponente\r\n * Leitet alle div-Props direkt weiter und wickelt Icons ein\r\n */\r\nexport const ClientOnlyIcon = (props: React.HTMLAttributes<HTMLDivElement> & { \r\n  children: React.ReactNode,\r\n  width?: string,\r\n  height?: string \r\n}) => {\r\n  const [hasMounted, setHasMounted] = useState(false)\r\n  const { children, className, width, height, ...rest } = props\r\n\r\n  useEffect(() => {\r\n    setHasMounted(true)\r\n  }, [])\r\n\r\n  // Nichts rendern, bis wir auf dem Client sind\r\n  if (!hasMounted) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div \r\n      className={className} \r\n      style={{ \r\n        display: 'inline-flex', \r\n        width: width || 'auto', \r\n        height: height || 'auto' \r\n      }} \r\n      {...rest}\r\n    >\r\n      {children}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAYO,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GAZa;KAAA;AAkBN,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,cAAc;QAChB;kCAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBAAO,6LAAC;QAAK,GAAG,KAAK;;;;;;AACvB;IAba;MAAA;AAmBN,MAAM,iBAAiB,CAAC;;IAK7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,cAAc;QAChB;mCAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,OAAO;YACL,SAAS;YACT,OAAO,SAAS;YAChB,QAAQ,UAAU;QACpB;QACC,GAAG,IAAI;kBAEP;;;;;;AAGP;IA9Ba;MAAA", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/DarkModeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Sun, Moon } from \"lucide-react\";\r\nimport { ClientOnlyIcon } from \"@/components/ui/client-only\";\r\n\r\nexport default function DarkModeToggle() {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) return null;\r\n\r\n  return (\r\n    <button\r\n      onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\r\n      className=\"p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-black dark:text-white flex items-center justify-center w-10 h-10\"\r\n      title=\"Toggle Dark Mode\"\r\n    >\r\n      <ClientOnlyIcon width=\"20px\" height=\"20px\">\r\n        {theme === \"light\" ? <Sun className=\"h-5 w-5\" /> : <Moon className=\"h-5 w-5\" />}\r\n      </ClientOnlyIcon>\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;QACV,OAAM;kBAEN,cAAA,6LAAC,6IAAA,CAAA,iBAAc;YAAC,OAAM;YAAO,QAAO;sBACjC,UAAU,wBAAU,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;qCAAe,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI3E;GArBwB;;QACM,mJAAA,CAAA,WAAQ;;;KADd", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/button.tsx"], "sourcesContent": ["'use client';\nimport * as React from 'react';\n\n/**\n * Flexible Button-Komponente mit Tailwind-basierten Stilen.\n * Unterstützt verschiedene Varianten, Größen und kann als Child-Element gerendert werden.\n * \n * @example\n * // Standard-Button\n * <Button onClick={handleClick}>Aktion</Button>\n * \n * // Outline-Variante\n * <Button variant=\"outline\" size=\"lg\">\n *   Großer Button\n * </Button>\n * \n * @param variant - Visuelle Variante ('default' | 'outline' | 'ghost' | 'link')\n * @param size - Größe des Buttons ('icon' | 'sm' | 'md' | 'lg')\n * @param asChild - Wenn true, wird das Children-Element direkt gerendert (für Komposition)\n * @param className - Zusätzliche CSS-Klassen\n * @param children - Button-Inhalt (Text/Icon)\n * @param ...props - Standard HTML Button-Attribute\n */\n\nexport type ButtonVariant = 'default' | 'outline' | 'ghost' | 'link';\nexport type ButtonSize = 'icon' | 'sm' | 'md' | 'lg';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: ButtonVariant;\n  size?: ButtonSize;\n  asChild?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n}\n\nexport const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({\n  variant = 'default',\n  size = 'md',\n  asChild = false,\n  className = '',\n  children,\n  ...props\n}, ref) => {\n  const Component: React.ElementType = asChild ? 'span' : 'button';\n  \n  const baseClasses = \"inline-flex items-center justify-center rounded cursor-pointer transition-colors\";\n  const variantClasses = variant === 'outline' \n    ? \"border border-[var(--color-border)] text-[var(--color-foreground)] hover:bg-[var(--color-accent)] hover:text-[var(--color-accent-foreground)]\" \n    : \"bg-[var(--color-primary)] text-[var(--color-primary-foreground)] hover:brightness-90\";\n  const sizeClasses = size === 'icon' ? \"p-2\" : \"px-4 py-2\";\n  \n  return (\n    <Component\n      ref={ref}\n      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}\n      {...props}\n    >\n      {children}\n    </Component>\n  );\n});\n\nButton.displayName = 'Button';\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAmCO,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAkC,CAAC,EACtE,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,YAA+B,UAAU,SAAS;IAExD,MAAM,cAAc;IACpB,MAAM,iBAAiB,YAAY,YAC/B,kJACA;IACJ,MAAM,cAAc,SAAS,SAAS,QAAQ;IAE9C,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,GAAG,YAAY,CAAC,EAAE,eAAe,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW;QACxE,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"./button\";\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close asChild>\r\n          <Button variant=\"outline\" size=\"icon\" className=\"absolute top-6 right-6\">\r\n            <XIcon className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">Close</span>\r\n          </Button>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,OAAO;kCAC3B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,WAAU;;8CAC9C,6LAAC,mMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MArCS;AAuCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { createClient } from '@/utils/supabase/client';\r\n\r\n// Globale Variable, um zu verfolgen, ob der Benutzer sich absichtlich ausgeloggt hat\r\nlet intentionalLogout = false;\r\n\r\n// Funktion, die von der Logout-Komponente aufgerufen werden kann\r\nexport const setIntentionalLogout = () => {\r\n  intentionalLogout = true;\r\n  // Nach 2 Sekunden zurücksetzen, um sicherzustellen, dass die Variable nicht dauerhaft gesetzt bleibt\r\n  setTimeout(() => {\r\n    intentionalLogout = false;\r\n  }, 2000);\r\n};\r\n\r\nconst SessionExpiredModal: React.FC = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const router = useRouter();\r\n  const { signOut } = useAuth();\r\n  const supabase = createClient();\r\n\r\n  useEffect(() => {\r\n    // Listen for the custom session-expired event\r\n    const handleSessionExpired = () => {\r\n      setIsVisible(true);\r\n    };\r\n\r\n    // Listen for Supabase auth state changes\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {\r\n      if (event === 'TOKEN_REFRESHED') {\r\n        // Token was refreshed, no need to show the modal\r\n        setIsVisible(false);\r\n      } else if (event === 'SIGNED_OUT') {\r\n        // Nur das Modal anzeigen, wenn es kein absichtliches Ausloggen war\r\n        if (!intentionalLogout) {\r\n          setIsVisible(true);\r\n        }\r\n      }\r\n    });\r\n\r\n    window.addEventListener('session-expired', handleSessionExpired);\r\n\r\n    // Clean up the event listener and subscription\r\n    return () => {\r\n      window.removeEventListener('session-expired', handleSessionExpired);\r\n      subscription.unsubscribe();\r\n    };\r\n  }, [supabase]);\r\n\r\n  const handleLogin = async () => {\r\n    setIsVisible(false);\r\n    // Sign out the user to clear any stale session data\r\n    await signOut();\r\n    router.push('/login');\r\n  };\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-card rounded-lg shadow-xl max-w-md w-full overflow-hidden flex flex-col\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <svg\r\n              className=\"w-8 h-8 text-amber-500 mr-3\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth=\"2\"\r\n                d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\r\n              />\r\n            </svg>\r\n            <h2 className=\"text-xl font-semibold text-card-foreground\">\r\n              Sitzung abgelaufen\r\n            </h2>\r\n          </div>\r\n          <p className=\"text-muted-foreground mb-6\">\r\n            Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an, um fortzufahren.\r\n          </p>\r\n          <div className=\"flex justify-end\">\r\n            <button\r\n              onClick={handleLogin}\r\n              className=\"bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-5 rounded-lg transition duration-150 ease-in-out\"\r\n            >\r\n              Zum Login\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SessionExpiredModal;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,qFAAqF;AACrF,IAAI,oBAAoB;AAGjB,MAAM,uBAAuB;IAClC,oBAAoB;IACpB,qGAAqG;IACrG,WAAW;QACT,oBAAoB;IACtB,GAAG;AACL;AAEA,MAAM,sBAAgC;;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,8CAA8C;YAC9C,MAAM;sEAAuB;oBAC3B,aAAa;gBACf;;YAEA,yCAAyC;YACzC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;iDAAC,CAAC;oBAClE,IAAI,UAAU,mBAAmB;wBAC/B,iDAAiD;wBACjD,aAAa;oBACf,OAAO,IAAI,UAAU,cAAc;wBACjC,mEAAmE;wBACnE,IAAI,CAAC,mBAAmB;4BACtB,aAAa;wBACf;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,mBAAmB;YAE3C,+CAA+C;YAC/C;iDAAO;oBACL,OAAO,mBAAmB,CAAC,mBAAmB;oBAC9C,aAAa,WAAW;gBAC1B;;QACF;wCAAG;QAAC;KAAS;IAEb,MAAM,cAAc;QAClB,aAAa;QACb,oDAAoD;QACpD,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,OAAM;0CAEN,cAAA,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAY;oCACZ,GAAE;;;;;;;;;;;0CAGN,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;;;;;;;kCAI7D,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjFM;;QAEW,qIAAA,CAAA,YAAS;QACJ,kIAAA,CAAA,UAAO;;;KAHvB;uCAmFS", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useRouter } from 'next/navigation';\r\nimport { setIntentionalLogout } from '@/components/SessionExpiredModal';\r\nimport React from 'react';\r\n\r\ninterface LogoutButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  variant?: 'default' | 'outline';\r\n  size?: 'sm' | 'lg' | 'icon' | 'md';\r\n  className?: string;\r\n  asChild?: boolean;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'default',\r\n  size = 'md',\r\n  className = '',\r\n  children,\r\n  ...props\r\n}: LogoutButtonProps) {\r\n  const { signOut } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleLogout = async () => {\r\n    // Markieren, dass dies ein absichtliches Ausloggen ist\r\n    setIntentionalLogout();\r\n    await signOut();\r\n    router.push('/');\r\n  };\r\n\r\n  return (\r\n    <Button\r\n      variant={variant}\r\n      size={size}\r\n      onClick={handleLogout}\r\n      className={`bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white ${className || ''}`}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACR,GAAG,OACe;;IAClB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,uDAAuD;QACvD,CAAA,GAAA,4IAAA,CAAA,uBAAoB,AAAD;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS;QACT,WAAW,CAAC,iFAAiF,EAAE,aAAa,IAAI;QAC/G,GAAG,KAAK;kBAER;;;;;;AAGP;GA5BwB;;QAOF,kIAAA,CAAA,UAAO;QACZ,qIAAA,CAAA,YAAS;;;KARF", "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/AuthStatus.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { setIntentionalLogout } from '@/components/SessionExpiredModal';\r\nimport LogoutButton from '@/components/auth/LogoutButton';\r\nimport { Button } from '@/components/ui/button'; // Import Button component\r\n\r\nexport default function AuthStatus() {\r\n  const { user, signOut, isLoading } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleSignOut = async () => {\r\n    // Markieren, dass dies ein absichtliches Ausloggen ist\r\n    setIntentionalLogout();\r\n    await signOut();\r\n    router.push('/');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return <div className=\"text-sm text-[var(--color-muted-foreground)]\">Laden...</div>;\r\n  }\r\n\r\n  if (!user) {\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button asChild size=\"md\" className=\"bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white text-sm\">\r\n          <Link\r\n            href=\"/login\"\r\n          >\r\n            Anmelden\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-4\">\r\n      <div className=\"text-sm text-[var(--color-muted-foreground)]\">\r\n        Angemeldet als <span className=\"font-medium text-[var(--color-foreground)]\">{user.name || user.email}</span>\r\n      </div>\r\n      <LogoutButton\r\n        onClick={handleSignOut}\r\n        className=\"text-sm transition-colors\"\r\n      >\r\n        Abmelden\r\n      </LogoutButton>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA,yOAAiD,0BAA0B;;;AAP3E;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,uDAAuD;QACvD,CAAA,GAAA,4IAAA,CAAA,uBAAoB,AAAD;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,qBAAO,6LAAC;YAAI,WAAU;sBAA+C;;;;;;IACvE;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAC,MAAK;gBAAK,WAAU;0BAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;8BACN;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAA+C;kCAC7C,6LAAC;wBAAK,WAAU;kCAA8C,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;;;;;;;;0BAEtG,6LAAC,6IAAA,CAAA,UAAY;gBACX,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP;GA1CwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1260, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyImage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Image, { ImageProps } from 'next/image';\r\n\r\n/**\r\n * ClientOnlyImage component ensures images are only rendered on the client\r\n * This prevents hydration errors caused by browser extensions that modify images\r\n */\r\nexport default function ClientOnlyImage(props: ImageProps) {\r\n  const [hasMounted, setHasMounted] = useState(false);\r\n  \r\n  useEffect(() => {\r\n    setHasMounted(true);\r\n  }, []);\r\n\r\n  // Before client-side hydration, render a placeholder div with the same dimensions\r\n  if (!hasMounted) {\r\n    return (\r\n      <div \r\n        className={props.className}\r\n        style={{ \r\n          width: props.width ? \r\n            typeof props.width === 'number' ? `${props.width}px` : props.width \r\n            : 'auto',\r\n          height: props.height ? \r\n            typeof props.height === 'number' ? `${props.height}px` : props.height \r\n            : 'auto',\r\n          display: 'inline-block',\r\n        }} \r\n      />\r\n    );\r\n  }\r\n\r\n  // Ensure the image always has an alt prop\r\n  return <Image {...props} alt={props.alt || \"\"} />;\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,gBAAgB,KAAiB;;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,cAAc;QAChB;oCAAG,EAAE;IAEL,kFAAkF;IAClF,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YACC,WAAW,MAAM,SAAS;YAC1B,OAAO;gBACL,OAAO,MAAM,KAAK,GAChB,OAAO,MAAM,KAAK,KAAK,WAAW,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK,GAChE;gBACJ,QAAQ,MAAM,MAAM,GAClB,OAAO,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM,GACnE;gBACJ,SAAS;YACX;;;;;;IAGN;IAEA,0CAA0C;IAC1C,qBAAO,6LAAC,gIAAA,CAAA,UAAK;QAAE,GAAG,KAAK;QAAE,KAAK,MAAM,GAAG,IAAI;;;;;;AAC7C;GA3BwB;KAAA", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport DarkModeToggle from '@/components/ui/DarkModeToggle';\r\nimport {\r\n  Sheet,\r\n  Sheet<PERSON>ontent,\r\n  She<PERSON><PERSON>eader,\r\n  SheetTitle,\r\n  SheetDescription,\r\n  SheetClose,\r\n} from './ui/sheet';\r\nimport { useTheme } from 'next-themes';\r\nimport { CheckCircle } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport AuthStatus from '@/components/auth/AuthStatus';\r\nimport { ClientOnlyIcon } from '@/components/ui/client-only';\r\nimport ClientOnlyImage from '@/components/ui/client-only/ClientOnlyImage';\r\n\r\n// Add custom animations\r\nconst mobileMenuStyles = `\r\n  @keyframes slideInFromRight {\r\n    from {\r\n      opacity: 0;\r\n      transform: translateX(30px);\r\n    }\r\n    to {\r\n      opacity: 1;\r\n      transform: translateX(0);\r\n    }\r\n  }\r\n  \r\n  @keyframes menuButtonSpin {\r\n    from {\r\n      transform: rotate(0deg);\r\n    }\r\n    to {\r\n      transform: rotate(180deg);\r\n    }\r\n  }\r\n  \r\n  .mobile-menu-animation {\r\n    animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;\r\n  }\r\n  \r\n  .menu-button-animation {\r\n    animation: menuButtonSpin 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  }\r\n`;\r\n\r\n// Inject styles\r\nif (typeof document !== 'undefined') {\r\n  const styleElement = document.createElement('style');\r\n  styleElement.textContent = mobileMenuStyles;\r\n  if (!document.head.querySelector('style[data-mobile-menu]')) {\r\n    styleElement.setAttribute('data-mobile-menu', 'true');\r\n    document.head.appendChild(styleElement);\r\n  }\r\n}\r\n\r\n// Custom NavLink component\r\nconst NavLink = ({ href, children }: { href: string; children: React.ReactNode }) => (\r\n  <div className=\"relative group\">\r\n    <Link\r\n      href={href}\r\n      className=\"relative px-4 py-2 font-medium text-foreground/80 hover:text-foreground transition-colors duration-200\"\r\n      prefetch={false}\r\n    >\r\n      {children}\r\n      <span className=\"absolute bottom-0 left-0 w-full h-0.5 bg-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left\" />\r\n    </Link>\r\n  </div>\r\n);\r\n\r\n// Mobile NavLink with enhanced animations and unique styling\r\nconst MobileNavLink = ({ \r\n  href, \r\n  children, \r\n  onClose, \r\n  index \r\n}: { \r\n  href: string; \r\n  children: React.ReactNode; \r\n  onClose: () => void;\r\n  index: number;\r\n}) => {\r\n  \r\n  return (\r\n    <div \r\n      className=\"w-full\"\r\n      style={{\r\n        animationDelay: `${index * 100}ms`,\r\n        animation: 'slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards'\r\n      }}\r\n    >\r\n      <SheetClose asChild>\r\n        <Link \r\n          href={href} \r\n          className=\"group relative flex w-full items-center h-14 text-lg font-medium rounded-2xl px-6 transition-all duration-300 overflow-hidden\"\r\n          prefetch={false}\r\n          onClick={onClose}\r\n        >\r\n          {/* Animated background gradient */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-500 ease-out\" />\r\n          \r\n          {/* Animated border */}\r\n          <div className=\"absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-primary/20 transition-all duration-300\" />\r\n          \r\n          {/* Content with icon animation */}\r\n          <div className=\"relative z-10 flex items-center space-x-4\">\r\n            <div className=\"w-2 h-2 rounded-full bg-primary/60 group-hover:bg-primary group-hover:scale-150 transition-all duration-300\" />\r\n            <span className=\"text-foreground/80 group-hover:text-foreground group-hover:translate-x-1 transition-all duration-300\">\r\n              {children}\r\n            </span>\r\n          </div>\r\n          \r\n          {/* Hover effect arrow */}\r\n          <div className=\"absolute right-6 opacity-0 group-hover:opacity-100 group-hover:translate-x-0 translate-x-2 transition-all duration-300\">\r\n            <div className=\"w-0 h-0 border-l-[6px] border-l-primary border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent\" />\r\n          </div>\r\n        </Link>\r\n      </SheetClose>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Navbar Component\r\nconst Navbar: React.FC = () => {\r\n  const { user, isLoading, initialAuthDone } = useAuth();\r\n  const isAuthenticated = !!user && !isLoading && initialAuthDone;\r\n\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsScrolled(window.scrollY > 10);\r\n    };\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n  const closeMenu = () => setIsMenuOpen(false);\r\n\r\n  return (\r\n    <header \r\n      className={cn(\r\n        'fixed top-0 left-0 right-0 z-50 flex h-20 items-center justify-between px-4 md:px-8 transition-all duration-300',\r\n        isScrolled \r\n          ? 'bg-background/80 backdrop-blur-md border-b border-border/50 shadow-sm' \r\n          : 'bg-transparent border-b border-transparent'\r\n      )}\r\n    >\r\n      <div className=\"container mx-auto flex items-center justify-between\">\r\n        {/* Logo Link */}\r\n        <div className=\"relative z-50 hover:scale-105 active:scale-95 transition-transform\">\r\n          <Link href=\"/\" className=\"flex items-center\" prefetch={false}>\r\n            <Logo className=\"h-16 w-auto\" />\r\n            <span className=\"sr-only\">Logo</span>\r\n          </Link>\r\n        </div>\r\n\r\n      {/* Desktop Navigation */}\r\n      <nav className=\"hidden lg:flex items-center space-x-1\">\r\n        <NavLink href=\"/\">Home</NavLink>\r\n        <NavLink href=\"/profile\">Profil</NavLink>\r\n        <NavLink href=\"/opuscanner\">Opuscanner</NavLink>\r\n        <NavLink href=\"/opulab\">Opulab</NavLink>\r\n        <div className=\"ml-4 flex items-center space-x-2\">\r\n          <AuthStatus />\r\n          <DarkModeToggle />\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Enhanced Mobile Menu Button */}\r\n      <button\r\n        onClick={toggleMenu}\r\n        className={cn(\r\n          \"lg:hidden relative z-50 p-3 rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary/30 active:scale-95 transition-all duration-300\",\r\n          \"bg-gradient-to-br from-background/80 to-background/60 backdrop-blur-sm border border-border/20\",\r\n          \"hover:bg-gradient-to-br hover:from-primary/10 hover:to-primary/5 hover:border-primary/30\",\r\n          isMenuOpen && \"bg-gradient-to-br from-primary/15 to-primary/10 border-primary/40\"\r\n        )}\r\n        aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}\r\n      >\r\n        <div className=\"relative w-6 h-6\">\r\n          {/* Animated hamburger/X icon */}\r\n          <div className={cn(\r\n            \"absolute top-1 left-0 w-6 h-0.5 bg-foreground transition-all duration-300 origin-center\",\r\n            isMenuOpen ? \"rotate-45 translate-y-2\" : \"rotate-0 translate-y-0\"\r\n          )} />\r\n          <div className={cn(\r\n            \"absolute top-3 left-0 w-6 h-0.5 bg-foreground transition-all duration-300\",\r\n            isMenuOpen ? \"opacity-0 scale-0\" : \"opacity-100 scale-100\"\r\n          )} />\r\n          <div className={cn(\r\n            \"absolute top-5 left-0 w-6 h-0.5 bg-foreground transition-all duration-300 origin-center\",\r\n            isMenuOpen ? \"-rotate-45 -translate-y-2\" : \"rotate-0 translate-y-0\"\r\n          )} />\r\n        </div>\r\n        \r\n        {/* Ripple effect */}\r\n        <div className={cn(\r\n          \"absolute inset-0 rounded-2xl bg-primary/20 scale-0 transition-transform duration-300\",\r\n          isMenuOpen && \"scale-100\"\r\n        )} />\r\n      </button>\r\n\r\n      {/* Enhanced Mobile Sheet Menu */}\r\n      <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\r\n        <SheetContent \r\n          side=\"left\" \r\n          className=\"w-[320px] sm:w-[380px] p-0 bg-gradient-to-br from-background/95 via-background/98 to-background backdrop-blur-xl border-r border-border/30\"\r\n        >\r\n          <span id=\"mobile-menu-description\" className=\"sr-only\">\r\n            Hauptnavigationsmenü\r\n          </span>\r\n          \r\n          {/* Enhanced Header with gradient background */}\r\n          <div className=\"relative p-6 border-b border-gradient-to-r from-transparent via-border/30 to-transparent\">\r\n            {/* Animated background pattern */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-50\" />\r\n            \r\n            <SheetHeader>\r\n              <SheetTitle className=\"sr-only\">Navigation Menu</SheetTitle>\r\n              <SheetDescription className=\"sr-only\">\r\n                Hauptnavigationsmenü mit Links zu allen wichtigen Bereichen der Anwendung\r\n              </SheetDescription>\r\n              <div className=\"relative z-10 flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Link \r\n                    href=\"/\" \r\n                    className=\"flex items-center group\" \r\n                    prefetch={false} \r\n                    onClick={closeMenu}\r\n                  >\r\n                    <div className=\"relative\">\r\n                      <Logo className=\"h-12 w-auto transition-transform duration-300 group-hover:scale-105\" />\r\n                      {/* Subtle glow effect */}\r\n                      <div className=\"absolute inset-0 bg-primary/10 rounded-lg blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\r\n                    </div>\r\n                  </Link>\r\n                  \r\n                  {/* Enhanced authentication status */}\r\n                  {isAuthenticated && (\r\n                    <div \r\n                      className=\"flex items-center px-3 py-2 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 border border-green-200/50 dark:border-green-800/50 backdrop-blur-sm\"\r\n                      style={{\r\n                        animation: 'slideInFromRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards'\r\n                      }}\r\n                    >\r\n                      <ClientOnlyIcon className=\"text-green-600 dark:text-green-400 mr-2\" width=\"14px\" height=\"14px\">\r\n                        <CheckCircle className=\"h-3.5 w-3.5 text-green-600 dark:text-green-400\" />\r\n                      </ClientOnlyIcon>\r\n                      <span className=\"text-sm font-medium text-green-700 dark:text-green-300\">Online</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </SheetHeader>\r\n          </div>\r\n          \r\n          {/* Navigation content container */}\r\n          <div className=\"px-6 pb-6\">\r\n            <div className=\"flex flex-col space-y-3 mt-8\">\r\n              <MobileNavLink href=\"/\" onClose={closeMenu} index={0}>\r\n                Home\r\n              </MobileNavLink>\r\n              <MobileNavLink href=\"/profile\" onClose={closeMenu} index={1}>\r\n                Profil\r\n              </MobileNavLink>\r\n              <MobileNavLink href=\"/opuscanner\" onClose={closeMenu} index={2}>\r\n                Opuscanner\r\n              </MobileNavLink>\r\n              <MobileNavLink href=\"/opulab\" onClose={closeMenu} index={3}>\r\n                Opulab\r\n              </MobileNavLink>\r\n              \r\n              {/* Enhanced footer section with animations */}\r\n              <div \r\n                className=\"pt-8 mt-6 border-t border-gradient-to-r from-transparent via-border/40 to-transparent\"\r\n                style={{\r\n                  animationDelay: '500ms',\r\n                  animation: 'slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards'\r\n                }}\r\n              >\r\n                <div className=\"flex items-center justify-between px-4 py-3 rounded-2xl bg-gradient-to-r from-background/50 to-background/80 border border-border/20 backdrop-blur-sm\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <AuthStatus />\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <DarkModeToggle />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </SheetContent>\r\n      </Sheet>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n\r\nfunction Logo({ className, ..._props }: { className?: string }) {\r\n  const { theme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    return <div className={className} />; // Return empty div while loading\r\n  }\r\n\r\n  // Determine logo source based on the current theme\r\n  const src = theme === 'dark' ? '/weiss-ohne-bg-svg.svg' : '/schwarz-ohne-bg-svg.svg';\r\n\r\n  return (\r\n    <div \r\n      className=\"relative\"\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n    >\r\n      <ClientOnlyImage\r\n        src={src}\r\n        alt=\"Logo\"\r\n        width={120}\r\n        height={40}\r\n        className={cn('transition-transform duration-300', className)}\r\n        priority\r\n      />\r\n      <div \r\n        className=\"absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent rounded-lg transition-opacity duration-300\"\r\n        style={{ opacity: isHovered ? 1 : 0 }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;AAoBA,wBAAwB;AACxB,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B1B,CAAC;AAED,gBAAgB;AAChB,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,WAAW,GAAG;IAC3B,IAAI,CAAC,SAAS,IAAI,CAAC,aAAa,CAAC,4BAA4B;QAC3D,aAAa,YAAY,CAAC,oBAAoB;QAC9C,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEA,2BAA2B;AAC3B,MAAM,UAAU,CAAC,EAAE,IAAI,EAAE,QAAQ,EAA+C,iBAC9E,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM;YACN,WAAU;YACV,UAAU;;gBAET;8BACD,6LAAC;oBAAK,WAAU;;;;;;;;;;;;;;;;;KARhB;AAaN,6DAA6D;AAC7D,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EAMN;IAEC,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;YAClC,WAAW;QACb;kBAEA,cAAA,6LAAC,oIAAA,CAAA,aAAU;YAAC,OAAO;sBACjB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAM;gBACN,WAAU;gBACV,UAAU;gBACV,SAAS;;kCAGT,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;MAjDM;AAmDN,mBAAmB;AACnB,MAAM,SAAmB;;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,aAAa;IAEhD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,YAAY,IAAM,cAAc;IAEtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mHACA,aACI,0EACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;wBAAoB,UAAU;;0CACrD,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAQ,MAAK;sCAAI;;;;;;sCAClB,6LAAC;4BAAQ,MAAK;sCAAW;;;;;;sCACzB,6LAAC;4BAAQ,MAAK;sCAAc;;;;;;sCAC5B,6LAAC;4BAAQ,MAAK;sCAAU;;;;;;sCACxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2IAAA,CAAA,UAAU;;;;;8CACX,6LAAC,6IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBACC,SAAS;oBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6IACA,kGACA,4FACA,cAAc;oBAEhB,cAAY,aAAa,eAAe;;sCAExC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2FACA,aAAa,4BAA4B;;;;;;8CAE3C,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6EACA,aAAa,sBAAsB;;;;;;8CAErC,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2FACA,aAAa,8BAA8B;;;;;;;;;;;;sCAK/C,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wFACA,cAAc;;;;;;;;;;;;8BAKlB,6LAAC,oIAAA,CAAA,QAAK;oBAAC,MAAM;oBAAY,cAAc;8BACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;wBACX,MAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAK,IAAG;gCAA0B,WAAU;0CAAU;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC,oIAAA,CAAA,cAAW;;0DACV,6LAAC,oIAAA,CAAA,aAAU;gDAAC,WAAU;0DAAU;;;;;;0DAChC,6LAAC,oIAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAAU;;;;;;0DAGtC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;4DACV,UAAU;4DACV,SAAS;sEAET,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;;;;;kFAEhB,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;wDAKlB,iCACC,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,WAAW;4DACb;;8EAEA,6LAAC,6IAAA,CAAA,iBAAc;oEAAC,WAAU;oEAA0C,OAAM;oEAAO,QAAO;8EACtF,cAAA,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;8EAEzB,6LAAC;oEAAK,WAAU;8EAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAc,MAAK;4CAAI,SAAS;4CAAW,OAAO;sDAAG;;;;;;sDAGtD,6LAAC;4CAAc,MAAK;4CAAW,SAAS;4CAAW,OAAO;sDAAG;;;;;;sDAG7D,6LAAC;4CAAc,MAAK;4CAAc,SAAS;4CAAW,OAAO;sDAAG;;;;;;sDAGhE,6LAAC;4CAAc,MAAK;4CAAU,SAAS;4CAAW,OAAO;sDAAG;;;;;;sDAK5D,6LAAC;4CACC,WAAU;4CACV,OAAO;gDACL,gBAAgB;gDAChB,WAAW;4CACb;sDAEA,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnC;GAhLM;;QACyC,kIAAA,CAAA,UAAO;;;MADhD;AAkLN,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,QAAgC;;IAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,WAAW;QACb;yBAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAI,WAAW;;;;;kBAAe,iCAAiC;IACzE;IAEA,mDAAmD;IACnD,MAAM,MAAM,UAAU,SAAS,2BAA2B;IAE1D,qBACE,6LAAC;QACC,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAEjC,6LAAC,gKAAA,CAAA,UAAe;gBACd,KAAK;gBACL,KAAI;gBACJ,OAAO;gBACP,QAAQ;gBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;gBACnD,QAAQ;;;;;;0BAEV,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,SAAS,YAAY,IAAI;gBAAE;;;;;;;;;;;;AAI5C;IApCS;;QACW,mJAAA,CAAA,WAAQ;;;MADnB;uCAsCM", "debugId": null}}, {"offset": {"line": 2013, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\n\r\ninterface PageTransitionContainerProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * A container component that handles global page transition setup\r\n * This component ensures that transitions are smooth and prevents layout shifts\r\n */\r\nconst PageTransitionContainer: React.FC<PageTransitionContainerProps> = ({ \r\n  children \r\n}) => {\r\n  const pathname = usePathname();\r\n  \r\n  // Set up global transition handling\r\n  useEffect(() => {\r\n    // Clean up any lingering transition classes when pathname changes\r\n    return () => {\r\n      document.body.classList.remove('page-transition-active');\r\n    };\r\n  }, [pathname]);\r\n\r\n  return (\r\n    <div className=\"w-full h-full overflow-hidden\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransitionContainer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASA;;;CAGC,GACD,MAAM,0BAAkE,CAAC,EACvE,QAAQ,EACT;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,kEAAkE;YAClE;qDAAO;oBACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBACjC;;QACF;4CAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;GAlBM;;QAGa,qIAAA,CAAA,cAAW;;;KAHxB;uCAoBS", "debugId": null}}, {"offset": {"line": 2070, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState } from 'react'\r\nimport { useServerInsertedHTML } from 'next/navigation'\r\nimport { ServerStyleSheet, StyleSheetManager } from 'styled-components'\r\n\r\nexport default function StyledComponentsRegistry({ children }: { children: React.ReactNode }) {\r\n  // Only create stylesheet once with lazy initial state\r\n  // x-ref: https://reactjs.org/docs/hooks-reference.html#lazy-initial-state\r\n  const [styledComponentsStyleSheet] = useState(() => new ServerStyleSheet())\r\n\r\n  useServerInsertedHTML(() => {\r\n    const styles = styledComponentsStyleSheet.getStyleElement()\r\n    styledComponentsStyleSheet.instance.clearTag()\r\n    return <>{styles}</>\r\n  })\r\n\r\n  if (typeof window !== 'undefined') return <>{children}</>\r\n\r\n  return (\r\n    <StyleSheetManager sheet={styledComponentsStyleSheet.instance}>\r\n      {children}\r\n    </StyleSheetManager>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS,yBAAyB,EAAE,QAAQ,EAAiC;;IAC1F,sDAAsD;IACtD,0EAA0E;IAC1E,MAAM,CAAC,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;6CAAE,IAAM,IAAI,yLAAA,CAAA,mBAAgB;;IAExE,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;0DAAE;YACpB,MAAM,SAAS,2BAA2B,eAAe;YACzD,2BAA2B,QAAQ,CAAC,QAAQ;YAC5C,qBAAO;0BAAG;;QACZ;;IAEA,wCAAmC,qBAAO;kBAAG;;;AAO/C;GAlBwB;;QAKtB,qIAAA,CAAA,wBAAqB;;;KALC", "debugId": null}}]}