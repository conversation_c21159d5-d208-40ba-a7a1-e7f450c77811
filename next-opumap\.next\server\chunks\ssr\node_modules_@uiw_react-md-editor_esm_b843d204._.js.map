{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/Context.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React from 'react';\nexport function reducer(state, action) {\n  return _extends({}, state, action);\n}\nexport var EditorContext = /*#__PURE__*/React.createContext({\n  markdown: ''\n});"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,QAAQ,KAAK,EAAE,MAAM;IACnC,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;AAC7B;AACO,IAAI,gBAAgB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IAC1D,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/TextArea/shortcuts.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nfunction getCommands(data, resulte) {\n  if (data === void 0) {\n    data = [];\n  }\n  if (resulte === void 0) {\n    resulte = {};\n  }\n  data.forEach(item => {\n    if (item.children && Array.isArray(item.children)) {\n      resulte = _extends({}, resulte, getCommands(item.children || []));\n    } else if (item.keyCommand && item.shortcuts && item.execute) {\n      resulte[item.shortcuts.toLocaleLowerCase()] = item;\n    }\n  });\n  return resulte;\n}\nexport default function shortcutsHandle(e, commands, commandOrchestrator, dispatch, state) {\n  if (commands === void 0) {\n    commands = [];\n  }\n  var data = getCommands(commands || []);\n  var shortcuts = [];\n  if (e.altKey) {\n    shortcuts.push('alt');\n  }\n  if (e.shiftKey) {\n    shortcuts.push('shift');\n  }\n  if (e.metaKey) {\n    shortcuts.push('cmd');\n  }\n  if (e.ctrl<PERSON>ey) {\n    shortcuts.push('ctrl');\n  }\n  if (shortcuts.length > 0 && !/(control|alt|meta|shift)/.test(e.key.toLocaleLowerCase())) {\n    shortcuts.push(e.key.toLocaleLowerCase());\n  }\n  if (/escape/.test(e.key.toLocaleLowerCase())) {\n    shortcuts.push('escape');\n  }\n  if (shortcuts.length < 1) {\n    return;\n  }\n  var equal = !!data[shortcuts.join('+')];\n  var command = equal ? data[shortcuts.join('+')] : undefined;\n  Object.keys(data).forEach(item => {\n    var isequal = item.split('+').every(v => {\n      if (/ctrlcmd/.test(v)) {\n        return shortcuts.includes('ctrl') || shortcuts.includes('cmd');\n      }\n      return shortcuts.includes(v);\n    });\n    if (isequal) {\n      command = data[item];\n    }\n  });\n  if (command && commandOrchestrator) {\n    e.stopPropagation();\n    e.preventDefault();\n    commandOrchestrator.executeCommand(command, dispatch, state, shortcuts);\n    return;\n  }\n}"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,IAAI,EAAE,OAAO;IAChC,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,EAAE;IACX;IACA,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU,CAAC;IACb;IACA,KAAK,OAAO,CAAC,CAAA;QACX,IAAI,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,GAAG;YACjD,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,YAAY,KAAK,QAAQ,IAAI,EAAE;QACjE,OAAO,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,EAAE;YAC5D,OAAO,CAAC,KAAK,SAAS,CAAC,iBAAiB,GAAG,GAAG;QAChD;IACF;IACA,OAAO;AACT;AACe,SAAS,gBAAgB,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK;IACvF,IAAI,aAAa,KAAK,GAAG;QACvB,WAAW,EAAE;IACf;IACA,IAAI,OAAO,YAAY,YAAY,EAAE;IACrC,IAAI,YAAY,EAAE;IAClB,IAAI,EAAE,MAAM,EAAE;QACZ,UAAU,IAAI,CAAC;IACjB;IACA,IAAI,EAAE,QAAQ,EAAE;QACd,UAAU,IAAI,CAAC;IACjB;IACA,IAAI,EAAE,OAAO,EAAE;QACb,UAAU,IAAI,CAAC;IACjB;IACA,IAAI,EAAE,OAAO,EAAE;QACb,UAAU,IAAI,CAAC;IACjB;IACA,IAAI,UAAU,MAAM,GAAG,KAAK,CAAC,2BAA2B,IAAI,CAAC,EAAE,GAAG,CAAC,iBAAiB,KAAK;QACvF,UAAU,IAAI,CAAC,EAAE,GAAG,CAAC,iBAAiB;IACxC;IACA,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,CAAC,iBAAiB,KAAK;QAC5C,UAAU,IAAI,CAAC;IACjB;IACA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB;IACF;IACA,IAAI,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK;IACvC,IAAI,UAAU,QAAQ,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,GAAG;IAClD,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;QACxB,IAAI,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,CAAA;YAClC,IAAI,UAAU,IAAI,CAAC,IAAI;gBACrB,OAAO,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC;YAC1D;YACA,OAAO,UAAU,QAAQ,CAAC;QAC5B;QACA,IAAI,SAAS;YACX,UAAU,IAAI,CAAC,KAAK;QACtB;IACF;IACA,IAAI,WAAW,qBAAqB;QAClC,EAAE,eAAe;QACjB,EAAE,cAAc;QAChB,oBAAoB,cAAc,CAAC,SAAS,UAAU,OAAO;QAC7D;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/TextArea/Markdown.js"], "sourcesContent": ["import _taggedTemplateLiteralLoose from \"@babel/runtime/helpers/taggedTemplateLiteralLoose\";\nvar _templateObject;\nimport React, { useContext, useEffect } from 'react';\nimport { rehype } from 'rehype';\nimport rehypePrism from 'rehype-prism-plus';\nimport { EditorContext } from '../../Context';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction html2Escape(sHtml) {\n  return sHtml\n  // .replace(/```(\\w+)?([\\s\\S]*?)(\\s.+)?```/g, (str: string) => {\n  //   return str.replace(\n  //     /[<&\"]/g,\n  //     (c: string) => (({ '<': '&lt;', '>': '&gt;', '&': '&amp;', '\"': '&quot;' } as Record<string, string>)[c]),\n  //   );\n  // })\n  .replace(/[<&\"]/g, c => ({\n    '<': '&lt;',\n    '>': '&gt;',\n    '&': '&amp;',\n    '\"': '&quot;'\n  })[c]);\n}\nexport default function Markdown(props) {\n  var {\n    prefixCls\n  } = props;\n  var {\n    markdown = '',\n    highlightEnable,\n    dispatch\n  } = useContext(EditorContext);\n  var preRef = /*#__PURE__*/React.createRef();\n  useEffect(() => {\n    if (preRef.current && dispatch) {\n      dispatch({\n        textareaPre: preRef.current\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  if (!markdown) {\n    return /*#__PURE__*/_jsx(\"pre\", {\n      ref: preRef,\n      className: prefixCls + \"-text-pre wmde-markdown-color\"\n    });\n  }\n  var mdStr = \"<pre class=\\\"language-markdown \" + prefixCls + \"-text-pre wmde-markdown-color\\\"><code class=\\\"language-markdown\\\">\" + html2Escape(String.raw(_templateObject || (_templateObject = _taggedTemplateLiteralLoose([\"\", \"\"])), markdown)) + \"\\n</code></pre>\";\n  if (highlightEnable) {\n    try {\n      mdStr = rehype().data('settings', {\n        fragment: true\n      })\n      // https://github.com/uiwjs/react-md-editor/issues/593\n      // @ts-ignore\n      .use(rehypePrism, {\n        ignoreMissing: true\n      }).processSync(mdStr).toString();\n    } catch (error) {}\n  }\n  return /*#__PURE__*/React.createElement('div', {\n    className: 'wmde-markdown-color',\n    dangerouslySetInnerHTML: {\n      __html: mdStr || ''\n    }\n  });\n}"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;AACA;;AALA,IAAI;;;;;;AAMJ,SAAS,YAAY,KAAK;IACxB,OAAO,KACP,gEAAgE;IAChE,wBAAwB;IACxB,gBAAgB;IAChB,iHAAiH;IACjH,OAAO;IACP,KAAK;KACJ,OAAO,CAAC,UAAU,CAAA,IAAK,CAAC;YACvB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP,CAAC,CAAC,CAAC,EAAE;AACP;AACe,SAAS,SAAS,KAAK;IACpC,IAAI,EACF,SAAS,EACV,GAAG;IACJ,IAAI,EACF,WAAW,EAAE,EACb,eAAe,EACf,QAAQ,EACT,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa;IAC5B,IAAI,SAAS,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,OAAO,IAAI,UAAU;YAC9B,SAAS;gBACP,aAAa,OAAO,OAAO;YAC7B;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,IAAI,CAAC,UAAU;QACb,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAC9B,KAAK;YACL,WAAW,YAAY;QACzB;IACF;IACA,IAAI,QAAQ,oCAAoC,YAAY,uEAAuE,YAAY,OAAO,GAAG,CAAC,mBAAmB,CAAC,kBAAkB,CAAA,GAAA,2KAAA,CAAA,UAA2B,AAAD,EAAE;QAAC;QAAI;KAAG,CAAC,GAAG,aAAa;IACrP,IAAI,iBAAiB;QACnB,IAAI;YACF,QAAQ,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,IAAI,IAAI,CAAC,YAAY;gBAChC,UAAU;YACZ,EACA,sDAAsD;YACtD,aAAa;aACZ,GAAG,CAAC,8JAAA,CAAA,UAAW,EAAE;gBAChB,eAAe;YACjB,GAAG,WAAW,CAAC,OAAO,QAAQ;QAChC,EAAE,OAAO,OAAO,CAAC;IACnB;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW;QACX,yBAAyB;YACvB,QAAQ,SAAS;QACnB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/utils/InsertTextAtPosition.js"], "sourcesContent": ["/**\n * The MIT License\n * Copyright (c) 2018 <PERSON><PERSON><PERSON><PERSON>\n * Copied from https://github.com/grassator/insert-text-at-cursor\n */\n\nvar browserSupportsTextareaTextNodes;\n\n/**\n * @param {HTMLElement} input\n * @return {boolean}\n */\nfunction canManipulateViaTextNodes(input) {\n  if (input.nodeName !== 'TEXTAREA') {\n    return false;\n  }\n  if (typeof browserSupportsTextareaTextNodes === 'undefined') {\n    var textarea = document.createElement('textarea');\n    textarea.value = '1';\n    browserSupportsTextareaTextNodes = !!textarea.firstChild;\n  }\n  return browserSupportsTextareaTextNodes;\n}\n\n/**\n * @param {HTMLTextAreaElement|HTMLInputElement} input\n * @param {string} text\n * @returns {void}\n */\nexport function insertTextAtPosition(input, text) {\n  // Most of the used APIs only work with the field selected\n  input.focus();\n\n  // IE 8-10\n  if (document.selection) {\n    var ieRange = document.selection.createRange();\n    ieRange.text = text;\n\n    // Move cursor after the inserted text\n    ieRange.collapse(false /* to the end */);\n    ieRange.select();\n    return;\n  }\n\n  // Webkit + Edge\n  var isSuccess = false;\n  if (text !== '') {\n    isSuccess = document.execCommand && document.execCommand('insertText', false, text);\n  } else {\n    isSuccess = document.execCommand && document.execCommand('delete', false);\n  }\n  if (!isSuccess) {\n    var start = input.selectionStart;\n    var end = input.selectionEnd;\n    // Firefox (non-standard method)\n    if (typeof input.setRangeText === 'function') {\n      input.setRangeText(text);\n    } else {\n      // To make a change we just need a Range, not a Selection\n      var range = document.createRange();\n      var textNode = document.createTextNode(text);\n      if (canManipulateViaTextNodes(input)) {\n        var node = input.firstChild;\n\n        // If textarea is empty, just insert the text\n        if (!node) {\n          input.appendChild(textNode);\n        } else {\n          // Otherwise we need to find a nodes for start and end\n          var offset = 0;\n          var startNode = null;\n          var endNode = null;\n          while (node && (startNode === null || endNode === null)) {\n            var nodeLength = node.nodeValue.length;\n\n            // if start of the selection falls into current node\n            if (start >= offset && start <= offset + nodeLength) {\n              range.setStart(startNode = node, start - offset);\n            }\n\n            // if end of the selection falls into current node\n            if (end >= offset && end <= offset + nodeLength) {\n              range.setEnd(endNode = node, end - offset);\n            }\n            offset += nodeLength;\n            node = node.nextSibling;\n          }\n\n          // If there is some text selected, remove it as we should replace it\n          if (start !== end) {\n            range.deleteContents();\n          }\n        }\n      }\n\n      // If the node is a textarea and the range doesn't span outside the element\n      //\n      // Get the commonAncestorContainer of the selected range and test its type\n      // If the node is of type `#text` it means that we're still working with text nodes within our textarea element\n      // otherwise, if it's of type `#document` for example it means our selection spans outside the textarea.\n      if (canManipulateViaTextNodes(input) && range.commonAncestorContainer.nodeName === '#text') {\n        // Finally insert a new node. The browser will automatically split start and end nodes into two if necessary\n        range.insertNode(textNode);\n      } else {\n        // If the node is not a textarea or the range spans outside a textarea the only way is to replace the whole value\n        var value = input.value;\n        input.value = value.slice(0, start) + text + value.slice(end);\n      }\n    }\n\n    // Correct the cursor position to be at the end of the insertion\n    input.setSelectionRange(start + text.length, start + text.length);\n\n    // Notify any possible listeners of the change\n    var e = document.createEvent('UIEvent');\n    e.initEvent('input', true, false);\n    input.dispatchEvent(e);\n  }\n}"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED,IAAI;AAEJ;;;CAGC,GACD,SAAS,0BAA0B,KAAK;IACtC,IAAI,MAAM,QAAQ,KAAK,YAAY;QACjC,OAAO;IACT;IACA,IAAI,OAAO,qCAAqC,aAAa;QAC3D,IAAI,WAAW,SAAS,aAAa,CAAC;QACtC,SAAS,KAAK,GAAG;QACjB,mCAAmC,CAAC,CAAC,SAAS,UAAU;IAC1D;IACA,OAAO;AACT;AAOO,SAAS,qBAAqB,KAAK,EAAE,IAAI;IAC9C,0DAA0D;IAC1D,MAAM,KAAK;IAEX,UAAU;IACV,IAAI,SAAS,SAAS,EAAE;QACtB,IAAI,UAAU,SAAS,SAAS,CAAC,WAAW;QAC5C,QAAQ,IAAI,GAAG;QAEf,sCAAsC;QACtC,QAAQ,QAAQ,CAAC;QACjB,QAAQ,MAAM;QACd;IACF;IAEA,gBAAgB;IAChB,IAAI,YAAY;IAChB,IAAI,SAAS,IAAI;QACf,YAAY,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,cAAc,OAAO;IAChF,OAAO;QACL,YAAY,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,UAAU;IACrE;IACA,IAAI,CAAC,WAAW;QACd,IAAI,QAAQ,MAAM,cAAc;QAChC,IAAI,MAAM,MAAM,YAAY;QAC5B,gCAAgC;QAChC,IAAI,OAAO,MAAM,YAAY,KAAK,YAAY;YAC5C,MAAM,YAAY,CAAC;QACrB,OAAO;YACL,yDAAyD;YACzD,IAAI,QAAQ,SAAS,WAAW;YAChC,IAAI,WAAW,SAAS,cAAc,CAAC;YACvC,IAAI,0BAA0B,QAAQ;gBACpC,IAAI,OAAO,MAAM,UAAU;gBAE3B,6CAA6C;gBAC7C,IAAI,CAAC,MAAM;oBACT,MAAM,WAAW,CAAC;gBACpB,OAAO;oBACL,sDAAsD;oBACtD,IAAI,SAAS;oBACb,IAAI,YAAY;oBAChB,IAAI,UAAU;oBACd,MAAO,QAAQ,CAAC,cAAc,QAAQ,YAAY,IAAI,EAAG;wBACvD,IAAI,aAAa,KAAK,SAAS,CAAC,MAAM;wBAEtC,oDAAoD;wBACpD,IAAI,SAAS,UAAU,SAAS,SAAS,YAAY;4BACnD,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ;wBAC3C;wBAEA,kDAAkD;wBAClD,IAAI,OAAO,UAAU,OAAO,SAAS,YAAY;4BAC/C,MAAM,MAAM,CAAC,UAAU,MAAM,MAAM;wBACrC;wBACA,UAAU;wBACV,OAAO,KAAK,WAAW;oBACzB;oBAEA,oEAAoE;oBACpE,IAAI,UAAU,KAAK;wBACjB,MAAM,cAAc;oBACtB;gBACF;YACF;YAEA,2EAA2E;YAC3E,EAAE;YACF,0EAA0E;YAC1E,+GAA+G;YAC/G,wGAAwG;YACxG,IAAI,0BAA0B,UAAU,MAAM,uBAAuB,CAAC,QAAQ,KAAK,SAAS;gBAC1F,4GAA4G;gBAC5G,MAAM,UAAU,CAAC;YACnB,OAAO;gBACL,iHAAiH;gBACjH,IAAI,QAAQ,MAAM,KAAK;gBACvB,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,GAAG,SAAS,OAAO,MAAM,KAAK,CAAC;YAC3D;QACF;QAEA,gEAAgE;QAChE,MAAM,iBAAiB,CAAC,QAAQ,KAAK,MAAM,EAAE,QAAQ,KAAK,MAAM;QAEhE,8CAA8C;QAC9C,IAAI,IAAI,SAAS,WAAW,CAAC;QAC7B,EAAE,SAAS,CAAC,SAAS,MAAM;QAC3B,MAAM,aAAa,CAAC;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/utils/markdownUtils.js"], "sourcesContent": ["export function selectWord(_ref) {\n  var {\n    text,\n    selection,\n    prefix,\n    suffix = prefix\n  } = _ref;\n  var result = selection;\n  if (text && text.length && selection.start === selection.end) {\n    result = getSurroundingWord(text, selection.start);\n  }\n  if (result.start >= prefix.length && result.end <= text.length - suffix.length) {\n    var selectedTextContext = text.slice(result.start - prefix.length, result.end + suffix.length);\n    if (selectedTextContext.startsWith(prefix) && selectedTextContext.endsWith(suffix)) {\n      return {\n        start: result.start - prefix.length,\n        end: result.end + suffix.length\n      };\n    }\n  }\n  return result;\n}\nexport function selectLine(_ref2) {\n  var {\n    text,\n    selection\n  } = _ref2;\n  var start = text.slice(0, selection.start).lastIndexOf('\\n') + 1;\n  var end = text.slice(selection.end).indexOf('\\n') + selection.end;\n  if (end === selection.end - 1) {\n    end = text.length;\n  }\n  return {\n    start,\n    end\n  };\n}\n\n/**\n *  Gets the number of line-breaks that would have to be inserted before the given 'startPosition'\n *  to make sure there's an empty line between 'startPosition' and the previous text\n */\nexport function getBreaksNeededForEmptyLineBefore(text, startPosition) {\n  if (text === void 0) {\n    text = '';\n  }\n  if (startPosition === 0) return 0;\n\n  // rules:\n  // - If we're in the first line, no breaks are needed\n  // - Otherwise there must be 2 breaks before the previous character. Depending on how many breaks exist already, we\n  //      may need to insert 0, 1 or 2 breaks\n\n  var neededBreaks = 2;\n  var isInFirstLine = true;\n  for (var i = startPosition - 1; i >= 0 && neededBreaks >= 0; i--) {\n    switch (text.charCodeAt(i)) {\n      case 32:\n        // blank space\n        continue;\n      case 10:\n        // line break\n        neededBreaks--;\n        isInFirstLine = false;\n        break;\n      default:\n        return neededBreaks;\n    }\n  }\n  return isInFirstLine ? 0 : neededBreaks;\n}\n\n/**\n *  Gets the number of line-breaks that would have to be inserted after the given 'startPosition'\n *  to make sure there's an empty line between 'startPosition' and the next text\n */\nexport function getBreaksNeededForEmptyLineAfter(text, startPosition) {\n  if (text === void 0) {\n    text = '';\n  }\n  if (startPosition === text.length - 1) return 0;\n\n  // rules:\n  // - If we're in the first line, no breaks are needed\n  // - Otherwise there must be 2 breaks before the previous character. Depending on how many breaks exist already, we\n  //      may need to insert 0, 1 or 2 breaks\n\n  var neededBreaks = 2;\n  var isInLastLine = true;\n  for (var i = startPosition; i < text.length && neededBreaks >= 0; i++) {\n    switch (text.charCodeAt(i)) {\n      case 32:\n        continue;\n      case 10:\n        {\n          neededBreaks--;\n          isInLastLine = false;\n          break;\n        }\n      default:\n        return neededBreaks;\n    }\n  }\n  return isInLastLine ? 0 : neededBreaks;\n}\nexport function getSurroundingWord(text, position) {\n  if (!text) throw Error(\"Argument 'text' should be truthy\");\n  var isWordDelimiter = c => c === ' ' || c.charCodeAt(0) === 10;\n\n  // leftIndex is initialized to 0 because if selection is 0, it won't even enter the iteration\n  var start = 0;\n  // rightIndex is initialized to text.length because if selection is equal to text.length it won't even enter the interation\n  var end = text.length;\n\n  // iterate to the left\n  for (var i = position; i - 1 > -1; i--) {\n    if (isWordDelimiter(text[i - 1])) {\n      start = i;\n      break;\n    }\n  }\n\n  // iterate to the right\n  for (var _i = position; _i < text.length; _i++) {\n    if (isWordDelimiter(text[_i])) {\n      end = _i;\n      break;\n    }\n  }\n  return {\n    start,\n    end\n  };\n}\nexport function executeCommand(_ref3) {\n  var {\n    api,\n    selectedText,\n    selection,\n    prefix,\n    suffix = prefix\n  } = _ref3;\n  if (selectedText.length >= prefix.length + suffix.length && selectedText.startsWith(prefix) && selectedText.endsWith(suffix)) {\n    api.replaceSelection(selectedText.slice(prefix.length, suffix.length ? -suffix.length : undefined));\n    api.setSelectionRange({\n      start: selection.start - prefix.length,\n      end: selection.end - prefix.length\n    });\n  } else {\n    api.replaceSelection(\"\" + prefix + selectedText + suffix);\n    api.setSelectionRange({\n      start: selection.start + prefix.length,\n      end: selection.end + prefix.length\n    });\n  }\n}\n/**\n * Inserts insertionString before each line\n */\nexport function insertBeforeEachLine(selectedText, insertBefore) {\n  var lines = selectedText.split(/\\n/);\n  var insertionLength = 0;\n  var modifiedText = lines.map((item, index) => {\n    if (typeof insertBefore === 'string') {\n      if (item.startsWith(insertBefore)) {\n        insertionLength -= insertBefore.length;\n        return item.slice(insertBefore.length);\n      }\n      insertionLength += insertBefore.length;\n      return insertBefore + item;\n    }\n    if (typeof insertBefore === 'function') {\n      if (item.startsWith(insertBefore(item, index))) {\n        insertionLength -= insertBefore(item, index).length;\n        return item.slice(insertBefore(item, index).length);\n      }\n      var insertionResult = insertBefore(item, index);\n      insertionLength += insertionResult.length;\n      return insertBefore(item, index) + item;\n    }\n    throw Error('insertion is expected to be either a string or a function');\n  }).join('\\n');\n  return {\n    modifiedText,\n    insertionLength\n  };\n}"], "names": [], "mappings": ";;;;;;;;;AAAO,SAAS,WAAW,IAAI;IAC7B,IAAI,EACF,IAAI,EACJ,SAAS,EACT,MAAM,EACN,SAAS,MAAM,EAChB,GAAG;IACJ,IAAI,SAAS;IACb,IAAI,QAAQ,KAAK,MAAM,IAAI,UAAU,KAAK,KAAK,UAAU,GAAG,EAAE;QAC5D,SAAS,mBAAmB,MAAM,UAAU,KAAK;IACnD;IACA,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI,OAAO,GAAG,IAAI,KAAK,MAAM,GAAG,OAAO,MAAM,EAAE;QAC9E,IAAI,sBAAsB,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG,OAAO,MAAM,EAAE,OAAO,GAAG,GAAG,OAAO,MAAM;QAC7F,IAAI,oBAAoB,UAAU,CAAC,WAAW,oBAAoB,QAAQ,CAAC,SAAS;YAClF,OAAO;gBACL,OAAO,OAAO,KAAK,GAAG,OAAO,MAAM;gBACnC,KAAK,OAAO,GAAG,GAAG,OAAO,MAAM;YACjC;QACF;IACF;IACA,OAAO;AACT;AACO,SAAS,WAAW,KAAK;IAC9B,IAAI,EACF,IAAI,EACJ,SAAS,EACV,GAAG;IACJ,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,EAAE,WAAW,CAAC,QAAQ;IAC/D,IAAI,MAAM,KAAK,KAAK,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,QAAQ,UAAU,GAAG;IACjE,IAAI,QAAQ,UAAU,GAAG,GAAG,GAAG;QAC7B,MAAM,KAAK,MAAM;IACnB;IACA,OAAO;QACL;QACA;IACF;AACF;AAMO,SAAS,kCAAkC,IAAI,EAAE,aAAa;IACnE,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO;IACT;IACA,IAAI,kBAAkB,GAAG,OAAO;IAEhC,SAAS;IACT,qDAAqD;IACrD,mHAAmH;IACnH,2CAA2C;IAE3C,IAAI,eAAe;IACnB,IAAI,gBAAgB;IACpB,IAAK,IAAI,IAAI,gBAAgB,GAAG,KAAK,KAAK,gBAAgB,GAAG,IAAK;QAChE,OAAQ,KAAK,UAAU,CAAC;YACtB,KAAK;gBAEH;YACF,KAAK;gBACH,aAAa;gBACb;gBACA,gBAAgB;gBAChB;YACF;gBACE,OAAO;QACX;IACF;IACA,OAAO,gBAAgB,IAAI;AAC7B;AAMO,SAAS,iCAAiC,IAAI,EAAE,aAAa;IAClE,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO;IACT;IACA,IAAI,kBAAkB,KAAK,MAAM,GAAG,GAAG,OAAO;IAE9C,SAAS;IACT,qDAAqD;IACrD,mHAAmH;IACnH,2CAA2C;IAE3C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAK,IAAI,IAAI,eAAe,IAAI,KAAK,MAAM,IAAI,gBAAgB,GAAG,IAAK;QACrE,OAAQ,KAAK,UAAU,CAAC;YACtB,KAAK;gBACH;YACF,KAAK;gBACH;oBACE;oBACA,eAAe;oBACf;gBACF;YACF;gBACE,OAAO;QACX;IACF;IACA,OAAO,eAAe,IAAI;AAC5B;AACO,SAAS,mBAAmB,IAAI,EAAE,QAAQ;IAC/C,IAAI,CAAC,MAAM,MAAM,MAAM;IACvB,IAAI,kBAAkB,CAAA,IAAK,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO;IAE5D,6FAA6F;IAC7F,IAAI,QAAQ;IACZ,2HAA2H;IAC3H,IAAI,MAAM,KAAK,MAAM;IAErB,sBAAsB;IACtB,IAAK,IAAI,IAAI,UAAU,IAAI,IAAI,CAAC,GAAG,IAAK;QACtC,IAAI,gBAAgB,IAAI,CAAC,IAAI,EAAE,GAAG;YAChC,QAAQ;YACR;QACF;IACF;IAEA,uBAAuB;IACvB,IAAK,IAAI,KAAK,UAAU,KAAK,KAAK,MAAM,EAAE,KAAM;QAC9C,IAAI,gBAAgB,IAAI,CAAC,GAAG,GAAG;YAC7B,MAAM;YACN;QACF;IACF;IACA,OAAO;QACL;QACA;IACF;AACF;AACO,SAAS,eAAe,KAAK;IAClC,IAAI,EACF,GAAG,EACH,YAAY,EACZ,SAAS,EACT,MAAM,EACN,SAAS,MAAM,EAChB,GAAG;IACJ,IAAI,aAAa,MAAM,IAAI,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,aAAa,UAAU,CAAC,WAAW,aAAa,QAAQ,CAAC,SAAS;QAC5H,IAAI,gBAAgB,CAAC,aAAa,KAAK,CAAC,OAAO,MAAM,EAAE,OAAO,MAAM,GAAG,CAAC,OAAO,MAAM,GAAG;QACxF,IAAI,iBAAiB,CAAC;YACpB,OAAO,UAAU,KAAK,GAAG,OAAO,MAAM;YACtC,KAAK,UAAU,GAAG,GAAG,OAAO,MAAM;QACpC;IACF,OAAO;QACL,IAAI,gBAAgB,CAAC,KAAK,SAAS,eAAe;QAClD,IAAI,iBAAiB,CAAC;YACpB,OAAO,UAAU,KAAK,GAAG,OAAO,MAAM;YACtC,KAAK,UAAU,GAAG,GAAG,OAAO,MAAM;QACpC;IACF;AACF;AAIO,SAAS,qBAAqB,YAAY,EAAE,YAAY;IAC7D,IAAI,QAAQ,aAAa,KAAK,CAAC;IAC/B,IAAI,kBAAkB;IACtB,IAAI,eAAe,MAAM,GAAG,CAAC,CAAC,MAAM;QAClC,IAAI,OAAO,iBAAiB,UAAU;YACpC,IAAI,KAAK,UAAU,CAAC,eAAe;gBACjC,mBAAmB,aAAa,MAAM;gBACtC,OAAO,KAAK,KAAK,CAAC,aAAa,MAAM;YACvC;YACA,mBAAmB,aAAa,MAAM;YACtC,OAAO,eAAe;QACxB;QACA,IAAI,OAAO,iBAAiB,YAAY;YACtC,IAAI,KAAK,UAAU,CAAC,aAAa,MAAM,SAAS;gBAC9C,mBAAmB,aAAa,MAAM,OAAO,MAAM;gBACnD,OAAO,KAAK,KAAK,CAAC,aAAa,MAAM,OAAO,MAAM;YACpD;YACA,IAAI,kBAAkB,aAAa,MAAM;YACzC,mBAAmB,gBAAgB,MAAM;YACzC,OAAO,aAAa,MAAM,SAAS;QACrC;QACA,MAAM,MAAM;IACd,GAAG,IAAI,CAAC;IACR,OAAO;QACL;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/bold.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var bold = {\n  name: 'bold',\n  keyCommand: 'bold',\n  shortcuts: 'ctrlcmd+b',\n  prefix: '**',\n  buttonProps: {\n    'aria-label': 'Add bold text (ctrl + b)',\n    title: 'Add bold text (ctrl + b)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 384 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M304.793 243.891c33.639-18.537 53.657-54.16 53.657-95.693 0-48.236-26.25-87.626-68.626-104.179C265.138 34.01 240.849 32 209.661 32H24c-8.837 0-16 7.163-16 16v33.049c0 8.837 7.163 16 16 16h33.113v318.53H24c-8.837 0-16 7.163-16 16V464c0 8.837 7.163 16 16 16h195.69c24.203 0 44.834-1.289 66.866-7.584C337.52 457.193 376 410.647 376 350.014c0-52.168-26.573-91.684-71.207-106.123zM142.217 100.809h67.444c16.294 0 27.536 2.019 37.525 6.717 15.828 8.479 24.906 26.502 24.906 49.446 0 35.029-20.32 56.79-53.029 56.79h-76.846V100.809zm112.642 305.475c-10.14 4.056-22.677 4.907-31.409 4.907h-81.233V281.943h84.367c39.645 0 63.057 25.38 63.057 63.057.001 28.425-13.66 52.483-34.782 61.284z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    executeCommand({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,OAAO;IAChB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,MAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/code.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var codeBlock = {\n  name: 'codeBlock',\n  keyCommand: 'codeBlock',\n  shortcuts: 'ctrlcmd+shift+j',\n  prefix: '```',\n  buttonProps: {\n    'aria-label': 'Insert Code Block (ctrl + shift + j)',\n    title: 'Insert Code Block (ctrl + shift +j)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"13\",\n    height: \"13\",\n    role: \"img\",\n    viewBox: \"0 0 156 156\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M110.85 120.575 43.7 120.483333 43.7083334 110.091667 110.85 110.191667 110.841667 120.583333 110.85 120.575ZM85.1333334 87.1916666 43.625 86.7083332 43.7083334 76.3166666 85.2083334 76.7916666 85.1333334 87.1916666 85.1333334 87.1916666ZM110.841667 53.4166666 43.7 53.3166666 43.7083334 42.925 110.85 43.025 110.841667 53.4166666ZM36 138C27.2916666 138 20.75 136.216667 16.4 132.666667 12.1333334 129.2 10 124.308333 10 118L10 95.3333332C10 91.0666666 9.25 88.1333332 7.7333334 86.5333332 6.3166668 84.8416666 3.7333334 84 0 84L0 72C3.7333334 72 6.3083334 71.2 7.7333334 69.6 9.2416668 67.9083334 10 64.9333334 10 60.6666666L10 38C10 31.775 12.1333334 26.8833334 16.4 23.3333332 20.7583334 19.7749998 27.2916666 18 36 18L40.6666668 18 40.6666668 30 36 30C34.0212222 29.9719277 32.1263151 30.7979128 30.8 32.2666666 29.3605875 33.8216362 28.5938182 35.8823287 28.6666668 38L28.6666668 60.6666666C28.6666668 67.5083332 26.6666668 72.4 22.6666668 75.3333332 20.9317416 76.7274684 18.8640675 77.6464347 16.6666668 78 18.8916668 78.35 20.8916668 79.2416666 22.6666668 80.6666666 26.6666668 83.95 28.6666668 88.8416666 28.6666668 95.3333332L28.6666668 118C28.6666668 120.308333 29.3750002 122.216667 30.8 123.733333 32.2166666 125.241667 33.9583334 126 36 126L40.6666668 126 40.6666668 138 36 138 36 138ZM114.116667 126 118.783333 126C120.833333 126 122.566667 125.241667 123.983333 123.733333 125.422746 122.178364 126.189515 120.117671 126.116667 118L126.116667 95.3333332C126.116667 88.8333332 128.116667 83.9499998 132.116667 80.6666666 133.9 79.2416666 135.9 78.35 138.116667 78 135.919156 77.6468047 133.851391 76.7277979 132.116667 75.3333332 128.116667 72.3999998 126.116667 67.5 126.116667 60.6666666L126.116667 38C126.189515 35.8823287 125.422746 33.8216361 123.983333 32.2666666 122.657018 30.7979128 120.762111 29.9719277 118.783333 30L114.116667 30 114.116667 18 118.783333 18C127.5 18 133.983333 19.775 138.25 23.3333332 142.608333 26.8833332 144.783333 31.7749998 144.783333 38L144.783333 60.6666666C144.783333 64.9333332 145.5 67.9083332 146.916667 69.6 148.433333 71.2 151.05 72 154.783333 72L154.783333 84C151.05 84 148.433333 84.8333334 146.916667 86.5333332 145.5 88.1333332 144.783333 91.0666666 144.783333 95.3333332L144.783333 118C144.783333 124.308333 142.616667 129.2 138.25 132.666667 133.983333 136.216667 127.5 138 118.783333 138L114.116667 138 114.116667 126 114.116667 126Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: '```\\n',\n      suffix: '\\n```'\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n\n    // Based on context determine if new line is needed or not\n    var prefix = '\\n```\\n';\n    var suffix = '\\n```\\n';\n    if (state1.selectedText.length >= prefix.length + suffix.length - 2 && state1.selectedText.startsWith(prefix) && state1.selectedText.endsWith(suffix)) {\n      // Remove code block\n      prefix = '```\\n';\n      suffix = '\\n```';\n    } else {\n      // Add code block\n      if (state1.selection.start >= 1 && state.text.slice(state1.selection.start - 1, state1.selection.start) === '\\n' || state1.selection.start === 0) {\n        prefix = '```\\n';\n      }\n      if (state1.selection.end <= state.text.length - 1 && state.text.slice(state1.selection.end, state1.selection.end + 1) === '\\n' || state1.selection.end === state.text.length) {\n        suffix = '\\n```';\n      }\n    }\n    var newSelectionRange2 = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix,\n      suffix\n    });\n    var state2 = api.setSelectionRange(newSelectionRange2);\n    executeCommand({\n      api,\n      selectedText: state2.selectedText,\n      selection: state.selection,\n      prefix,\n      suffix\n    });\n  }\n};\nexport var code = {\n  name: 'code',\n  keyCommand: 'code',\n  shortcuts: 'ctrlcmd+j',\n  prefix: '`',\n  buttonProps: {\n    'aria-label': 'Insert code (ctrl + j)',\n    title: 'Insert code (ctrl + j)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"14\",\n    height: \"14\",\n    role: \"img\",\n    viewBox: \"0 0 640 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z\"\n    })\n  }),\n  execute: (state, api) => {\n    if (state.selectedText.indexOf('\\n') === -1) {\n      var newSelectionRange = selectWord({\n        text: state.text,\n        selection: state.selection,\n        prefix: state.command.prefix\n      });\n      var state1 = api.setSelectionRange(newSelectionRange);\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix\n      });\n    } else {\n      codeBlock.execute(state, api);\n    }\n  }\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,IAAI,YAAY;IACrB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ;YACR,QAAQ;QACV;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QAEnC,0DAA0D;QAC1D,IAAI,SAAS;QACb,IAAI,SAAS;QACb,IAAI,OAAO,YAAY,CAAC,MAAM,IAAI,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG,KAAK,OAAO,YAAY,CAAC,UAAU,CAAC,WAAW,OAAO,YAAY,CAAC,QAAQ,CAAC,SAAS;YACrJ,oBAAoB;YACpB,SAAS;YACT,SAAS;QACX,OAAO;YACL,iBAAiB;YACjB,IAAI,OAAO,SAAS,CAAC,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,SAAS,CAAC,KAAK,MAAM,QAAQ,OAAO,SAAS,CAAC,KAAK,KAAK,GAAG;gBAChJ,SAAS;YACX;YACA,IAAI,OAAO,SAAS,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG,EAAE,OAAO,SAAS,CAAC,GAAG,GAAG,OAAO,QAAQ,OAAO,SAAS,CAAC,GAAG,KAAK,MAAM,IAAI,CAAC,MAAM,EAAE;gBAC5K,SAAS;YACX;QACF;QACA,IAAI,qBAAqB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YAClC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B;YACA;QACF;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B;YACA;QACF;IACF;AACF;AACO,IAAI,OAAO;IAChB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,MAAM,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG;YAC3C,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;gBACjC,MAAM,MAAM,IAAI;gBAChB,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;YACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;YACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF,OAAO;YACL,UAAU,OAAO,CAAC,OAAO;QAC3B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/comment.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var comment = {\n  name: 'comment',\n  keyCommand: 'comment',\n  shortcuts: 'ctrlcmd+/',\n  prefix: '<!-- ',\n  suffix: ' -->',\n  buttonProps: {\n    'aria-label': 'Insert comment (ctrl + /)',\n    title: 'Insert comment (ctrl + /)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    height: \"1em\",\n    width: \"1em\",\n    viewBox: \"0 0 25 25\",\n    children: /*#__PURE__*/_jsxs(\"g\", {\n      fill: \"none\",\n      fillRule: \"evenodd\",\n      children: [/*#__PURE__*/_jsx(\"polygon\", {\n        points: \".769 .727 24.981 .727 24.981 24.727 .769 24.727\"\n      }), /*#__PURE__*/_jsx(\"path\", {\n        stroke: \"currentColor\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"3\",\n        d: \"M12.625,23.8787879 L8.125,19.6969697 L5.125,19.6969697 C2.63971863,19.6969697 0.625,17.8247059 0.625,15.5151515 L0.625,7.15151515 C0.625,4.84196074 2.63971863,2.96969697 5.125,2.96969697 L20.125,2.96969697 C22.6102814,2.96969697 24.625,4.84196074 24.625,7.15151515 L24.625,15.5151515 C24.625,17.8247059 22.6102814,19.6969697 20.125,19.6969697 L17.125,19.6969697 L12.625,23.8787879\"\n      }), /*#__PURE__*/_jsx(\"path\", {\n        stroke: \"currentColor\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: \"3\",\n        d: \"M10.625,8.54545455 L7.25,11.3333333 L10.625,14.1212121 M15.6875,8.54545455 L19.0625,11.3333333 L15.6875,14.1212121\"\n      })]\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    executeCommand({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,UAAU;IACnB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,QAAQ;QACR,OAAO;QACP,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,KAAK;YAChC,MAAM;YACN,UAAU;YACV,UAAU;gBAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;oBACtC,QAAQ;gBACV;gBAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oBAC5B,QAAQ;oBACR,eAAe;oBACf,gBAAgB;oBAChB,aAAa;oBACb,GAAG;gBACL;gBAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oBAC5B,QAAQ;oBACR,eAAe;oBACf,gBAAgB;oBAChB,aAAa;oBACb,GAAG;gBACL;aAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/divider.js"], "sourcesContent": ["export var divider = {\n  keyCommand: 'divider'\n};"], "names": [], "mappings": ";;;AAAO,IAAI,UAAU;IACnB,YAAY;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/fullscreen.js"], "sourcesContent": ["import React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var fullscreen = {\n  name: 'fullscreen',\n  keyCommand: 'fullscreen',\n  shortcuts: 'ctrlcmd+0',\n  value: 'fullscreen',\n  buttonProps: {\n    'aria-label': 'Toggle fullscreen (ctrl + 0)',\n    title: 'Toggle fullscreen (ctrl+ 0)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M118 171.133334L118 342.200271C118 353.**********.675 365.**********.**********.333605L382.**********.333605C394.**********.**********.**********.**********.**********.200271L405.**********.133334C405.767948 159.566667 397.092948 148 382.634614 148L141.133333 148C126.674999 148 117.999999 156.675 118 171.133334zM465.353591 413.444444L370 413.444444 370 471.222222 474.0221 471.222222C500.027624 471.222222 520.254143 451 520.254143 425L520.254143 321 462.464089 321 462.464089 413.444444 465.353591 413.444444zM471.0221 43L367 43 367 100.777778 462.353591 100.777778 462.353591 196.111111 520.143647 196.111111 520.143647 89.2222219C517.254144 63.2222219 497.027624 43 471.0221 43zM57.7900547 100.777778L153.143646 100.777778 153.143646 43 46.2320439 43C20.2265191 43 0 63.2222219 0 89.2222219L0 193.222222 57.7900547 193.222222 57.7900547 100.777778zM57.7900547 321L0 321 0 425C0 451 20.2265191 471.222222 46.2320439 471.222223L150.**********.**********.**********.444445 57.7900547 413.444445 57.7900547 321z\"\n    })\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        fullscreen: !executeCommandState.fullscreen\n      });\n    }\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,IAAI,aAAa;IACtB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,OAAO;IACP,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO,KAAK,UAAU,qBAAqB;QACnD,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,aAAa,YAAY,qBAAqB;YAChD,SAAS;gBACP,YAAY,CAAC,oBAAoB,UAAU;YAC7C;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/group.js"], "sourcesContent": ["import _objectDestructuringEmpty from \"@babel/runtime/helpers/objectDestructuringEmpty\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var group = (arr, options) => {\n  var data = _extends({\n    children: arr,\n    icon: /*#__PURE__*/_jsx(\"svg\", {\n      width: \"12\",\n      height: \"12\",\n      viewBox: \"0 0 520 520\",\n      children: /*#__PURE__*/_jsx(\"path\", {\n        fill: \"currentColor\",\n        d: \"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z\"\n      })\n    }),\n    execute: () => {}\n  }, options, {\n    keyCommand: 'group'\n  });\n  if (Array.isArray(data.children)) {\n    data.children = data.children.map(_ref => {\n      var item = _extends({}, (_objectDestructuringEmpty(_ref), _ref));\n      item.parent = data;\n      return _extends({}, item);\n    });\n  }\n  return data;\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,IAAI,QAAQ,CAAC,KAAK;IACvB,IAAI,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;QAClB,UAAU;QACV,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAC7B,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBAClC,MAAM;gBACN,GAAG;YACL;QACF;QACA,SAAS,KAAO;IAClB,GAAG,SAAS;QACV,YAAY;IACd;IACA,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,GAAG;QAChC,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA;YAChC,IAAI,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,CAAC,CAAA,GAAA,yKAAA,CAAA,UAAyB,AAAD,EAAE,OAAO,IAAI;YAC9D,KAAK,MAAM,GAAG;YACd,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;QACtB;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/hr.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var hr = {\n  name: 'hr',\n  keyCommand: 'hr',\n  shortcuts: 'ctrlcmd+h',\n  prefix: '\\n\\n---\\n',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert HR (ctrl + h)',\n    title: 'Insert HR (ctrl + h)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 175 175\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M0,129 L175,129 L175,154 L0,154 L0,129 Z M3,9 L28.2158203,9 L28.2158203,47.9824219 L55.7695313,47.9824219 L55.7695313,9 L81.0966797,9 L81.0966797,107.185547 L55.7695313,107.185547 L55.7695313,68.0214844 L28.2158203,68.0214844 L28.2158203,107.185547 L3,107.185547 L3,9 Z M93.1855469,100.603516 L93.1855469,19 L135.211914,19 C143.004922,19 148.960917,19.6679621 153.080078,21.0039063 C157.199239,22.3398504 160.520495,24.8168764 163.043945,28.4350586 C165.567395,32.0532407 166.829102,36.459935 166.829102,41.6552734 C166.829102,46.1826398 165.864267,50.0883625 163.93457,53.3725586 C162.004873,56.6567547 159.351579,59.3193257 155.974609,61.3603516 C153.822255,62.6591862 150.872089,63.7353473 147.124023,64.5888672 C150.129898,65.5908253 152.319329,66.5927684 153.692383,67.5947266 C154.620122,68.2626987 155.965323,69.6913953 157.728027,71.8808594 C159.490731,74.0703234 160.668942,75.7587831 161.262695,76.9462891 L173,100.603516 L144.953125,100.603516 L131.482422,75.6660156 C129.775382,72.4374839 128.253913,70.3408251 126.917969,69.3759766 C125.0996,68.1142515 123.040051,67.4833984 120.739258,67.4833984 L118.512695,67.4833984 L118.512695,100.603516 L93.1855469,100.603516 Z M118.512695,52.0644531 L129.144531,52.0644531 C130.294928,52.0644531 132.521468,51.6933631 135.824219,50.9511719 C137.494149,50.6171858 138.857905,49.7636787 139.915527,48.390625 C140.97315,47.0175713 141.501953,45.4404386 141.501953,43.6591797 C141.501953,41.0244009 140.667001,39.0019602 138.99707,37.5917969 C137.32714,36.1816336 134.191429,35.4765625 129.589844,35.4765625 L117.512695,35.4765625 L118.512695,52.0644531 Z\",\n      transform: \"translate(0 9)\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.length >= state.command.prefix.length && state1.selectedText.startsWith(state.command.prefix)) {\n      // Remove\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      // Add\n      state1 = api.setSelectionRange({\n        start: state.selection.start,\n        end: state.selection.start\n      });\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    }\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,KAAK;IACd,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;YACH,WAAW;QACb;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,IAAI,OAAO,YAAY,CAAC,MAAM,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,MAAM,GAAG;YACrH,SAAS;YACT,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF,OAAO;YACL,MAAM;YACN,SAAS,IAAI,iBAAiB,CAAC;gBAC7B,OAAO,MAAM,SAAS,CAAC,KAAK;gBAC5B,KAAK,MAAM,SAAS,CAAC,KAAK;YAC5B;YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/image.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var image = {\n  name: 'image',\n  keyCommand: 'image',\n  shortcuts: 'ctrlcmd+k',\n  prefix: '![image](',\n  suffix: ')',\n  buttonProps: {\n    'aria-label': 'Add image (ctrl + k)',\n    title: 'Add image (ctrl + k)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"13\",\n    height: \"13\",\n    viewBox: \"0 0 20 20\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M15 9c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm4-7H1c-.55 0-1 .45-1 1v14c0 .55.45 1 1 1h18c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zm-1 13l-6-5-2 2-4-5-4 8V4h16v11z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.includes('http') || state1.selectedText.includes('www')) {\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      newSelectionRange = selectWord({\n        text: state.text,\n        selection: state.selection,\n        prefix: '![',\n        suffix: ']()'\n      });\n      state1 = api.setSelectionRange(newSelectionRange);\n      if (state1.selectedText.length === 0) {\n        executeCommand({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '![image',\n          suffix: '](url)'\n        });\n      } else {\n        executeCommand({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '![',\n          suffix: ']()'\n        });\n      }\n    }\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,QAAQ;IACjB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,IAAI,OAAO,YAAY,CAAC,QAAQ,CAAC,WAAW,OAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ;YAC/E,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF,OAAO;YACL,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;gBAC7B,MAAM,MAAM,IAAI;gBAChB,WAAW,MAAM,SAAS;gBAC1B,QAAQ;gBACR,QAAQ;YACV;YACA,SAAS,IAAI,iBAAiB,CAAC;YAC/B,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,GAAG;gBACpC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;oBACb;oBACA,cAAc,OAAO,YAAY;oBACjC,WAAW,MAAM,SAAS;oBAC1B,QAAQ;oBACR,QAAQ;gBACV;YACF,OAAO;gBACL,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;oBACb;oBACA,cAAc,OAAO,YAAY;oBACjC,WAAW,MAAM,SAAS;oBAC1B,QAAQ;oBACR,QAAQ;gBACV;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/italic.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var italic = {\n  name: 'italic',\n  keyCommand: 'italic',\n  shortcuts: 'ctrlcmd+i',\n  prefix: '*',\n  buttonProps: {\n    'aria-label': 'Add italic text (ctrl + i)',\n    title: 'Add italic text (ctrl + i)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"italic\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 320 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M204.758 416h-33.849l62.092-320h40.725a16 16 0 0 0 15.704-12.937l6.242-32C297.599 41.184 290.034 32 279.968 32H120.235a16 16 0 0 0-15.704 12.937l-6.242 32C96.362 86.816 103.927 96 113.993 96h33.846l-62.09 320H46.278a16 16 0 0 0-15.704 12.935l-6.245 32C22.402 470.815 29.967 480 40.034 480h158.479a16 16 0 0 0 15.704-12.935l6.245-32c1.927-9.88-5.638-19.065-15.704-19.065z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    executeCommand({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/link.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var link = {\n  name: 'link',\n  keyCommand: 'link',\n  shortcuts: 'ctrlcmd+l',\n  prefix: '[',\n  suffix: '](url)',\n  buttonProps: {\n    'aria-label': 'Add a link (ctrl + l)',\n    title: 'Add a link (ctrl + l)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"italic\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M331.751196,182.121107 C392.438214,241.974735 391.605313,337.935283 332.11686,396.871226 C332.005129,396.991316 331.873084,397.121413 331.751196,397.241503 L263.493918,464.491645 C203.291404,523.80587 105.345257,523.797864 45.151885,464.491645 C-15.0506283,405.187427 -15.0506283,308.675467 45.151885,249.371249 L82.8416853,212.237562 C92.836501,202.39022 110.049118,208.9351 110.56511,222.851476 C111.223305,240.5867 114.451306,258.404985 120.407566,275.611815 C122.424812,281.438159 120.983487,287.882964 116.565047,292.23621 L103.272145,305.332975 C74.8052033,333.379887 73.9123737,379.047937 102.098973,407.369054 C130.563883,435.969378 177.350591,436.139505 206.033884,407.879434 L274.291163,340.6393 C302.9257,312.427264 302.805844,266.827265 274.291163,238.733318 C270.531934,235.036561 266.74528,232.16442 263.787465,230.157924 C259.544542,227.2873 256.928256,222.609848 256.731165,217.542518 C256.328935,206.967633 260.13184,196.070508 268.613213,187.714278 L289.998463,166.643567 C295.606326,161.118448 304.403592,160.439942 310.906317,164.911276 C318.353355,170.034591 325.328531,175.793397 331.751196,182.121107 Z M240.704978,55.4828366 L172.447607,122.733236 C172.325719,122.853326 172.193674,122.983423 172.081943,123.103513 C117.703294,179.334654 129.953294,261.569283 185.365841,328.828764 C191.044403,335.721376 198.762988,340.914712 206.209732,346.037661 C212.712465,350.509012 221.510759,349.829503 227.117615,344.305363 L248.502893,323.234572 C256.984277,314.87831 260.787188,303.981143 260.384957,293.406218 C260.187865,288.338869 257.571576,283.661398 253.328648,280.790763 C250.370829,278.78426 246.58417,275.912107 242.824936,272.215337 C214.310216,244.121282 206.209732,204.825874 229.906702,179.334654 L298.164073,112.094263 C326.847404,83.8340838 373.633159,84.0042113 402.099123,112.604645 C430.285761,140.92587 429.393946,186.594095 400.92595,214.641114 L387.63303,227.737929 C383.214584,232.091191 381.773257,238.536021 383.790506,244.362388 C389.746774,261.569283 392.974779,279.387637 393.632975,297.122928 C394.149984,311.039357 411.361608,317.584262 421.356437,307.736882 L459.046288,270.603053 C519.249898,211.29961 519.249898,114.787281 459.047304,55.4828366 C398.853851,-3.82360914 300.907572,-3.83161514 240.704978,55.4828366 Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.includes('http') || state1.selectedText.includes('www')) {\n      newSelectionRange = selectWord({\n        text: state.text,\n        selection: state.selection,\n        prefix: '[](',\n        suffix: ')'\n      });\n      state1 = api.setSelectionRange(newSelectionRange);\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: '[](',\n        suffix: ')'\n      });\n    } else {\n      if (state1.selectedText.length === 0) {\n        executeCommand({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: '[title',\n          suffix: '](url)'\n        });\n      } else {\n        executeCommand({\n          api,\n          selectedText: state1.selectedText,\n          selection: state.selection,\n          prefix: state.command.prefix,\n          suffix: state.command.suffix\n        });\n      }\n    }\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,OAAO;IAChB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,IAAI,OAAO,YAAY,CAAC,QAAQ,CAAC,WAAW,OAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ;YAC/E,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;gBAC7B,MAAM,MAAM,IAAI;gBAChB,WAAW,MAAM,SAAS;gBAC1B,QAAQ;gBACR,QAAQ;YACV;YACA,SAAS,IAAI,iBAAiB,CAAC;YAC/B,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ;gBACR,QAAQ;YACV;QACF,OAAO;YACL,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,GAAG;gBACpC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;oBACb;oBACA,cAAc,OAAO,YAAY;oBACjC,WAAW,MAAM,SAAS;oBAC1B,QAAQ;oBACR,QAAQ;gBACV;YACF,OAAO;gBACL,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;oBACb;oBACA,cAAc,OAAO,YAAY;oBACjC,WAAW,MAAM,SAAS;oBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;oBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC9B;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/list.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, getBreaksNeededForEmptyLineBefore, getBreaksNeededForEmptyLineAfter, insertBeforeEachLine } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var makeList = (state, api, insertBefore) => {\n  var newSelectionRange = selectWord({\n    text: state.text,\n    selection: state.selection,\n    prefix: state.command.prefix\n  });\n  var state1 = api.setSelectionRange(newSelectionRange);\n  var breaksBeforeCount = getBreaksNeededForEmptyLineBefore(state1.text, state1.selection.start);\n  var breaksBefore = Array(breaksBeforeCount + 1).join('\\n');\n  var breaksAfterCount = getBreaksNeededForEmptyLineAfter(state1.text, state1.selection.end);\n  var breaksAfter = Array(breaksAfterCount + 1).join('\\n');\n  var {\n    modifiedText,\n    insertionLength\n  } = insertBeforeEachLine(state1.selectedText, insertBefore);\n  if (insertionLength < 0) {\n    // Remove\n    var selectionStart = state1.selection.start;\n    var selectionEnd = state1.selection.end;\n    if (state1.selection.start > 0 && state.text.slice(state1.selection.start - 1, state1.selection.start) === '\\n') {\n      selectionStart -= 1;\n    }\n    if (state1.selection.end < state.text.length - 1 && state.text.slice(state1.selection.end, state1.selection.end + 1) === '\\n') {\n      selectionEnd += 1;\n    }\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionEnd\n    });\n    api.replaceSelection(\"\" + modifiedText);\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionStart + modifiedText.length\n    });\n  } else {\n    // Add\n    api.replaceSelection(\"\" + breaksBefore + modifiedText + breaksAfter);\n    var _selectionStart = state1.selection.start + breaksBeforeCount;\n    var _selectionEnd = _selectionStart + modifiedText.length;\n    api.setSelectionRange({\n      start: _selectionStart,\n      end: _selectionEnd\n    });\n  }\n};\nexport var unorderedListCommand = {\n  name: 'unordered-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+u',\n  prefix: '- ',\n  buttonProps: {\n    'aria-label': 'Add unordered list (ctrl + shift + u)',\n    title: 'Add unordered list (ctrl + shift + u)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"unordered-list\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M96 96c0 26.51-21.49 48-48 48S0 122.51 0 96s21.49-48 48-48 48 21.49 48 48zM48 208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm0 160c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm96-236h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, '- ');\n  }\n};\nexport var orderedListCommand = {\n  name: 'ordered-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+o',\n  prefix: '1. ',\n  buttonProps: {\n    'aria-label': 'Add ordered list (ctrl + shift + o)',\n    title: 'Add ordered list (ctrl + shift + o)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"ordered-list\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M3.263 139.527c0-7.477 3.917-11.572 11.573-11.572h15.131V88.078c0-5.163.534-10.503.534-10.503h-.356s-1.779 2.67-2.848 3.738c-4.451 4.273-10.504 4.451-15.666-1.068l-5.518-6.231c-5.342-5.341-4.984-11.216.534-16.379l21.72-19.938C32.815 33.602 36.732 32 42.785 32H54.89c7.656 0 11.749 3.916 11.749 11.572v84.384h15.488c7.655 0 11.572 4.094 11.572 11.572v8.901c0 7.477-3.917 11.572-11.572 11.572H14.836c-7.656 0-11.573-4.095-11.573-11.572v-8.902zM2.211 304.591c0-47.278 50.955-56.383 50.955-69.165 0-7.18-5.954-8.755-9.28-8.755-3.153 0-6.479 1.051-9.455 3.852-5.079 4.903-10.507 7.004-16.111 2.451l-8.579-6.829c-5.779-4.553-7.18-9.805-2.803-15.409C13.592 201.981 26.025 192 47.387 192c19.437 0 44.476 10.506 44.476 39.573 0 38.347-46.753 46.402-48.679 56.909h39.049c7.529 0 11.557 4.027 11.557 11.382v8.755c0 7.354-4.028 11.382-11.557 11.382h-67.94c-7.005 0-12.083-4.028-12.083-11.382v-4.028zM5.654 454.61l5.603-9.28c3.853-6.654 9.105-7.004 15.584-3.152 4.903 2.101 9.63 3.152 14.359 3.152 10.155 0 14.358-3.502 14.358-8.23 0-6.654-5.604-9.106-15.934-9.106h-4.728c-5.954 0-9.28-2.101-12.258-7.88l-1.05-1.926c-2.451-4.728-1.226-9.806 2.801-14.884l5.604-7.004c6.829-8.405 12.257-13.483 12.257-13.483v-.35s-4.203 1.051-12.608 1.051H16.685c-7.53 0-11.383-4.028-11.383-11.382v-8.755c0-7.53 3.853-11.382 11.383-11.382h58.484c7.529 0 11.382 4.027 11.382 11.382v3.327c0 5.778-1.401 9.806-5.079 14.183l-17.509 20.137c19.611 5.078 28.716 20.487 28.716 34.845 0 21.363-14.358 44.126-48.503 44.126-16.636 0-28.192-4.728-35.896-9.455-5.779-4.202-6.304-9.805-2.626-15.934zM144 132h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, (item, index) => index + 1 + \". \");\n  }\n};\nexport var checkedListCommand = {\n  name: 'checked-list',\n  keyCommand: 'list',\n  shortcuts: 'ctrl+shift+c',\n  prefix: '- [ ] ',\n  buttonProps: {\n    'aria-label': 'Add checked list (ctrl + shift + c)',\n    title: 'Add checked list (ctrl + shift + c)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"checked-list\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M208 132h288c8.8 0 16-7.2 16-16V76c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zM64 368c-26.5 0-48.6 21.5-48.6 48s22.1 48 48.6 48 48-21.5 48-48-21.5-48-48-48zm92.5-299l-72.2 72.2-15.6 15.6c-4.7 4.7-12.9 4.7-17.6 0L3.5 109.4c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.3c4.7-4.7 12.3-4.7 17 0l17 16.5c4.6 4.7 4.6 12.3-.1 17zm0 159.6l-72.2 72.2-15.7 15.7c-4.7 4.7-12.9 4.7-17.6 0L3.5 269c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.7c4.7-4.7 12.3-4.7 17 0l17 17c4.6 4.6 4.6 12.2-.1 16.9z\"\n    })\n  }),\n  execute: (state, api) => {\n    makeList(state, api, (item, index) => \"- [ ] \");\n  }\n};"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACO,IAAI,WAAW,CAAC,OAAO,KAAK;IACjC,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;QACjC,MAAM,MAAM,IAAI;QAChB,WAAW,MAAM,SAAS;QAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;IAC9B;IACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;IACnC,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,oCAAiC,AAAD,EAAE,OAAO,IAAI,EAAE,OAAO,SAAS,CAAC,KAAK;IAC7F,IAAI,eAAe,MAAM,oBAAoB,GAAG,IAAI,CAAC;IACrD,IAAI,mBAAmB,CAAA,GAAA,+KAAA,CAAA,mCAAgC,AAAD,EAAE,OAAO,IAAI,EAAE,OAAO,SAAS,CAAC,GAAG;IACzF,IAAI,cAAc,MAAM,mBAAmB,GAAG,IAAI,CAAC;IACnD,IAAI,EACF,YAAY,EACZ,eAAe,EAChB,GAAG,CAAA,GAAA,+KAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,YAAY,EAAE;IAC9C,IAAI,kBAAkB,GAAG;QACvB,SAAS;QACT,IAAI,iBAAiB,OAAO,SAAS,CAAC,KAAK;QAC3C,IAAI,eAAe,OAAO,SAAS,CAAC,GAAG;QACvC,IAAI,OAAO,SAAS,CAAC,KAAK,GAAG,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO,SAAS,CAAC,KAAK,MAAM,MAAM;YAC/G,kBAAkB;QACpB;QACA,IAAI,OAAO,SAAS,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG,EAAE,OAAO,SAAS,CAAC,GAAG,GAAG,OAAO,MAAM;YAC7H,gBAAgB;QAClB;QACA,IAAI,iBAAiB,CAAC;YACpB,OAAO;YACP,KAAK;QACP;QACA,IAAI,gBAAgB,CAAC,KAAK;QAC1B,IAAI,iBAAiB,CAAC;YACpB,OAAO;YACP,KAAK,iBAAiB,aAAa,MAAM;QAC3C;IACF,OAAO;QACL,MAAM;QACN,IAAI,gBAAgB,CAAC,KAAK,eAAe,eAAe;QACxD,IAAI,kBAAkB,OAAO,SAAS,CAAC,KAAK,GAAG;QAC/C,IAAI,gBAAgB,kBAAkB,aAAa,MAAM;QACzD,IAAI,iBAAiB,CAAC;YACpB,OAAO;YACP,KAAK;QACP;IACF;AACF;AACO,IAAI,uBAAuB;IAChC,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,SAAS,OAAO,KAAK;IACvB;AACF;AACO,IAAI,qBAAqB;IAC9B,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,SAAS,OAAO,KAAK,CAAC,MAAM,QAAU,QAAQ,IAAI;IACpD;AACF;AACO,IAAI,qBAAqB;IAC9B,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,SAAS,OAAO,KAAK,CAAC,MAAM,QAAU;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/preview.js"], "sourcesContent": ["import React from 'react';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var codePreview = {\n  name: 'preview',\n  keyCommand: 'preview',\n  value: 'preview',\n  shortcuts: 'ctrlcmd+9',\n  buttonProps: {\n    'aria-label': 'Preview code (ctrl + 9)',\n    title: 'Preview code (ctrl + 9)'\n  },\n  icon: /*#__PURE__*/_jsxs(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 38.023 123 38.023 398 0 397 0 449.707 91.023 450.413 91.023 72.293\"\n    }), /*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"148.023 72.293 520 71.*********** 200.023 124 200.023 397 520 396 520 449.707 148.023 450.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'preview'\n      });\n    }\n  }\n};\nexport var codeEdit = {\n  name: 'edit',\n  keyCommand: 'preview',\n  value: 'edit',\n  shortcuts: 'ctrlcmd+7',\n  buttonProps: {\n    'aria-label': 'Edit code (ctrl + 7)',\n    title: 'Edit code (ctrl + 7)'\n  },\n  icon: /*#__PURE__*/_jsxs(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 319 122 319 397 0 397 0 449.707 372 449.413 372 71.293\"\n    }), /*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"429 71.293 520 71.*********** 481 123 481 *********** 520 449.707 429 449.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'edit'\n      });\n    }\n  }\n};\nexport var codeLive = {\n  name: 'live',\n  keyCommand: 'preview',\n  value: 'live',\n  shortcuts: 'ctrlcmd+8',\n  buttonProps: {\n    'aria-label': 'Live code (ctrl + 8)',\n    title: 'Live code (ctrl + 8)'\n  },\n  icon: /*#__PURE__*/_jsxs(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: [/*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"0 71.293 0 122 179 122 179 397 0 397 0 449.707 232 449.413 232 71.293\"\n    }), /*#__PURE__*/_jsx(\"polygon\", {\n      fill: \"currentColor\",\n      points: \"289 71.293 520 71.*********** *********** *********** 520 449.***********.413\"\n    })]\n  }),\n  execute: (state, api, dispatch, executeCommandState, shortcuts) => {\n    api.textArea.focus();\n    if (shortcuts && dispatch && executeCommandState) {\n      dispatch({\n        preview: 'live'\n      });\n    }\n  }\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AACO,IAAI,cAAc;IACvB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBACtC,MAAM;gBACN,QAAQ;YACV;YAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBAC/B,MAAM;gBACN,QAAQ;YACV;SAAG;IACL;IACA,SAAS,CAAC,OAAO,KAAK,UAAU,qBAAqB;QACnD,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,aAAa,YAAY,qBAAqB;YAChD,SAAS;gBACP,SAAS;YACX;QACF;IACF;AACF;AACO,IAAI,WAAW;IACpB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBACtC,MAAM;gBACN,QAAQ;YACV;YAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBAC/B,MAAM;gBACN,QAAQ;YACV;SAAG;IACL;IACA,SAAS,CAAC,OAAO,KAAK,UAAU,qBAAqB;QACnD,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,aAAa,YAAY,qBAAqB;YAChD,SAAS;gBACP,SAAS;YACX;QACF;IACF;AACF;AACO,IAAI,WAAW;IACpB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBACtC,MAAM;gBACN,QAAQ;YACV;YAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,WAAW;gBAC/B,MAAM;gBACN,QAAQ;YACV;SAAG;IACL;IACA,SAAS,CAAC,OAAO,KAAK,UAAU,qBAAqB;QACnD,IAAI,QAAQ,CAAC,KAAK;QAClB,IAAI,aAAa,YAAY,qBAAqB;YAChD,SAAS;gBACP,SAAS;YACX;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/quote.js"], "sourcesContent": ["import React from 'react';\nimport { getBreaksNeededForEmptyLineBefore, getBreaksNeededForEmptyLineAfter, selectWord, insertBeforeEachLine } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var quote = {\n  name: 'quote',\n  keyCommand: 'quote',\n  shortcuts: 'ctrlcmd+q',\n  prefix: '> ',\n  buttonProps: {\n    'aria-label': 'Insert a quote (ctrl + q)',\n    title: 'Insert a quote (ctrl + q)'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M520,95.75 L520,225.75 C520,364.908906 457.127578,437.050625 325.040469,472.443125 C309.577578,476.586875 294.396016,464.889922 294.396016,448.881641 L294.396016,414.457031 C294.396016,404.242891 300.721328,395.025078 310.328125,391.554687 C377.356328,367.342187 414.375,349.711094 414.375,274.5 L341.25,274.5 C314.325781,274.5 292.5,252.674219 292.5,225.75 L292.5,95.75 C292.5,68.8257812 314.325781,47 341.25,47 L471.25,47 C498.174219,47 520,68.8257812 520,95.75 Z M178.75,47 L48.75,47 C21.8257813,47 0,68.8257812 0,95.75 L0,225.75 C0,252.674219 21.8257813,274.5 48.75,274.5 L121.875,274.5 C121.875,349.711094 84.8563281,367.342187 17.828125,391.554687 C8.22132813,395.025078 1.89601563,404.242891 1.89601563,414.457031 L1.89601563,448.881641 C1.89601563,464.889922 17.0775781,476.586875 32.5404687,472.443125 C164.627578,437.050625 227.5,364.908906 227.5,225.75 L227.5,95.75 C227.5,68.8257812 205.674219,47 178.75,47 Z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    var breaksBeforeCount = getBreaksNeededForEmptyLineBefore(state1.text, state1.selection.start);\n    var breaksBefore = Array(breaksBeforeCount + 1).join('\\n');\n    var breaksAfterCount = getBreaksNeededForEmptyLineAfter(state1.text, state1.selection.end);\n    var breaksAfter = Array(breaksAfterCount + 1).join('\\n');\n    var modifiedText = insertBeforeEachLine(state1.selectedText, state.command.prefix);\n    api.replaceSelection(\"\" + breaksBefore + modifiedText.modifiedText + breaksAfter);\n    var selectionStart = state1.selection.start + breaksBeforeCount;\n    var selectionEnd = selectionStart + modifiedText.modifiedText.length;\n    api.setSelectionRange({\n      start: selectionStart,\n      end: selectionEnd\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,QAAQ;IACjB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,oCAAiC,AAAD,EAAE,OAAO,IAAI,EAAE,OAAO,SAAS,CAAC,KAAK;QAC7F,IAAI,eAAe,MAAM,oBAAoB,GAAG,IAAI,CAAC;QACrD,IAAI,mBAAmB,CAAA,GAAA,+KAAA,CAAA,mCAAgC,AAAD,EAAE,OAAO,IAAI,EAAE,OAAO,SAAS,CAAC,GAAG;QACzF,IAAI,cAAc,MAAM,mBAAmB,GAAG,IAAI,CAAC;QACnD,IAAI,eAAe,CAAA,GAAA,+KAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,YAAY,EAAE,MAAM,OAAO,CAAC,MAAM;QACjF,IAAI,gBAAgB,CAAC,KAAK,eAAe,aAAa,YAAY,GAAG;QACrE,IAAI,iBAAiB,OAAO,SAAS,CAAC,KAAK,GAAG;QAC9C,IAAI,eAAe,iBAAiB,aAAa,YAAY,CAAC,MAAM;QACpE,IAAI,iBAAiB,CAAC;YACpB,OAAO;YACP,KAAK;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/strikeThrough.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var strikethrough = {\n  name: 'strikethrough',\n  keyCommand: 'strikethrough',\n  shortcuts: 'ctrl+shift+x',\n  buttonProps: {\n    'aria-label': 'Add strikethrough text (ctrl + shift + x)',\n    title: 'Add strikethrough text (ctrl + shift + x)'\n  },\n  prefix: '~~',\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    \"data-name\": \"strikethrough\",\n    width: \"12\",\n    height: \"12\",\n    role: \"img\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M496 288H16c-8.837 0-16-7.163-16-16v-32c0-8.837 7.163-16 16-16h480c8.837 0 16 7.163 16 16v32c0 8.837-7.163 16-16 16zm-214.666 16c27.258 12.937 46.524 28.683 46.524 56.243 0 33.108-28.977 53.676-75.621 53.676-32.325 0-76.874-12.08-76.874-44.271V368c0-8.837-7.164-16-16-16H113.75c-8.836 0-16 7.163-16 16v19.204c0 66.845 77.717 101.82 154.487 101.82 88.578 0 162.013-45.438 162.013-134.424 0-19.815-3.618-36.417-10.143-50.6H281.334zm-30.952-96c-32.422-13.505-56.836-28.946-56.836-59.683 0-33.92 30.901-47.406 64.962-47.406 42.647 0 64.962 16.593 64.962 32.985V136c0 8.837 7.164 16 16 16h45.613c8.836 0 16-7.163 16-16v-30.318c0-52.438-71.725-79.875-142.575-79.875-85.203 0-150.726 40.972-150.726 125.646 0 22.71 4.665 41.176 12.777 56.547h129.823z\"\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    executeCommand({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,gBAAgB;IACzB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,QAAQ;IACR,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,aAAa;QACb,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title1.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title1 = {\n  name: 'title1',\n  keyCommand: 'title1',\n  shortcuts: 'ctrlcmd+1',\n  prefix: '# ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title1 (ctrl + 1)',\n    title: 'Insert title1 (ctrl + 1)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 18,\n      textAlign: 'left'\n    },\n    children: \"Title 1\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React from 'react';\nimport { title1 } from './title1';\nimport { selectLine, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function titleExecute(_ref) {\n  var {\n    state,\n    api,\n    prefix,\n    suffix = prefix\n  } = _ref;\n  var newSelectionRange = selectLine({\n    text: state.text,\n    selection: state.selection\n  });\n  var state1 = api.setSelectionRange(newSelectionRange);\n  executeCommand({\n    api,\n    selectedText: state1.selectedText,\n    selection: state.selection,\n    prefix,\n    suffix\n  });\n}\nexport var title = _extends({}, title1, {\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 520 520\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z\"\n    })\n  })\n});"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,aAAa,IAAI;IAC/B,IAAI,EACF,KAAK,EACL,GAAG,EACH,MAAM,EACN,SAAS,MAAM,EAChB,GAAG;IACJ,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;QACjC,MAAM,MAAM,IAAI;QAChB,WAAW,MAAM,SAAS;IAC5B;IACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;IACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;QACb;QACA,cAAc,OAAO,YAAY;QACjC,WAAW,MAAM,SAAS;QAC1B;QACA;IACF;AACF;AACO,IAAI,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,2KAAA,CAAA,SAAM,EAAE;IACtC,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QACL;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title2.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title2 = {\n  name: 'title2',\n  keyCommand: 'title2',\n  shortcuts: 'ctrlcmd+2',\n  prefix: '## ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title2 (ctrl + 2)',\n    title: 'Insert title2 (ctrl + 2)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 16,\n      textAlign: 'left'\n    },\n    children: \"Title 2\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title3.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title3 = {\n  name: 'title3',\n  keyCommand: 'title3',\n  shortcuts: 'ctrlcmd+3',\n  prefix: '### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title3 (ctrl + 3)',\n    title: 'Insert title3 (ctrl + 3)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 15,\n      textAlign: 'left'\n    },\n    children: \"Title 3\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title4.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title4 = {\n  name: 'title4',\n  keyCommand: 'title4',\n  shortcuts: 'ctrlcmd+4',\n  prefix: '#### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title4 (ctrl + 4)',\n    title: 'Insert title4 (ctrl + 4)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 14,\n      textAlign: 'left'\n    },\n    children: \"Title 4\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title5.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title5 = {\n  name: 'title5',\n  keyCommand: 'title5',\n  shortcuts: 'ctrlcmd+5',\n  prefix: '##### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title5 (ctrl + 5)',\n    title: 'Insert title5 (ctrl + 5)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 12,\n      textAlign: 'left'\n    },\n    children: \"Title 5\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1654, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/title6.js"], "sourcesContent": ["import React from 'react';\nimport { titleExecute } from '../commands/title';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var title6 = {\n  name: 'title6',\n  keyCommand: 'title6',\n  shortcuts: 'ctrlcmd+6',\n  prefix: '###### ',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Insert title6 (ctrl + 6)',\n    title: 'Insert title6 (ctrl + 6)'\n  },\n  icon: /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      fontSize: 12,\n      textAlign: 'left'\n    },\n    children: \"Title 6\"\n  }),\n  execute: (state, api) => {\n    titleExecute({\n      state,\n      api,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,SAAS;IAClB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,OAAO;YACL,UAAU;YACV,WAAW;QACb;QACA,UAAU;IACZ;IACA,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;YACX;YACA;YACA,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/table.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var table = {\n  name: 'table',\n  keyCommand: 'table',\n  prefix: '\\n| Header | Header |\\n|--------|--------|\\n| Cell | Cell |\\n| Cell | Cell |\\n| Cell | Cell |\\n\\n',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Add table',\n    title: 'Add table'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 512 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M64 256V160H224v96H64zm0 64H224v96H64V320zm224 96V320H448v96H288zM448 256H288V160H448v96zM64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z\"\n      //Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    if (state1.selectedText.length >= state.command.prefix.length + state.command.suffix.length && state1.selectedText.startsWith(state.command.prefix)) {\n      // Remove\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    } else {\n      // Add\n      state1 = api.setSelectionRange({\n        start: state.selection.start,\n        end: state.selection.start\n      });\n      executeCommand({\n        api,\n        selectedText: state1.selectedText,\n        selection: state.selection,\n        prefix: state.command.prefix,\n        suffix: state.command.suffix\n      });\n    }\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,QAAQ;IACjB,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,MAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QAEL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,IAAI,OAAO,YAAY,CAAC,MAAM,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,MAAM,GAAG;YACnJ,SAAS;YACT,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF,OAAO;YACL,MAAM;YACN,SAAS,IAAI,iBAAiB,CAAC;gBAC7B,OAAO,MAAM,SAAS,CAAC,KAAK;gBAC5B,KAAK,MAAM,SAAS,CAAC,KAAK;YAC5B;YACA,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;gBACb;gBACA,cAAc,OAAO,YAAY;gBACjC,WAAW,MAAM,SAAS;gBAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;gBAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC9B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/issue.js"], "sourcesContent": ["import React from 'react';\nimport { selectWord, executeCommand } from '../utils/markdownUtils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var issue = {\n  name: 'issue',\n  keyCommand: 'issue',\n  prefix: '#',\n  suffix: '',\n  buttonProps: {\n    'aria-label': 'Add issue',\n    title: 'Add issue'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    role: \"img\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 448 512\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M181.3 32.4c17.4 2.9 29.2 19.4 26.3 36.8L197.8 128l95.1 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3s29.2 19.4 26.3 36.8L357.8 128l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0L325.8 320l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8l9.8-58.7-95.1 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8L90.2 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 21.3-128L64 192c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3zM187.1 192L165.8 320l95.1 0 21.3-128-95.1 0z\"\n      //Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com\n    })\n  }),\n  execute: (state, api) => {\n    var newSelectionRange = selectWord({\n      text: state.text,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n    var state1 = api.setSelectionRange(newSelectionRange);\n    executeCommand({\n      api,\n      selectedText: state1.selectedText,\n      selection: state.selection,\n      prefix: state.command.prefix,\n      suffix: state.command.suffix\n    });\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,QAAQ;IACjB,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,MAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,MAAM;YACN,GAAG;QAEL;IACF;IACA,SAAS,CAAC,OAAO;QACf,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,MAAM,IAAI;YAChB,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;QACA,IAAI,SAAS,IAAI,iBAAiB,CAAC;QACnC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE;YACb;YACA,cAAc,OAAO,YAAY;YACjC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,OAAO,CAAC,MAAM;YAC5B,QAAQ,MAAM,OAAO,CAAC,MAAM;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/help.js"], "sourcesContent": ["import React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var help = {\n  name: 'help',\n  keyCommand: 'help',\n  buttonProps: {\n    'aria-label': 'Open help',\n    title: 'Open help'\n  },\n  icon: /*#__PURE__*/_jsx(\"svg\", {\n    viewBox: \"0 0 16 16\",\n    width: \"12px\",\n    height: \"12px\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      d: \"M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8Zm.9 13H7v-1.8h1.9V13Zm-.1-3.6v.5H7.1v-.6c.2-2.1 2-1.9 1.9-3.2.1-.7-.3-1.1-1-1.1-.8 0-1.2.7-1.2 1.6H5c0-1.7 1.2-3 2.9-3 2.3 0 3 1.4 3 2.3.1 2.3-1.9 2-2.1 3.5Z\",\n      fill: \"currentColor\"\n    })\n  }),\n  execute: () => {\n    window.open('https://www.markdownguide.org/basic-syntax/', '_blank', 'noreferrer');\n  }\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,IAAI,OAAO;IAChB,MAAM;IACN,YAAY;IACZ,aAAa;QACX,cAAc;QACd,OAAO;IACT;IACA,MAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,SAAS;QACT,OAAO;QACP,QAAQ;QACR,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YAClC,GAAG;YACH,MAAM;QACR;IACF;IACA,SAAS;QACP,OAAO,IAAI,CAAC,+CAA+C,UAAU;IACvE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/commands/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport { insertTextAtPosition } from '../utils/InsertTextAtPosition';\nimport { bold } from './bold';\nimport { code, codeBlock } from './code';\nimport { comment } from './comment';\nimport { divider } from './divider';\nimport { fullscreen } from './fullscreen';\nimport { group } from './group';\nimport { hr } from './hr';\nimport { image } from './image';\nimport { italic } from './italic';\nimport { link } from './link';\nimport { checkedListCommand, orderedListCommand, unorderedListCommand } from './list';\nimport { codeEdit, codeLive, codePreview } from './preview';\nimport { quote } from './quote';\nimport { strikethrough } from './strikeThrough';\nimport { title } from './title';\nimport { title1 } from './title1';\nimport { title2 } from './title2';\nimport { title3 } from './title3';\nimport { title4 } from './title4';\nimport { title5 } from './title5';\nimport { title6 } from './title6';\nimport { table } from './table';\nimport { issue } from './issue';\nimport { help } from './help';\nvar getCommands = () => [bold, italic, strikethrough, hr, group([title1, title2, title3, title4, title5, title6], {\n  name: 'title',\n  groupName: 'title',\n  buttonProps: {\n    'aria-label': 'Insert title',\n    title: 'Insert title'\n  }\n}), divider, link, quote, code, codeBlock, comment, image, table, divider, unorderedListCommand, orderedListCommand, checkedListCommand, divider, help];\nvar getExtraCommands = () => [codeEdit, codeLive, codePreview, divider, fullscreen];\nfunction getStateFromTextArea(textArea) {\n  var _textArea$value;\n  return {\n    selection: {\n      start: textArea.selectionStart,\n      end: textArea.selectionEnd\n    },\n    text: textArea.value,\n    selectedText: (_textArea$value = textArea.value) == null ? void 0 : _textArea$value.slice(textArea.selectionStart, textArea.selectionEnd)\n  };\n}\nclass TextAreaTextApi {\n  constructor(textArea) {\n    this.textArea = void 0;\n    this.textArea = textArea;\n  }\n\n  /**\n   * Replaces the current selection with the new text. This will make the new selectedText to be empty, the\n   * selection start and selection end will be the same and will both point to the end\n   * @param text Text that should replace the current selection\n   */\n  replaceSelection(text) {\n    insertTextAtPosition(this.textArea, text);\n    return getStateFromTextArea(this.textArea);\n  }\n\n  /**\n   * Selects the specified text range\n   * @param selection\n   */\n  setSelectionRange(selection) {\n    this.textArea.focus();\n    this.textArea.selectionStart = selection.start;\n    this.textArea.selectionEnd = selection.end;\n    return getStateFromTextArea(this.textArea);\n  }\n}\nclass TextAreaCommandOrchestrator {\n  constructor(textArea) {\n    this.textArea = void 0;\n    this.textApi = void 0;\n    this.textArea = textArea;\n    this.textApi = new TextAreaTextApi(textArea);\n  }\n  getState() {\n    if (!this.textArea) return false;\n    return getStateFromTextArea(this.textArea);\n  }\n  executeCommand(command, dispatch, state, shortcuts) {\n    command.execute && command.execute(_extends({\n      command\n    }, getStateFromTextArea(this.textArea)), this.textApi, dispatch, state, shortcuts);\n  }\n}\nexport { title, title1, title2, title3, title4, title5, title6, bold, codeBlock, comment, italic, strikethrough, hr, group, divider, link, quote, code, image, unorderedListCommand, orderedListCommand, checkedListCommand, table, issue, help, codeEdit, codeLive, codePreview, fullscreen,\n// Tool method.\ngetCommands, getExtraCommands, getStateFromTextArea, TextAreaCommandOrchestrator, TextAreaTextApi };"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,cAAc,IAAM;QAAC,yKAAA,CAAA,OAAI;QAAE,2KAAA,CAAA,SAAM;QAAE,kLAAA,CAAA,gBAAa;QAAE,uKAAA,CAAA,KAAE;QAAE,CAAA,GAAA,0KAAA,CAAA,QAAK,AAAD,EAAE;YAAC,2KAAA,CAAA,SAAM;YAAE,2KAAA,CAAA,SAAM;YAAE,2KAAA,CAAA,SAAM;YAAE,2KAAA,CAAA,SAAM;YAAE,2KAAA,CAAA,SAAM;YAAE,2KAAA,CAAA,SAAM;SAAC,EAAE;YAChH,MAAM;YACN,WAAW;YACX,aAAa;gBACX,cAAc;gBACd,OAAO;YACT;QACF;QAAI,4KAAA,CAAA,UAAO;QAAE,yKAAA,CAAA,OAAI;QAAE,0KAAA,CAAA,QAAK;QAAE,yKAAA,CAAA,OAAI;QAAE,yKAAA,CAAA,YAAS;QAAE,4KAAA,CAAA,UAAO;QAAE,0KAAA,CAAA,QAAK;QAAE,0KAAA,CAAA,QAAK;QAAE,4KAAA,CAAA,UAAO;QAAE,yKAAA,CAAA,uBAAoB;QAAE,yKAAA,CAAA,qBAAkB;QAAE,yKAAA,CAAA,qBAAkB;QAAE,4KAAA,CAAA,UAAO;QAAE,yKAAA,CAAA,OAAI;KAAC;AACvJ,IAAI,mBAAmB,IAAM;QAAC,4KAAA,CAAA,WAAQ;QAAE,4KAAA,CAAA,WAAQ;QAAE,4KAAA,CAAA,cAAW;QAAE,4KAAA,CAAA,UAAO;QAAE,+KAAA,CAAA,aAAU;KAAC;AACnF,SAAS,qBAAqB,QAAQ;IACpC,IAAI;IACJ,OAAO;QACL,WAAW;YACT,OAAO,SAAS,cAAc;YAC9B,KAAK,SAAS,YAAY;QAC5B;QACA,MAAM,SAAS,KAAK;QACpB,cAAc,CAAC,kBAAkB,SAAS,KAAK,KAAK,OAAO,KAAK,IAAI,gBAAgB,KAAK,CAAC,SAAS,cAAc,EAAE,SAAS,YAAY;IAC1I;AACF;AACA,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA;;;;GAIC,GACD,iBAAiB,IAAI,EAAE;QACrB,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAE;QACpC,OAAO,qBAAqB,IAAI,CAAC,QAAQ;IAC3C;IAEA;;;GAGC,GACD,kBAAkB,SAAS,EAAE;QAC3B,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnB,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,UAAU,KAAK;QAC9C,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,UAAU,GAAG;QAC1C,OAAO,qBAAqB,IAAI,CAAC,QAAQ;IAC3C;AACF;AACA,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,gBAAgB;IACrC;IACA,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,OAAO,qBAAqB,IAAI,CAAC,QAAQ;IAC3C;IACA,eAAe,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;QAClD,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;YAC1C;QACF,GAAG,qBAAqB,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,OAAO;IAC1E;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2039, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/TextArea/handleKeyDown.js"], "sourcesContent": ["import { insertTextAtPosition } from '../../utils/InsertTextAtPosition';\nimport { insertBeforeEachLine, selectLine } from '../../utils/markdownUtils';\nimport { TextAreaTextApi } from '../../commands';\n\n/**\n * - `13` - `Enter`\n * - `9` - `Tab`\n */\nfunction stopPropagation(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}\nfunction handleLineMove(e, direction) {\n  stopPropagation(e);\n  var target = e.target;\n  var textArea = new TextAreaTextApi(target);\n  var selection = {\n    start: target.selectionStart,\n    end: target.selectionEnd\n  };\n  selection = selectLine({\n    text: target.value,\n    selection\n  });\n  if (direction < 0 && selection.start <= 0 || direction > 0 && selection.end >= target.value.length) {\n    return;\n  }\n  var blockText = target.value.slice(selection.start, selection.end);\n  if (direction < 0) {\n    var prevLineSelection = selectLine({\n      text: target.value,\n      selection: {\n        start: selection.start - 1,\n        end: selection.start - 1\n      }\n    });\n    var prevLineText = target.value.slice(prevLineSelection.start, prevLineSelection.end);\n    textArea.setSelectionRange({\n      start: prevLineSelection.start,\n      end: selection.end\n    });\n    insertTextAtPosition(target, blockText + \"\\n\" + prevLineText);\n    textArea.setSelectionRange({\n      start: prevLineSelection.start,\n      end: prevLineSelection.start + blockText.length\n    });\n  } else {\n    var nextLineSelection = selectLine({\n      text: target.value,\n      selection: {\n        start: selection.end + 1,\n        end: selection.end + 1\n      }\n    });\n    var nextLineText = target.value.slice(nextLineSelection.start, nextLineSelection.end);\n    textArea.setSelectionRange({\n      start: selection.start,\n      end: nextLineSelection.end\n    });\n    insertTextAtPosition(target, nextLineText + \"\\n\" + blockText);\n    textArea.setSelectionRange({\n      start: nextLineSelection.end - blockText.length,\n      end: nextLineSelection.end\n    });\n  }\n}\nexport default function handleKeyDown(e, tabSize, defaultTabEnable) {\n  if (tabSize === void 0) {\n    tabSize = 2;\n  }\n  if (defaultTabEnable === void 0) {\n    defaultTabEnable = false;\n  }\n  var target = e.target;\n  var starVal = target.value.substr(0, target.selectionStart);\n  var valArr = starVal.split('\\n');\n  var currentLineStr = valArr[valArr.length - 1];\n  var textArea = new TextAreaTextApi(target);\n\n  /**\n   * `9` - `Tab`\n   */\n  if (!defaultTabEnable && e.code && e.code.toLowerCase() === 'tab') {\n    stopPropagation(e);\n    var space = new Array(tabSize + 1).join('  ');\n    if (target.selectionStart !== target.selectionEnd) {\n      var _star = target.value.substring(0, target.selectionStart).split('\\n');\n      var _end = target.value.substring(0, target.selectionEnd).split('\\n');\n      var modifiedTextLine = [];\n      _end.forEach((item, idx) => {\n        if (item !== _star[idx]) {\n          modifiedTextLine.push(item);\n        }\n      });\n      var modifiedText = modifiedTextLine.join('\\n');\n      var oldSelectText = target.value.substring(target.selectionStart, target.selectionEnd);\n      var newStarNum = target.value.substring(0, target.selectionStart).length;\n      textArea.setSelectionRange({\n        start: target.value.indexOf(modifiedText),\n        end: target.selectionEnd\n      });\n      var modifiedTextObj = insertBeforeEachLine(modifiedText, e.shiftKey ? '' : space);\n      var text = modifiedTextObj.modifiedText;\n      if (e.shiftKey) {\n        text = text.split('\\n').map(item => item.replace(new RegExp(\"^\" + space), '')).join('\\n');\n      }\n      textArea.replaceSelection(text);\n      var startTabSize = e.shiftKey ? -tabSize : tabSize;\n      var endTabSize = e.shiftKey ? -modifiedTextLine.length * tabSize : modifiedTextLine.length * tabSize;\n      textArea.setSelectionRange({\n        start: newStarNum + startTabSize,\n        end: newStarNum + oldSelectText.length + endTabSize\n      });\n    } else {\n      return insertTextAtPosition(target, space);\n    }\n  } else if (e.keyCode === 13 && e.code.toLowerCase() === 'enter' && (/^(-|\\*)\\s/.test(currentLineStr) || /^\\d+.\\s/.test(currentLineStr)) && !e.shiftKey) {\n    /**\n     * `13` - `Enter`\n     */\n    stopPropagation(e);\n    var startStr = '\\n- ';\n    if (currentLineStr.startsWith('*')) {\n      startStr = '\\n* ';\n    }\n    if (currentLineStr.startsWith('- [ ]') || currentLineStr.startsWith('- [X]') || currentLineStr.startsWith('- [x]')) {\n      startStr = '\\n- [ ] ';\n    }\n    if (/^\\d+.\\s/.test(currentLineStr)) {\n      startStr = \"\\n\" + (parseInt(currentLineStr) + 1) + \". \";\n    }\n    return insertTextAtPosition(target, startStr);\n  } else if (e.code && e.code.toLowerCase() === 'keyd' && e.ctrlKey) {\n    // Duplicate lines\n    stopPropagation(e);\n    var selection = {\n      start: target.selectionStart,\n      end: target.selectionEnd\n    };\n    var savedSelection = selection;\n    selection = selectLine({\n      text: target.value,\n      selection\n    });\n    var textToDuplicate = target.value.slice(selection.start, selection.end);\n    textArea.setSelectionRange({\n      start: selection.end,\n      end: selection.end\n    });\n    insertTextAtPosition(target, \"\\n\" + textToDuplicate);\n    textArea.setSelectionRange({\n      start: savedSelection.start,\n      end: savedSelection.end\n    });\n  } else if (e.code && e.code.toLowerCase() === 'arrowup' && e.altKey) {\n    handleLineMove(e, -1);\n  } else if (e.code && e.code.toLowerCase() === 'arrowdown' && e.altKey) {\n    handleLineMove(e, 1);\n  }\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEA;;;CAGC,GACD,SAAS,gBAAgB,CAAC;IACxB,EAAE,eAAe;IACjB,EAAE,cAAc;AAClB;AACA,SAAS,eAAe,CAAC,EAAE,SAAS;IAClC,gBAAgB;IAChB,IAAI,SAAS,EAAE,MAAM;IACrB,IAAI,WAAW,IAAI,0LAAA,CAAA,kBAAe,CAAC;IACnC,IAAI,YAAY;QACd,OAAO,OAAO,cAAc;QAC5B,KAAK,OAAO,YAAY;IAC1B;IACA,YAAY,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;QACrB,MAAM,OAAO,KAAK;QAClB;IACF;IACA,IAAI,YAAY,KAAK,UAAU,KAAK,IAAI,KAAK,YAAY,KAAK,UAAU,GAAG,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE;QAClG;IACF;IACA,IAAI,YAAY,OAAO,KAAK,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE,UAAU,GAAG;IACjE,IAAI,YAAY,GAAG;QACjB,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,OAAO,KAAK;YAClB,WAAW;gBACT,OAAO,UAAU,KAAK,GAAG;gBACzB,KAAK,UAAU,KAAK,GAAG;YACzB;QACF;QACA,IAAI,eAAe,OAAO,KAAK,CAAC,KAAK,CAAC,kBAAkB,KAAK,EAAE,kBAAkB,GAAG;QACpF,SAAS,iBAAiB,CAAC;YACzB,OAAO,kBAAkB,KAAK;YAC9B,KAAK,UAAU,GAAG;QACpB;QACA,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,YAAY,OAAO;QAChD,SAAS,iBAAiB,CAAC;YACzB,OAAO,kBAAkB,KAAK;YAC9B,KAAK,kBAAkB,KAAK,GAAG,UAAU,MAAM;QACjD;IACF,OAAO;QACL,IAAI,oBAAoB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACjC,MAAM,OAAO,KAAK;YAClB,WAAW;gBACT,OAAO,UAAU,GAAG,GAAG;gBACvB,KAAK,UAAU,GAAG,GAAG;YACvB;QACF;QACA,IAAI,eAAe,OAAO,KAAK,CAAC,KAAK,CAAC,kBAAkB,KAAK,EAAE,kBAAkB,GAAG;QACpF,SAAS,iBAAiB,CAAC;YACzB,OAAO,UAAU,KAAK;YACtB,KAAK,kBAAkB,GAAG;QAC5B;QACA,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,eAAe,OAAO;QACnD,SAAS,iBAAiB,CAAC;YACzB,OAAO,kBAAkB,GAAG,GAAG,UAAU,MAAM;YAC/C,KAAK,kBAAkB,GAAG;QAC5B;IACF;AACF;AACe,SAAS,cAAc,CAAC,EAAE,OAAO,EAAE,gBAAgB;IAChE,IAAI,YAAY,KAAK,GAAG;QACtB,UAAU;IACZ;IACA,IAAI,qBAAqB,KAAK,GAAG;QAC/B,mBAAmB;IACrB;IACA,IAAI,SAAS,EAAE,MAAM;IACrB,IAAI,UAAU,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,cAAc;IAC1D,IAAI,SAAS,QAAQ,KAAK,CAAC;IAC3B,IAAI,iBAAiB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;IAC9C,IAAI,WAAW,IAAI,0LAAA,CAAA,kBAAe,CAAC;IAEnC;;GAEC,GACD,IAAI,CAAC,oBAAoB,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,WAAW,OAAO,OAAO;QACjE,gBAAgB;QAChB,IAAI,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC;QACxC,IAAI,OAAO,cAAc,KAAK,OAAO,YAAY,EAAE;YACjD,IAAI,QAAQ,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,cAAc,EAAE,KAAK,CAAC;YACnE,IAAI,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,YAAY,EAAE,KAAK,CAAC;YAChE,IAAI,mBAAmB,EAAE;YACzB,KAAK,OAAO,CAAC,CAAC,MAAM;gBAClB,IAAI,SAAS,KAAK,CAAC,IAAI,EAAE;oBACvB,iBAAiB,IAAI,CAAC;gBACxB;YACF;YACA,IAAI,eAAe,iBAAiB,IAAI,CAAC;YACzC,IAAI,gBAAgB,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO,cAAc,EAAE,OAAO,YAAY;YACrF,IAAI,aAAa,OAAO,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,cAAc,EAAE,MAAM;YACxE,SAAS,iBAAiB,CAAC;gBACzB,OAAO,OAAO,KAAK,CAAC,OAAO,CAAC;gBAC5B,KAAK,OAAO,YAAY;YAC1B;YACA,IAAI,kBAAkB,CAAA,GAAA,+KAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc,EAAE,QAAQ,GAAG,KAAK;YAC3E,IAAI,OAAO,gBAAgB,YAAY;YACvC,IAAI,EAAE,QAAQ,EAAE;gBACd,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,IAAI,OAAO,MAAM,QAAQ,KAAK,IAAI,CAAC;YACtF;YACA,SAAS,gBAAgB,CAAC;YAC1B,IAAI,eAAe,EAAE,QAAQ,GAAG,CAAC,UAAU;YAC3C,IAAI,aAAa,EAAE,QAAQ,GAAG,CAAC,iBAAiB,MAAM,GAAG,UAAU,iBAAiB,MAAM,GAAG;YAC7F,SAAS,iBAAiB,CAAC;gBACzB,OAAO,aAAa;gBACpB,KAAK,aAAa,cAAc,MAAM,GAAG;YAC3C;QACF,OAAO;YACL,OAAO,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;QACtC;IACF,OAAO,IAAI,EAAE,OAAO,KAAK,MAAM,EAAE,IAAI,CAAC,WAAW,OAAO,WAAW,CAAC,YAAY,IAAI,CAAC,mBAAmB,UAAU,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,QAAQ,EAAE;QACtJ;;KAEC,GACD,gBAAgB;QAChB,IAAI,WAAW;QACf,IAAI,eAAe,UAAU,CAAC,MAAM;YAClC,WAAW;QACb;QACA,IAAI,eAAe,UAAU,CAAC,YAAY,eAAe,UAAU,CAAC,YAAY,eAAe,UAAU,CAAC,UAAU;YAClH,WAAW;QACb;QACA,IAAI,UAAU,IAAI,CAAC,iBAAiB;YAClC,WAAW,OAAO,CAAC,SAAS,kBAAkB,CAAC,IAAI;QACrD;QACA,OAAO,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;IACtC,OAAO,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,WAAW,OAAO,UAAU,EAAE,OAAO,EAAE;QACjE,kBAAkB;QAClB,gBAAgB;QAChB,IAAI,YAAY;YACd,OAAO,OAAO,cAAc;YAC5B,KAAK,OAAO,YAAY;QAC1B;QACA,IAAI,iBAAiB;QACrB,YAAY,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE;YACrB,MAAM,OAAO,KAAK;YAClB;QACF;QACA,IAAI,kBAAkB,OAAO,KAAK,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE,UAAU,GAAG;QACvE,SAAS,iBAAiB,CAAC;YACzB,OAAO,UAAU,GAAG;YACpB,KAAK,UAAU,GAAG;QACpB;QACA,CAAA,GAAA,sLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,OAAO;QACpC,SAAS,iBAAiB,CAAC;YACzB,OAAO,eAAe,KAAK;YAC3B,KAAK,eAAe,GAAG;QACzB;IACF,OAAO,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,WAAW,OAAO,aAAa,EAAE,MAAM,EAAE;QACnE,eAAe,GAAG,CAAC;IACrB,OAAO,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,WAAW,OAAO,eAAe,EAAE,MAAM,EAAE;QACrE,eAAe,GAAG;IACpB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/TextArea/Textarea.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"prefixCls\", \"onChange\"],\n  _excluded2 = [\"markdown\", \"commands\", \"fullscreen\", \"preview\", \"highlightEnable\", \"extraCommands\", \"tabSize\", \"defaultTabEnable\", \"dispatch\"];\nimport React, { useContext, useEffect } from 'react';\nimport { EditorContext } from '../../Context';\nimport { TextAreaCommandOrchestrator } from '../../commands';\nimport handleKeyDown from './handleKeyDown';\nimport shortcuts from './shortcuts';\nimport \"./index.css\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function Textarea(props) {\n  var {\n      prefixCls,\n      onChange: _onChange\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  var _useContext = useContext(EditorContext),\n    {\n      markdown,\n      commands,\n      fullscreen,\n      preview,\n      highlightEnable,\n      extraCommands,\n      tabSize,\n      defaultTabEnable,\n      dispatch\n    } = _useContext,\n    otherStore = _objectWithoutPropertiesLoose(_useContext, _excluded2);\n  var textRef = React.useRef(null);\n  var executeRef = React.useRef();\n  var statesRef = React.useRef({\n    fullscreen,\n    preview\n  });\n  useEffect(() => {\n    statesRef.current = {\n      fullscreen,\n      preview,\n      highlightEnable\n    };\n  }, [fullscreen, preview, highlightEnable]);\n  useEffect(() => {\n    if (textRef.current && dispatch) {\n      var commandOrchestrator = new TextAreaCommandOrchestrator(textRef.current);\n      executeRef.current = commandOrchestrator;\n      dispatch({\n        textarea: textRef.current,\n        commandOrchestrator\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var onKeyDown = e => {\n    handleKeyDown(e, tabSize, defaultTabEnable);\n    shortcuts(e, [...(commands || []), ...(extraCommands || [])], executeRef.current, dispatch, statesRef.current);\n  };\n  useEffect(() => {\n    if (textRef.current) {\n      textRef.current.addEventListener('keydown', onKeyDown);\n    }\n    return () => {\n      if (textRef.current) {\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        textRef.current.removeEventListener('keydown', onKeyDown);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsx(\"textarea\", _extends({\n    autoComplete: \"off\",\n    autoCorrect: \"off\",\n    autoCapitalize: \"off\",\n    spellCheck: false\n  }, other, {\n    ref: textRef,\n    className: prefixCls + \"-text-input \" + (other.className ? other.className : ''),\n    value: markdown,\n    onChange: e => {\n      dispatch && dispatch({\n        markdown: e.target.value\n      });\n      _onChange && _onChange(e);\n    }\n  }));\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AAEA;;;AARA,IAAI,YAAY;IAAC;IAAa;CAAW,EACvC,aAAa;IAAC;IAAY;IAAY;IAAc;IAAW;IAAmB;IAAiB;IAAW;IAAoB;CAAW;;;;;;;;AAQhI,SAAS,SAAS,KAAK;IACpC,IAAI,EACA,SAAS,EACT,UAAU,SAAS,EACpB,GAAG,OACJ,QAAQ,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;IAC/C,IAAI,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa,GACxC,EACE,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,OAAO,EACP,eAAe,EACf,aAAa,EACb,OAAO,EACP,gBAAgB,EAChB,QAAQ,EACT,GAAG,aACJ,aAAa,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,aAAa;IAC1D,IAAI,UAAU,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,IAAI,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM;IAC7B,IAAI,YAAY,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAC3B;QACA;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU,OAAO,GAAG;YAClB;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAY;QAAS;KAAgB;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,IAAI,UAAU;YAC/B,IAAI,sBAAsB,IAAI,0LAAA,CAAA,8BAA2B,CAAC,QAAQ,OAAO;YACzE,WAAW,OAAO,GAAG;YACrB,SAAS;gBACP,UAAU,QAAQ,OAAO;gBACzB;YACF;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,IAAI,YAAY,CAAA;QACd,CAAA,GAAA,gMAAA,CAAA,UAAa,AAAD,EAAE,GAAG,SAAS;QAC1B,CAAA,GAAA,4LAAA,CAAA,UAAS,AAAD,EAAE,GAAG;eAAK,YAAY,EAAE;eAAO,iBAAiB,EAAE;SAAE,EAAE,WAAW,OAAO,EAAE,UAAU,UAAU,OAAO;IAC/G;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,gBAAgB,CAAC,WAAW;QAC9C;QACA,OAAO;YACL,IAAI,QAAQ,OAAO,EAAE;gBACnB,uDAAuD;gBACvD,QAAQ,OAAO,CAAC,mBAAmB,CAAC,WAAW;YACjD;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;QAC5C,cAAc;QACd,aAAa;QACb,gBAAgB;QAChB,YAAY;IACd,GAAG,OAAO;QACR,KAAK;QACL,WAAW,YAAY,iBAAiB,CAAC,MAAM,SAAS,GAAG,MAAM,SAAS,GAAG,EAAE;QAC/E,OAAO;QACP,UAAU,CAAA;YACR,YAAY,SAAS;gBACnB,UAAU,EAAE,MAAM,CAAC,KAAK;YAC1B;YACA,aAAa,UAAU;QACzB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/TextArea/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"prefixCls\", \"className\", \"onScroll\", \"renderTextarea\"];\nimport React, { useEffect, Fragment, useContext } from 'react';\nimport { EditorContext } from '../../Context';\nimport shortcuts from './shortcuts';\nimport Markdown from './Markdown';\nimport Textarea from './Textarea';\nimport { TextAreaCommandOrchestrator } from '../../commands';\nimport \"./index.css\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function TextArea(props) {\n  var _ref = props || {},\n    {\n      prefixCls,\n      className,\n      onScroll,\n      renderTextarea\n    } = _ref,\n    otherProps = _objectWithoutPropertiesLoose(_ref, _excluded);\n  var {\n    markdown,\n    scrollTop,\n    commands,\n    minHeight,\n    highlightEnable,\n    extraCommands,\n    dispatch\n  } = useContext(EditorContext);\n  var textRef = React.useRef(null);\n  var executeRef = React.useRef();\n  var warp = /*#__PURE__*/React.createRef();\n  useEffect(() => {\n    var state = {};\n    if (warp.current) {\n      state.textareaWarp = warp.current || undefined;\n      warp.current.scrollTop = scrollTop || 0;\n    }\n    if (dispatch) {\n      dispatch(_extends({}, state));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    if (textRef.current && dispatch) {\n      var commandOrchestrator = new TextAreaCommandOrchestrator(textRef.current);\n      executeRef.current = commandOrchestrator;\n      dispatch({\n        textarea: textRef.current,\n        commandOrchestrator\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var textStyle = highlightEnable ? {} : {\n    WebkitTextFillColor: 'initial',\n    overflow: 'auto'\n  };\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: warp,\n    className: prefixCls + \"-area \" + (className || ''),\n    onScroll: onScroll,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      className: prefixCls + \"-text\",\n      style: {\n        minHeight\n      },\n      children: renderTextarea ? (/*#__PURE__*/React.cloneElement(renderTextarea(_extends({}, otherProps, {\n        value: markdown,\n        autoComplete: 'off',\n        autoCorrect: 'off',\n        spellCheck: 'false',\n        autoCapitalize: 'off',\n        className: prefixCls + \"-text-input\",\n        style: {\n          WebkitTextFillColor: 'inherit',\n          overflow: 'auto'\n        }\n      }), {\n        dispatch,\n        onChange: otherProps.onChange,\n        shortcuts,\n        useContext: {\n          commands,\n          extraCommands,\n          commandOrchestrator: executeRef.current\n        }\n      }), {\n        ref: textRef\n      })) : /*#__PURE__*/_jsxs(Fragment, {\n        children: [highlightEnable && /*#__PURE__*/_jsx(Markdown, {\n          prefixCls: prefixCls\n        }), /*#__PURE__*/_jsx(Textarea, _extends({\n          prefixCls: prefixCls\n        }, otherProps, {\n          style: textStyle\n        }))]\n      })\n    })\n  });\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;;;AARA,IAAI,YAAY;IAAC;IAAa;IAAa;IAAY;CAAiB;;;;;;;;;AASzD,SAAS,SAAS,KAAK;IACpC,IAAI,OAAO,SAAS,CAAC,GACnB,EACE,SAAS,EACT,SAAS,EACT,QAAQ,EACR,cAAc,EACf,GAAG,MACJ,aAAa,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;IACnD,IAAI,EACF,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,eAAe,EACf,aAAa,EACb,QAAQ,EACT,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa;IAC5B,IAAI,UAAU,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,IAAI,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM;IAC7B,IAAI,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,SAAS;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,CAAC;QACb,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,YAAY,GAAG,KAAK,OAAO,IAAI;YACrC,KAAK,OAAO,CAAC,SAAS,GAAG,aAAa;QACxC;QACA,IAAI,UAAU;YACZ,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;QACxB;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,IAAI,UAAU;YAC/B,IAAI,sBAAsB,IAAI,0LAAA,CAAA,8BAA2B,CAAC,QAAQ,OAAO;YACzE,WAAW,OAAO,GAAG;YACrB,SAAS;gBACP,UAAU,QAAQ,OAAO;gBACzB;YACF;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,IAAI,YAAY,kBAAkB,CAAC,IAAI;QACrC,qBAAqB;QACrB,UAAU;IACZ;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC9B,KAAK;QACL,WAAW,YAAY,WAAW,CAAC,aAAa,EAAE;QAClD,UAAU;QACV,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YACjC,WAAW,YAAY;YACvB,OAAO;gBACL;YACF;YACA,UAAU,iBAAkB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,YAAY;gBAClG,OAAO;gBACP,cAAc;gBACd,aAAa;gBACb,YAAY;gBACZ,gBAAgB;gBAChB,WAAW,YAAY;gBACvB,OAAO;oBACL,qBAAqB;oBACrB,UAAU;gBACZ;YACF,IAAI;gBACF;gBACA,UAAU,WAAW,QAAQ;gBAC7B,WAAA,4LAAA,CAAA,UAAS;gBACT,YAAY;oBACV;oBACA;oBACA,qBAAqB,WAAW,OAAO;gBACzC;YACF,IAAI;gBACF,KAAK;YACP,KAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,qMAAA,CAAA,WAAQ,EAAE;gBACjC,UAAU;oBAAC,mBAAmB,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,2LAAA,CAAA,UAAQ,EAAE;wBACxD,WAAW;oBACb;oBAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,2LAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;wBACvC,WAAW;oBACb,GAAG,YAAY;wBACb,OAAO;oBACT;iBAAI;YACN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2428, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/Toolbar/Child.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React, { useContext, useMemo } from 'react';\nimport \"./Child.css\";\nimport Toolbar from './';\nimport { EditorContext } from '../../Context';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function Child(props) {\n  var {\n    prefixCls,\n    groupName,\n    commands,\n    children\n  } = props || {};\n  var {\n    barPopup = {}\n  } = useContext(EditorContext);\n  return useMemo(() => /*#__PURE__*/_jsx(\"div\", {\n    className: prefixCls + \"-toolbar-child \" + (groupName && barPopup[groupName] ? 'active' : ''),\n    onClick: e => e.stopPropagation(),\n    children: Array.isArray(commands) ? /*#__PURE__*/_jsx(Toolbar, _extends({\n      commands: commands\n    }, props, {\n      isChild: true\n    })) : children\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [commands, barPopup, groupName, prefixCls]);\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AACe,SAAS,MAAM,KAAK;IACjC,IAAI,EACF,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG,SAAS,CAAC;IACd,IAAI,EACF,WAAW,CAAC,CAAC,EACd,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa;IAC5B,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAC5C,WAAW,YAAY,oBAAoB,CAAC,aAAa,QAAQ,CAAC,UAAU,GAAG,WAAW,EAAE;YAC5F,SAAS,CAAA,IAAK,EAAE,eAAe;YAC/B,UAAU,MAAM,OAAO,CAAC,YAAY,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,uLAAA,CAAA,UAAO,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;gBACtE,UAAU;YACZ,GAAG,OAAO;gBACR,SAAS;YACX,MAAM;QACR,IACA,uDAAuD;IACvD;QAAC;QAAU;QAAU;QAAW;KAAU;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/Toolbar/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React, { Fragment, useContext, useEffect, useRef } from 'react';\nimport { EditorContext } from '../../Context';\nimport Child from './Child';\nimport \"./index.css\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport function ToolbarItems(props) {\n  var {\n    prefixCls,\n    overflow\n  } = props;\n  var {\n    fullscreen,\n    preview,\n    barPopup = {},\n    components,\n    commandOrchestrator,\n    dispatch\n  } = useContext(EditorContext);\n  var originalOverflow = useRef('');\n  function handleClick(command, name) {\n    if (!dispatch) return;\n    var state = {\n      barPopup: _extends({}, barPopup)\n    };\n    if (command.keyCommand === 'preview') {\n      state.preview = command.value;\n    }\n    if (command.keyCommand === 'fullscreen') {\n      state.fullscreen = !fullscreen;\n    }\n    if (props.commands && command.keyCommand === 'group') {\n      props.commands.forEach(item => {\n        if (name === item.groupName) {\n          state.barPopup[name] = true;\n        } else if (item.keyCommand) {\n          state.barPopup[item.groupName] = false;\n        }\n      });\n    } else if (name || command.parent) {\n      Object.keys(state.barPopup || {}).forEach(keyName => {\n        state.barPopup[keyName] = false;\n      });\n    }\n    if (Object.keys(state).length) {\n      dispatch(_extends({}, state));\n    }\n    commandOrchestrator && commandOrchestrator.executeCommand(command);\n  }\n  useEffect(() => {\n    if (document && overflow) {\n      if (fullscreen) {\n        // prevent scroll on fullscreen\n        document.body.style.overflow = 'hidden';\n      } else {\n        // get the original overflow only the first time\n        if (!originalOverflow.current) {\n          originalOverflow.current = window.getComputedStyle(document.body, null).overflow;\n        }\n        // reset to the original overflow\n        document.body.style.overflow = originalOverflow.current;\n      }\n    }\n  }, [fullscreen, originalOverflow, overflow]);\n  return /*#__PURE__*/_jsx(\"ul\", {\n    children: (props.commands || []).map((item, idx) => {\n      if (item.keyCommand === 'divider') {\n        return /*#__PURE__*/_jsx(\"li\", _extends({}, item.liProps, {\n          className: prefixCls + \"-toolbar-divider\"\n        }), idx);\n      }\n      if (!item.keyCommand) return /*#__PURE__*/_jsx(Fragment, {}, idx);\n      var activeBtn = fullscreen && item.keyCommand === 'fullscreen' || item.keyCommand === 'preview' && preview === item.value;\n      var childNode = item.children && typeof item.children === 'function' ? item.children({\n        getState: () => commandOrchestrator.getState(),\n        textApi: commandOrchestrator ? commandOrchestrator.textApi : undefined,\n        close: () => handleClick({}, item.groupName),\n        execute: () => handleClick({\n          execute: item.execute\n        }),\n        dispatch\n      }) : undefined;\n      var disabled = barPopup && preview && preview === 'preview' && !/(preview|fullscreen)/.test(item.keyCommand);\n      var render = (components == null ? void 0 : components.toolbar) || item.render;\n      var com = render && typeof render === 'function' ? render(item, !!disabled, handleClick, idx) : null;\n      return /*#__PURE__*/_jsxs(\"li\", _extends({}, item.liProps, {\n        className: activeBtn ? \"active\" : '',\n        children: [com && /*#__PURE__*/React.isValidElement(com) && com, !com && !item.buttonProps && item.icon, !com && item.buttonProps && /*#__PURE__*/React.createElement('button', _extends({\n          type: 'button',\n          key: idx,\n          disabled,\n          'data-name': item.name\n        }, item.buttonProps, {\n          onClick: evn => {\n            evn.stopPropagation();\n            handleClick(item, item.groupName);\n          }\n        }), item.icon), item.children && /*#__PURE__*/_jsx(Child, {\n          overflow: overflow,\n          groupName: item.groupName,\n          prefixCls: prefixCls,\n          children: childNode,\n          commands: Array.isArray(item.children) ? item.children : undefined\n        })]\n      }), idx);\n    })\n  });\n}\nexport default function Toolbar(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var {\n    prefixCls,\n    isChild,\n    className\n  } = props;\n  var {\n    commands,\n    extraCommands\n  } = useContext(EditorContext);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: prefixCls + \"-toolbar \" + className,\n    children: [/*#__PURE__*/_jsx(ToolbarItems, _extends({}, props, {\n      commands: props.commands || commands || []\n    })), !isChild && /*#__PURE__*/_jsx(ToolbarItems, _extends({}, props, {\n      commands: extraCommands || []\n    }))]\n  });\n}\nexport function ToolbarVisibility(props) {\n  var {\n    hideToolbar,\n    toolbarBottom,\n    placement,\n    overflow,\n    prefixCls\n  } = props;\n  if (hideToolbar || placement === 'bottom' && !toolbarBottom || placement === 'top' && toolbarBottom) {\n    return null;\n  }\n  var cls = toolbarBottom ? 'bottom' : '';\n  return /*#__PURE__*/_jsx(Toolbar, {\n    prefixCls: prefixCls,\n    overflow: overflow,\n    className: cls\n  });\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AACO,SAAS,aAAa,KAAK;IAChC,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG;IACJ,IAAI,EACF,UAAU,EACV,OAAO,EACP,WAAW,CAAC,CAAC,EACb,UAAU,EACV,mBAAmB,EACnB,QAAQ,EACT,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa;IAC5B,IAAI,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,SAAS,YAAY,OAAO,EAAE,IAAI;QAChC,IAAI,CAAC,UAAU;QACf,IAAI,QAAQ;YACV,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;QACzB;QACA,IAAI,QAAQ,UAAU,KAAK,WAAW;YACpC,MAAM,OAAO,GAAG,QAAQ,KAAK;QAC/B;QACA,IAAI,QAAQ,UAAU,KAAK,cAAc;YACvC,MAAM,UAAU,GAAG,CAAC;QACtB;QACA,IAAI,MAAM,QAAQ,IAAI,QAAQ,UAAU,KAAK,SAAS;YACpD,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACrB,IAAI,SAAS,KAAK,SAAS,EAAE;oBAC3B,MAAM,QAAQ,CAAC,KAAK,GAAG;gBACzB,OAAO,IAAI,KAAK,UAAU,EAAE;oBAC1B,MAAM,QAAQ,CAAC,KAAK,SAAS,CAAC,GAAG;gBACnC;YACF;QACF,OAAO,IAAI,QAAQ,QAAQ,MAAM,EAAE;YACjC,OAAO,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG,OAAO,CAAC,CAAA;gBACxC,MAAM,QAAQ,CAAC,QAAQ,GAAG;YAC5B;QACF;QACA,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE;YAC7B,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;QACxB;QACA,uBAAuB,oBAAoB,cAAc,CAAC;IAC5D;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,UAAU;YACxB,IAAI,YAAY;gBACd,+BAA+B;gBAC/B,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,gDAAgD;gBAChD,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,iBAAiB,OAAO,GAAG,OAAO,gBAAgB,CAAC,SAAS,IAAI,EAAE,MAAM,QAAQ;gBAClF;gBACA,iCAAiC;gBACjC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,iBAAiB,OAAO;YACzD;QACF;IACF,GAAG;QAAC;QAAY;QAAkB;KAAS;IAC3C,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,MAAM;QAC7B,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM;YAC1C,IAAI,KAAK,UAAU,KAAK,WAAW;gBACjC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE;oBACxD,WAAW,YAAY;gBACzB,IAAI;YACN;YACA,IAAI,CAAC,KAAK,UAAU,EAAE,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,qMAAA,CAAA,WAAQ,EAAE,CAAC,GAAG;YAC7D,IAAI,YAAY,cAAc,KAAK,UAAU,KAAK,gBAAgB,KAAK,UAAU,KAAK,aAAa,YAAY,KAAK,KAAK;YACzH,IAAI,YAAY,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,KAAK,aAAa,KAAK,QAAQ,CAAC;gBACnF,UAAU,IAAM,oBAAoB,QAAQ;gBAC5C,SAAS,sBAAsB,oBAAoB,OAAO,GAAG;gBAC7D,OAAO,IAAM,YAAY,CAAC,GAAG,KAAK,SAAS;gBAC3C,SAAS,IAAM,YAAY;wBACzB,SAAS,KAAK,OAAO;oBACvB;gBACA;YACF,KAAK;YACL,IAAI,WAAW,YAAY,WAAW,YAAY,aAAa,CAAC,uBAAuB,IAAI,CAAC,KAAK,UAAU;YAC3G,IAAI,SAAS,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,KAAK,KAAK,MAAM;YAC9E,IAAI,MAAM,UAAU,OAAO,WAAW,aAAa,OAAO,MAAM,CAAC,CAAC,UAAU,aAAa,OAAO;YAChG,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE;gBACzD,WAAW,YAAY,WAAW;gBAClC,UAAU;oBAAC,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,QAAQ;oBAAK,CAAC,OAAO,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI;oBAAE,CAAC,OAAO,KAAK,WAAW,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;wBACvL,MAAM;wBACN,KAAK;wBACL;wBACA,aAAa,KAAK,IAAI;oBACxB,GAAG,KAAK,WAAW,EAAE;wBACnB,SAAS,CAAA;4BACP,IAAI,eAAe;4BACnB,YAAY,MAAM,KAAK,SAAS;wBAClC;oBACF,IAAI,KAAK,IAAI;oBAAG,KAAK,QAAQ,IAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,uLAAA,CAAA,UAAK,EAAE;wBACxD,UAAU;wBACV,WAAW,KAAK,SAAS;wBACzB,WAAW;wBACX,UAAU;wBACV,UAAU,MAAM,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAAG;oBAC3D;iBAAG;YACL,IAAI;QACN;IACF;AACF;AACe,SAAS,QAAQ,KAAK;IACnC,IAAI,UAAU,KAAK,GAAG;QACpB,QAAQ,CAAC;IACX;IACA,IAAI,EACF,SAAS,EACT,OAAO,EACP,SAAS,EACV,GAAG;IACJ,IAAI,EACF,QAAQ,EACR,aAAa,EACd,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,gBAAa;IAC5B,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC/B,WAAW,YAAY,cAAc;QACrC,UAAU;YAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;gBAC7D,UAAU,MAAM,QAAQ,IAAI,YAAY,EAAE;YAC5C;YAAK,CAAC,WAAW,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;gBACnE,UAAU,iBAAiB,EAAE;YAC/B;SAAI;IACN;AACF;AACO,SAAS,kBAAkB,KAAK;IACrC,IAAI,EACF,WAAW,EACX,aAAa,EACb,SAAS,EACT,QAAQ,EACR,SAAS,EACV,GAAG;IACJ,IAAI,eAAe,cAAc,YAAY,CAAC,iBAAiB,cAAc,SAAS,eAAe;QACnG,OAAO;IACT;IACA,IAAI,MAAM,gBAAgB,WAAW;IACrC,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,SAAS;QAChC,WAAW;QACX,UAAU;QACV,WAAW;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2620, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/components/DragBar/index.js"], "sourcesContent": ["import React, { useEffect, useMemo, useRef } from 'react';\nimport \"./index.css\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar DragBar = props => {\n  var {\n    prefixCls,\n    onChange\n  } = props || {};\n  var $dom = useRef(null);\n  var dragRef = useRef();\n  var heightRef = useRef(props.height);\n  useEffect(() => {\n    if (heightRef.current !== props.height) {\n      heightRef.current = props.height;\n    }\n  }, [props.height]);\n  function handleMouseMove(event) {\n    if (dragRef.current) {\n      var _changedTouches$;\n      var clientY = event.clientY || ((_changedTouches$ = event.changedTouches[0]) == null ? void 0 : _changedTouches$.clientY);\n      var newHeight = dragRef.current.height + clientY - dragRef.current.dragY;\n      if (newHeight >= props.minHeight && newHeight <= props.maxHeight) {\n        onChange && onChange(dragRef.current.height + (clientY - dragRef.current.dragY));\n      }\n    }\n  }\n  function handleMouseUp() {\n    var _$dom$current, _$dom$current2;\n    dragRef.current = undefined;\n    document.removeEventListener('mousemove', handleMouseMove);\n    document.removeEventListener('mouseup', handleMouseUp);\n    (_$dom$current = $dom.current) == null || _$dom$current.removeEventListener('touchmove', handleMouseMove);\n    (_$dom$current2 = $dom.current) == null || _$dom$current2.removeEventListener('touchend', handleMouseUp);\n  }\n  function handleMouseDown(event) {\n    var _changedTouches$2, _$dom$current3, _$dom$current4;\n    event.preventDefault();\n    var clientY = event.clientY || ((_changedTouches$2 = event.changedTouches[0]) == null ? void 0 : _changedTouches$2.clientY);\n    dragRef.current = {\n      height: heightRef.current,\n      dragY: clientY\n    };\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n    (_$dom$current3 = $dom.current) == null || _$dom$current3.addEventListener('touchmove', handleMouseMove, {\n      passive: false\n    });\n    (_$dom$current4 = $dom.current) == null || _$dom$current4.addEventListener('touchend', handleMouseUp, {\n      passive: false\n    });\n  }\n  useEffect(() => {\n    if (document) {\n      var _$dom$current5, _$dom$current6;\n      (_$dom$current5 = $dom.current) == null || _$dom$current5.addEventListener('touchstart', handleMouseDown, {\n        passive: false\n      });\n      (_$dom$current6 = $dom.current) == null || _$dom$current6.addEventListener('mousedown', handleMouseDown);\n    }\n    return () => {\n      if (document) {\n        var _$dom$current7;\n        (_$dom$current7 = $dom.current) == null || _$dom$current7.removeEventListener('touchstart', handleMouseDown);\n        document.removeEventListener('mousemove', handleMouseMove);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var svg = useMemo(() => /*#__PURE__*/_jsx(\"svg\", {\n    viewBox: \"0 0 512 512\",\n    height: \"100%\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      fill: \"currentColor\",\n      d: \"M304 256c0 26.5-21.5 48-48 48s-48-21.5-48-48 21.5-48 48-48 48 21.5 48 48zm120-48c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48zm-336 0c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z\"\n    })\n  }), []);\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: prefixCls + \"-bar\",\n    ref: $dom,\n    children: svg\n  });\n};\nexport default DragBar;"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AACA,IAAI,UAAU,CAAA;IACZ,IAAI,EACF,SAAS,EACT,QAAQ,EACT,GAAG,SAAS,CAAC;IACd,IAAI,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClB,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACnB,IAAI,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,MAAM,MAAM;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,KAAK,MAAM,MAAM,EAAE;YACtC,UAAU,OAAO,GAAG,MAAM,MAAM;QAClC;IACF,GAAG;QAAC,MAAM,MAAM;KAAC;IACjB,SAAS,gBAAgB,KAAK;QAC5B,IAAI,QAAQ,OAAO,EAAE;YACnB,IAAI;YACJ,IAAI,UAAU,MAAM,OAAO,IAAI,CAAC,CAAC,mBAAmB,MAAM,cAAc,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,iBAAiB,OAAO;YACxH,IAAI,YAAY,QAAQ,OAAO,CAAC,MAAM,GAAG,UAAU,QAAQ,OAAO,CAAC,KAAK;YACxE,IAAI,aAAa,MAAM,SAAS,IAAI,aAAa,MAAM,SAAS,EAAE;gBAChE,YAAY,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,CAAC,UAAU,QAAQ,OAAO,CAAC,KAAK;YAChF;QACF;IACF;IACA,SAAS;QACP,IAAI,eAAe;QACnB,QAAQ,OAAO,GAAG;QAClB,SAAS,mBAAmB,CAAC,aAAa;QAC1C,SAAS,mBAAmB,CAAC,WAAW;QACxC,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,mBAAmB,CAAC,aAAa;QACzF,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,mBAAmB,CAAC,YAAY;IAC5F;IACA,SAAS,gBAAgB,KAAK;QAC5B,IAAI,mBAAmB,gBAAgB;QACvC,MAAM,cAAc;QACpB,IAAI,UAAU,MAAM,OAAO,IAAI,CAAC,CAAC,oBAAoB,MAAM,cAAc,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,OAAO;QAC1H,QAAQ,OAAO,GAAG;YAChB,QAAQ,UAAU,OAAO;YACzB,OAAO;QACT;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;QACrC,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,gBAAgB,CAAC,aAAa,iBAAiB;YACvG,SAAS;QACX;QACA,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,gBAAgB,CAAC,YAAY,eAAe;YACpG,SAAS;QACX;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,IAAI,gBAAgB;YACpB,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,gBAAgB,CAAC,cAAc,iBAAiB;gBACxG,SAAS;YACX;YACA,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,gBAAgB,CAAC,aAAa;QAC1F;QACA,OAAO;YACL,IAAI,UAAU;gBACZ,IAAI;gBACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,mBAAmB,CAAC,cAAc;gBAC5F,SAAS,mBAAmB,CAAC,aAAa;YAC5C;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,IAAI,MAAM,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAC/C,SAAS;YACT,QAAQ;YACR,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBAClC,MAAM;gBACN,GAAG;YACL;QACF,IAAI,EAAE;IACN,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC9B,WAAW,YAAY;QACvB,KAAK;QACL,UAAU;IACZ;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/Editor.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"prefixCls\", \"className\", \"value\", \"commands\", \"commandsFilter\", \"direction\", \"extraCommands\", \"height\", \"enableScroll\", \"visibleDragbar\", \"highlightEnable\", \"preview\", \"fullscreen\", \"overflow\", \"previewOptions\", \"textareaProps\", \"maxHeight\", \"minHeight\", \"autoFocus\", \"tabSize\", \"defaultTabEnable\", \"onChange\", \"onStatistics\", \"onHeightChange\", \"hideToolbar\", \"toolbarBottom\", \"components\", \"renderTextarea\"];\nimport React, { useEffect, useReducer, useMemo, useRef, useImperativeHandle } from 'react';\nimport MarkdownPreview from '@uiw/react-markdown-preview';\nimport TextArea from './components/TextArea';\nimport { ToolbarVisibility } from './components/Toolbar';\nimport DragBar from './components/DragBar';\nimport { getCommands, getExtraCommands, TextAreaCommandOrchestrator } from './commands';\nimport { reducer, EditorContext } from './Context';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction setGroupPopFalse(data) {\n  if (data === void 0) {\n    data = {};\n  }\n  Object.keys(data).forEach(keyname => {\n    data[keyname] = false;\n  });\n  return data;\n}\nvar InternalMDEditor = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref = props || {},\n    {\n      prefixCls = 'w-md-editor',\n      className,\n      value: propsValue,\n      commands = getCommands(),\n      commandsFilter,\n      direction,\n      extraCommands = getExtraCommands(),\n      height = 200,\n      enableScroll = true,\n      visibleDragbar = typeof props.visiableDragbar === 'boolean' ? props.visiableDragbar : true,\n      highlightEnable = true,\n      preview: previewType = 'live',\n      fullscreen = false,\n      overflow = true,\n      previewOptions = {},\n      textareaProps,\n      maxHeight = 1200,\n      minHeight = 100,\n      autoFocus,\n      tabSize = 2,\n      defaultTabEnable = false,\n      onChange,\n      onStatistics,\n      onHeightChange,\n      hideToolbar,\n      toolbarBottom = false,\n      components,\n      renderTextarea\n    } = _ref,\n    other = _objectWithoutPropertiesLoose(_ref, _excluded);\n  var cmds = commands.map(item => commandsFilter ? commandsFilter(item, false) : item).filter(Boolean);\n  var extraCmds = extraCommands.map(item => commandsFilter ? commandsFilter(item, true) : item).filter(Boolean);\n  var [state, dispatch] = useReducer(reducer, {\n    markdown: propsValue,\n    preview: previewType,\n    components,\n    height,\n    minHeight,\n    highlightEnable,\n    tabSize,\n    defaultTabEnable,\n    scrollTop: 0,\n    scrollTopPreview: 0,\n    commands: cmds,\n    extraCommands: extraCmds,\n    fullscreen,\n    barPopup: {}\n  });\n  var container = useRef(null);\n  var previewRef = useRef(null);\n  var enableScrollRef = useRef(enableScroll);\n  useImperativeHandle(ref, () => _extends({}, state, {\n    container: container.current,\n    dispatch\n  }));\n  useMemo(() => enableScrollRef.current = enableScroll, [enableScroll]);\n  useEffect(() => {\n    var stateInit = {};\n    if (container.current) {\n      stateInit.container = container.current || undefined;\n    }\n    stateInit.markdown = propsValue || '';\n    stateInit.barPopup = {};\n    if (dispatch) {\n      dispatch(_extends({}, state, stateInit));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var cls = [className, 'wmde-markdown-var', direction ? prefixCls + \"-\" + direction : null, prefixCls, state.preview ? prefixCls + \"-show-\" + state.preview : null, state.fullscreen ? prefixCls + \"-fullscreen\" : null].filter(Boolean).join(' ').trim();\n  useMemo(() => propsValue !== state.markdown && dispatch({\n    markdown: propsValue || ''\n  }), [propsValue, state.markdown]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => previewType !== state.preview && dispatch({\n    preview: previewType\n  }), [previewType]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => tabSize !== state.tabSize && dispatch({\n    tabSize\n  }), [tabSize]);\n  useMemo(() => highlightEnable !== state.highlightEnable && dispatch({\n    highlightEnable\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [highlightEnable]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => autoFocus !== state.autoFocus && dispatch({\n    autoFocus: autoFocus\n  }), [autoFocus]);\n  useMemo(() => fullscreen !== state.fullscreen && dispatch({\n    fullscreen: fullscreen\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [fullscreen]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => height !== state.height && dispatch({\n    height: height\n  }), [height]);\n  useMemo(() => height !== state.height && onHeightChange && onHeightChange(state.height, height, state), [height, onHeightChange, state]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => commands !== state.commands && dispatch({\n    commands: cmds\n  }), [props.commands]);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useMemo(() => extraCommands !== state.extraCommands && dispatch({\n    extraCommands: extraCmds\n  }), [props.extraCommands]);\n  var textareaDomRef = useRef();\n  var active = useRef('preview');\n  var initScroll = useRef(false);\n  useMemo(() => {\n    textareaDomRef.current = state.textareaWarp;\n    if (state.textareaWarp) {\n      state.textareaWarp.addEventListener('mouseover', () => {\n        active.current = 'text';\n      });\n      state.textareaWarp.addEventListener('mouseleave', () => {\n        active.current = 'preview';\n      });\n    }\n  }, [state.textareaWarp]);\n  var handleScroll = (e, type) => {\n    if (!enableScrollRef.current) return;\n    var textareaDom = textareaDomRef.current;\n    var previewDom = previewRef.current ? previewRef.current : undefined;\n    if (!initScroll.current) {\n      active.current = type;\n      initScroll.current = true;\n    }\n    if (textareaDom && previewDom) {\n      var scale = (textareaDom.scrollHeight - textareaDom.offsetHeight) / (previewDom.scrollHeight - previewDom.offsetHeight);\n      if (e.target === textareaDom && active.current === 'text') {\n        previewDom.scrollTop = textareaDom.scrollTop / scale;\n      }\n      if (e.target === previewDom && active.current === 'preview') {\n        textareaDom.scrollTop = previewDom.scrollTop * scale;\n      }\n      var scrollTop = 0;\n      if (active.current === 'text') {\n        scrollTop = textareaDom.scrollTop || 0;\n      } else if (active.current === 'preview') {\n        scrollTop = previewDom.scrollTop || 0;\n      }\n      dispatch({\n        scrollTop\n      });\n    }\n  };\n  var previewClassName = prefixCls + \"-preview \" + (previewOptions.className || '');\n  var handlePreviewScroll = e => handleScroll(e, 'preview');\n  var mdPreview = useMemo(() => /*#__PURE__*/_jsx(\"div\", {\n    ref: previewRef,\n    className: previewClassName,\n    children: /*#__PURE__*/_jsx(MarkdownPreview, _extends({}, previewOptions, {\n      onScroll: handlePreviewScroll,\n      source: state.markdown || ''\n    }))\n  }), [previewClassName, previewOptions, state.markdown]);\n  var preview = (components == null ? void 0 : components.preview) && (components == null ? void 0 : components.preview(state.markdown || '', state, dispatch));\n  if (preview && /*#__PURE__*/React.isValidElement(preview)) {\n    mdPreview = /*#__PURE__*/_jsx(\"div\", {\n      className: previewClassName,\n      ref: previewRef,\n      onScroll: handlePreviewScroll,\n      children: preview\n    });\n  }\n  var containerStyle = _extends({}, other.style, {\n    height: state.height || '100%'\n  });\n  var containerClick = () => dispatch({\n    barPopup: _extends({}, setGroupPopFalse(state.barPopup))\n  });\n  var dragBarChange = newHeight => dispatch({\n    height: newHeight\n  });\n  var changeHandle = evn => {\n    onChange && onChange(evn.target.value, evn, state);\n    if (textareaProps && textareaProps.onChange) {\n      textareaProps.onChange(evn);\n    }\n    if (state.textarea && state.textarea instanceof HTMLTextAreaElement && onStatistics) {\n      var obj = new TextAreaCommandOrchestrator(state.textarea);\n      var objState = obj.getState() || {};\n      onStatistics(_extends({}, objState, {\n        lineCount: evn.target.value.split('\\n').length,\n        length: evn.target.value.length\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsx(EditorContext.Provider, {\n    value: _extends({}, state, {\n      dispatch\n    }),\n    children: /*#__PURE__*/_jsxs(\"div\", _extends({\n      ref: container,\n      className: cls\n    }, other, {\n      onClick: containerClick,\n      style: containerStyle,\n      children: [/*#__PURE__*/_jsx(ToolbarVisibility, {\n        hideToolbar: hideToolbar,\n        toolbarBottom: toolbarBottom,\n        prefixCls: prefixCls,\n        overflow: overflow,\n        placement: \"top\"\n      }), /*#__PURE__*/_jsxs(\"div\", {\n        className: prefixCls + \"-content\",\n        children: [/(edit|live)/.test(state.preview || '') && /*#__PURE__*/_jsx(TextArea, _extends({\n          className: prefixCls + \"-input\",\n          prefixCls: prefixCls,\n          autoFocus: autoFocus\n        }, textareaProps, {\n          onChange: changeHandle,\n          renderTextarea: (components == null ? void 0 : components.textarea) || renderTextarea,\n          onScroll: e => handleScroll(e, 'text')\n        })), /(live|preview)/.test(state.preview || '') && mdPreview]\n      }), visibleDragbar && !state.fullscreen && /*#__PURE__*/_jsx(DragBar, {\n        prefixCls: prefixCls,\n        height: state.height,\n        maxHeight: maxHeight,\n        minHeight: minHeight,\n        onChange: dragBarChange\n      }), /*#__PURE__*/_jsx(ToolbarVisibility, {\n        hideToolbar: hideToolbar,\n        toolbarBottom: toolbarBottom,\n        prefixCls: prefixCls,\n        overflow: overflow,\n        placement: \"bottom\"\n      })]\n    }))\n  });\n});\nvar Editor = InternalMDEditor;\nEditor.Markdown = MarkdownPreview;\nexport default Editor;"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AARA,IAAI,YAAY;IAAC;IAAa;IAAa;IAAS;IAAY;IAAkB;IAAa;IAAiB;IAAU;IAAgB;IAAkB;IAAmB;IAAW;IAAc;IAAY;IAAkB;IAAiB;IAAa;IAAa;IAAa;IAAW;IAAoB;IAAY;IAAgB;IAAkB;IAAe;IAAiB;IAAc;CAAiB;;;;;;;;;AAS1a,SAAS,iBAAiB,IAAI;IAC5B,IAAI,SAAS,KAAK,GAAG;QACnB,OAAO,CAAC;IACV;IACA,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;QACxB,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,OAAO;AACT;AACA,IAAI,mBAAmB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,CAAC,OAAO;IAC3D,IAAI,OAAO,SAAS,CAAC,GACnB,EACE,YAAY,aAAa,EACzB,SAAS,EACT,OAAO,UAAU,EACjB,WAAW,CAAA,GAAA,0LAAA,CAAA,cAAW,AAAD,GAAG,EACxB,cAAc,EACd,SAAS,EACT,gBAAgB,CAAA,GAAA,0LAAA,CAAA,mBAAgB,AAAD,GAAG,EAClC,SAAS,GAAG,EACZ,eAAe,IAAI,EACnB,iBAAiB,OAAO,MAAM,eAAe,KAAK,YAAY,MAAM,eAAe,GAAG,IAAI,EAC1F,kBAAkB,IAAI,EACtB,SAAS,cAAc,MAAM,EAC7B,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,iBAAiB,CAAC,CAAC,EACnB,aAAa,EACb,YAAY,IAAI,EAChB,YAAY,GAAG,EACf,SAAS,EACT,UAAU,CAAC,EACX,mBAAmB,KAAK,EACxB,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,WAAW,EACX,gBAAgB,KAAK,EACrB,UAAU,EACV,cAAc,EACf,GAAG,MACJ,QAAQ,CAAA,GAAA,6KAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;IAC9C,IAAI,OAAO,SAAS,GAAG,CAAC,CAAA,OAAQ,iBAAiB,eAAe,MAAM,SAAS,MAAM,MAAM,CAAC;IAC5F,IAAI,YAAY,cAAc,GAAG,CAAC,CAAA,OAAQ,iBAAiB,eAAe,MAAM,QAAQ,MAAM,MAAM,CAAC;IACrG,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gKAAA,CAAA,UAAO,EAAE;QAC1C,UAAU;QACV,SAAS;QACT;QACA;QACA;QACA;QACA;QACA;QACA,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,eAAe;QACf;QACA,UAAU,CAAC;IACb;IACA,IAAI,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,IAAI,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;YACjD,WAAW,UAAU,OAAO;YAC5B;QACF;IACA,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,gBAAgB,OAAO,GAAG,cAAc;QAAC;KAAa;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,CAAC;QACjB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,SAAS,GAAG,UAAU,OAAO,IAAI;QAC7C;QACA,UAAU,QAAQ,GAAG,cAAc;QACnC,UAAU,QAAQ,GAAG,CAAC;QACtB,IAAI,UAAU;YACZ,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QAC/B;IACA,uDAAuD;IACzD,GAAG,EAAE;IACL,IAAI,MAAM;QAAC;QAAW;QAAqB,YAAY,YAAY,MAAM,YAAY;QAAM;QAAW,MAAM,OAAO,GAAG,YAAY,WAAW,MAAM,OAAO,GAAG;QAAM,MAAM,UAAU,GAAG,YAAY,gBAAgB;KAAK,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI;IACtP,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,eAAe,MAAM,QAAQ,IAAI,SAAS;YACtD,UAAU,cAAc;QAC1B,IAAI;QAAC;QAAY,MAAM,QAAQ;KAAC;IAChC,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,gBAAgB,MAAM,OAAO,IAAI,SAAS;YACtD,SAAS;QACX,IAAI;QAAC;KAAY;IACjB,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,YAAY,MAAM,OAAO,IAAI,SAAS;YAClD;QACF,IAAI;QAAC;KAAQ;IACb,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,oBAAoB,MAAM,eAAe,IAAI,SAAS;YAClE;QACF,IACA,uDAAuD;IACvD;QAAC;KAAgB;IACjB,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,cAAc,MAAM,SAAS,IAAI,SAAS;YACtD,WAAW;QACb,IAAI;QAAC;KAAU;IACf,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,eAAe,MAAM,UAAU,IAAI,SAAS;YACxD,YAAY;QACd,IACA,uDAAuD;IACvD;QAAC;KAAW;IACZ,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,MAAM,MAAM,IAAI,SAAS;YAChD,QAAQ;QACV,IAAI;QAAC;KAAO;IACZ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,MAAM,MAAM,IAAI,kBAAkB,eAAe,MAAM,MAAM,EAAE,QAAQ,QAAQ;QAAC;QAAQ;QAAgB;KAAM;IACvI,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,aAAa,MAAM,QAAQ,IAAI,SAAS;YACpD,UAAU;QACZ,IAAI;QAAC,MAAM,QAAQ;KAAC;IACpB,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,kBAAkB,MAAM,aAAa,IAAI,SAAS;YAC9D,eAAe;QACjB,IAAI;QAAC,MAAM,aAAa;KAAC;IACzB,IAAI,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,IAAI,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpB,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACN,eAAe,OAAO,GAAG,MAAM,YAAY;QAC3C,IAAI,MAAM,YAAY,EAAE;YACtB,MAAM,YAAY,CAAC,gBAAgB,CAAC,aAAa;gBAC/C,OAAO,OAAO,GAAG;YACnB;YACA,MAAM,YAAY,CAAC,gBAAgB,CAAC,cAAc;gBAChD,OAAO,OAAO,GAAG;YACnB;QACF;IACF,GAAG;QAAC,MAAM,YAAY;KAAC;IACvB,IAAI,eAAe,CAAC,GAAG;QACrB,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC9B,IAAI,cAAc,eAAe,OAAO;QACxC,IAAI,aAAa,WAAW,OAAO,GAAG,WAAW,OAAO,GAAG;QAC3D,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,OAAO,GAAG;YACjB,WAAW,OAAO,GAAG;QACvB;QACA,IAAI,eAAe,YAAY;YAC7B,IAAI,QAAQ,CAAC,YAAY,YAAY,GAAG,YAAY,YAAY,IAAI,CAAC,WAAW,YAAY,GAAG,WAAW,YAAY;YACtH,IAAI,EAAE,MAAM,KAAK,eAAe,OAAO,OAAO,KAAK,QAAQ;gBACzD,WAAW,SAAS,GAAG,YAAY,SAAS,GAAG;YACjD;YACA,IAAI,EAAE,MAAM,KAAK,cAAc,OAAO,OAAO,KAAK,WAAW;gBAC3D,YAAY,SAAS,GAAG,WAAW,SAAS,GAAG;YACjD;YACA,IAAI,YAAY;YAChB,IAAI,OAAO,OAAO,KAAK,QAAQ;gBAC7B,YAAY,YAAY,SAAS,IAAI;YACvC,OAAO,IAAI,OAAO,OAAO,KAAK,WAAW;gBACvC,YAAY,WAAW,SAAS,IAAI;YACtC;YACA,SAAS;gBACP;YACF;QACF;IACF;IACA,IAAI,mBAAmB,YAAY,cAAc,CAAC,eAAe,SAAS,IAAI,EAAE;IAChF,IAAI,sBAAsB,CAAA,IAAK,aAAa,GAAG;IAC/C,IAAI,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YACrD,KAAK;YACL,WAAW;YACX,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,qLAAA,CAAA,UAAe,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;gBACxE,UAAU;gBACV,QAAQ,MAAM,QAAQ,IAAI;YAC5B;QACF,IAAI;QAAC;QAAkB;QAAgB,MAAM,QAAQ;KAAC;IACtD,IAAI,UAAU,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,KAAK,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,CAAC,MAAM,QAAQ,IAAI,IAAI,OAAO,SAAS;IAC5J,IAAI,WAAW,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,UAAU;QACzD,YAAY,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YACnC,WAAW;YACX,KAAK;YACL,UAAU;YACV,UAAU;QACZ;IACF;IACA,IAAI,iBAAiB,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,KAAK,EAAE;QAC7C,QAAQ,MAAM,MAAM,IAAI;IAC1B;IACA,IAAI,iBAAiB,IAAM,SAAS;YAClC,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,iBAAiB,MAAM,QAAQ;QACxD;IACA,IAAI,gBAAgB,CAAA,YAAa,SAAS;YACxC,QAAQ;QACV;IACA,IAAI,eAAe,CAAA;QACjB,YAAY,SAAS,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK;QAC5C,IAAI,iBAAiB,cAAc,QAAQ,EAAE;YAC3C,cAAc,QAAQ,CAAC;QACzB;QACA,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,YAAY,uBAAuB,cAAc;YACnF,IAAI,MAAM,IAAI,0LAAA,CAAA,8BAA2B,CAAC,MAAM,QAAQ;YACxD,IAAI,WAAW,IAAI,QAAQ,MAAM,CAAC;YAClC,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU;gBAClC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,MAAM;gBAC9C,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM;YACjC;QACF;IACF;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,gKAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;QAC/C,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;YACzB;QACF;QACA,UAAU,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;YAC3C,KAAK;YACL,WAAW;QACb,GAAG,OAAO;YACR,SAAS;YACT,OAAO;YACP,UAAU;gBAAC,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,uLAAA,CAAA,oBAAiB,EAAE;oBAC9C,aAAa;oBACb,eAAe;oBACf,WAAW;oBACX,UAAU;oBACV,WAAW;gBACb;gBAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,OAAK,AAAD,EAAE,OAAO;oBAC5B,WAAW,YAAY;oBACvB,UAAU;wBAAC,cAAc,IAAI,CAAC,MAAM,OAAO,IAAI,OAAO,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,wLAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAQ,AAAD,EAAE;4BACzF,WAAW,YAAY;4BACvB,WAAW;4BACX,WAAW;wBACb,GAAG,eAAe;4BAChB,UAAU;4BACV,gBAAgB,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,QAAQ,KAAK;4BACvE,UAAU,CAAA,IAAK,aAAa,GAAG;wBACjC;wBAAK,iBAAiB,IAAI,CAAC,MAAM,OAAO,IAAI,OAAO;qBAAU;gBAC/D;gBAAI,kBAAkB,CAAC,MAAM,UAAU,IAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,uLAAA,CAAA,UAAO,EAAE;oBACpE,WAAW;oBACX,QAAQ,MAAM,MAAM;oBACpB,WAAW;oBACX,WAAW;oBACX,UAAU;gBACZ;gBAAI,WAAW,GAAE,CAAA,GAAA,uNAAA,CAAA,MAAI,AAAD,EAAE,uLAAA,CAAA,oBAAiB,EAAE;oBACvC,aAAa;oBACb,eAAe;oBACf,WAAW;oBACX,UAAU;oBACV,WAAW;gBACb;aAAG;QACL;IACF;AACF;AACA,IAAI,SAAS;AACb,OAAO,QAAQ,GAAG,qLAAA,CAAA,UAAe;uCAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3035, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3043, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/node_modules/%40uiw/react-md-editor/esm/index.js"], "sourcesContent": ["import MDEditor from './Editor';\nimport * as commands from './commands';\nimport * as MarkdownUtil from './utils/markdownUtils';\nimport \"./index.css\";\nexport * from './commands';\nexport * from './commands/group';\nexport * from './utils/markdownUtils';\nexport * from './utils/InsertTextAtPosition';\nexport * from './Editor';\nexport * from './Context';\nexport * from './Types';\nexport { MarkdownUtil, commands };\nexport default MDEditor;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAGA;AAEA;AAEA;AACA;;;;;;;;;;;;;uCAEe,+JAAA,CAAA,UAAQ", "ignoreList": [0], "debugId": null}}]}