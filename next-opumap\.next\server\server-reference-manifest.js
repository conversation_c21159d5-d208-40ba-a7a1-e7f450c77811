self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"402fc347d9dd2b1b15867c0f1c996e02fcd1c8aea1\": {\n      \"workers\": {\n        \"app/profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/profile/actions/update-profile.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/profile/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"uvuxr9cwVZ9uxgoW5b6R530YJ+6IwW4UKlFirHLImNc=\"\n}"