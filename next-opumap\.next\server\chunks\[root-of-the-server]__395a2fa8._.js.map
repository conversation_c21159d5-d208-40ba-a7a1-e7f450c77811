{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { cookies } from 'next/headers';\r\n\r\nexport async function createClient() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            );\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/callback/route.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { type NextRequest, NextResponse } from 'next/server';\r\n\r\nexport async function GET(request: NextRequest) {\r\n  const requestUrl = new URL(request.url);\r\n  const code = requestUrl.searchParams.get('code');\r\n  const next = requestUrl.searchParams.get('next') ?? '/';\r\n  const type = requestUrl.searchParams.get('type'); // Get the type parameter (recovery for password reset)\r\n\r\n  console.log('Auth callback called with code:', code ? 'present' : 'missing', 'next:', next, 'type:', type || 'none');\r\n\r\n  if (code) {\r\n    try {\r\n      const supabase = await createClient();\r\n\r\n      console.log('Exchanging code for session...');\r\n      const { data, error } = await supabase.auth.exchangeCodeForSession(code);\r\n\r\n      if (error) {\r\n        console.error('Error exchanging code for session:', error);\r\n        return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=${encodeURIComponent(error.message)}`);\r\n      }\r\n\r\n      if (data.session) {\r\n        console.log('Session exchange successful, user ID:', data.session.user.id);\r\n\r\n                // Force multiple session refreshes to ensure proper cookie propagation        console.log('Performing additional session refresh...');        await supabase.auth.getSession();        // Wait longer for cookie setting to complete in production environments        await new Promise(resolve => setTimeout(resolve, 500)); // Increased timeout for production        // Multiple refreshes for better cookie propagation        await supabase.auth.getSession();        await new Promise(resolve => setTimeout(resolve, 300));                // Additional refresh cycle for better session establishment        await supabase.auth.getSession();        await new Promise(resolve => setTimeout(resolve, 200));\r\n        \r\n        // Final session check\r\n        const { data: { session: finalSession } } = await supabase.auth.getSession();\r\n        if (finalSession) {\r\n          console.log('Final session confirmed, user ID:', finalSession.user.id);\r\n\r\n          // For password reset flow, add a special parameter to indicate valid session\r\n          if (type === 'recovery' && next.includes('/reset-password')) {\r\n            console.log('Password reset flow detected, adding session validation parameter');\r\n            return NextResponse.redirect(`${requestUrl.origin}${next}?session_valid=true&auth_event=recovery`);\r\n          }\r\n        } else {\r\n          console.warn('Final session check failed');\r\n        }\r\n      } else {\r\n        console.warn('Session exchange completed but no session returned');\r\n\r\n        // Special handling for password reset with no session\r\n        if (type === 'recovery' && next.includes('/reset-password')) {\r\n          console.log('Password reset flow with no session, redirecting with error parameter');\r\n          return NextResponse.redirect(`${requestUrl.origin}${next}?session_error=no_session&auth_event=recovery`);\r\n        }\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Unexpected error during code exchange:', err);\r\n      return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=unexpected_error`);\r\n    }\r\n  } else {\r\n    console.warn('No code parameter provided to callback');\r\n    return NextResponse.redirect(`${requestUrl.origin}/auth/auth-code-error?error=no_code`);\r\n  }\r\n\r\n  // Create response with enhanced headers for proper cookie propagation\r\n  const response = NextResponse.redirect(`${requestUrl.origin}${next}`);\r\n\r\n  // Add comprehensive cache control to prevent caching of this redirect\r\n  response.headers.set('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');\r\n  response.headers.set('Pragma', 'no-cache');\r\n  response.headers.set('Expires', '0');\r\n\r\n  // Add a small delay header to help with cookie propagation\r\n  response.headers.set('X-Session-Processed', 'true');\r\n\r\n  console.log('Redirecting to:', `${requestUrl.origin}${next}`);\r\n  return response;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;IACtC,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC;IACzC,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC,WAAW;IACpD,MAAM,OAAO,WAAW,YAAY,CAAC,GAAG,CAAC,SAAS,uDAAuD;IAEzG,QAAQ,GAAG,CAAC,mCAAmC,OAAO,YAAY,WAAW,SAAS,MAAM,SAAS,QAAQ;IAE7G,IAAI,MAAM;QACR,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;YAElC,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;YAEnE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,4BAA4B,EAAE,mBAAmB,MAAM,OAAO,GAAG;YACrH;YAEA,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC,yCAAyC,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;gBAEjE,0rBAA0rB;gBAElsB,sBAAsB;gBACtB,MAAM,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAC1E,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC,qCAAqC,aAAa,IAAI,CAAC,EAAE;oBAErE,6EAA6E;oBAC7E,IAAI,SAAS,cAAc,KAAK,QAAQ,CAAC,oBAAoB;wBAC3D,QAAQ,GAAG,CAAC;wBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,GAAG,KAAK,uCAAuC,CAAC;oBACnG;gBACF,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;gBAEb,sDAAsD;gBACtD,IAAI,SAAS,cAAc,KAAK,QAAQ,CAAC,oBAAoB;oBAC3D,QAAQ,GAAG,CAAC;oBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,GAAG,KAAK,6CAA6C,CAAC;gBACzG;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,4CAA4C,CAAC;QACjG;IACF,OAAO;QACL,QAAQ,IAAI,CAAC;QACb,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,CAAC,mCAAmC,CAAC;IACxF;IAEA,sEAAsE;IACtE,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,GAAG,WAAW,MAAM,GAAG,MAAM;IAEpE,sEAAsE;IACtE,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;IACtC,SAAS,OAAO,CAAC,GAAG,CAAC,UAAU;IAC/B,SAAS,OAAO,CAAC,GAAG,CAAC,WAAW;IAEhC,2DAA2D;IAC3D,SAAS,OAAO,CAAC,GAAG,CAAC,uBAAuB;IAE5C,QAAQ,GAAG,CAAC,mBAAmB,GAAG,WAAW,MAAM,GAAG,MAAM;IAC5D,OAAO;AACT", "debugId": null}}]}