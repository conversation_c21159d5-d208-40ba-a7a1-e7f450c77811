'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { FloatingBackground } from './components/shared';
import { HeroSection } from './components/HeroSection';
import { FeaturesSection } from './components/FeaturesSection';
import { ProcessSection } from './components/ProcessSection';
import { CTASection } from './components/CTASection';

const LandingPageRefactored: React.FC = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/login');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <FloatingBackground />
      
      <HeroSection onGetStarted={handleGetStarted} />
      
      <FeaturesSection />
      
      <ProcessSection />
      
      <CTASection onGetStarted={handleGetStarted} />
    </div>
  );
};

export default LandingPageRefactored;