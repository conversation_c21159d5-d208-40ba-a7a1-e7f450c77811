'use client';

import React, { useState } from "react";
import Image from "next/image";
import { motion } from 'framer-motion';
import { ScrollArea } from "@/components/ui/scroll-area";
import { BusinessData } from "@/types";
import { getPhotoUrl } from "@/utils";
import { formatOpeningHours } from "@/utils/formatters";
import { MapPin, Phone, Globe, Clock, Info, Building } from 'lucide-react';

interface BusinessCardProps {
  selectedBusiness: BusinessData | null;
  isFlipped: boolean;
  toggleFlip: () => void;
}

/**
 * Revolutionary Business Card with morphing design
 */
const BusinessCard: React.FC<BusinessCardProps> = ({
  selectedBusiness,
  isFlipped,
  toggleFlip
}) => {
  const [isHovered, setIsHovered] = useState(false);

  if (!selectedBusiness) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.6, ease: [0.68, -0.55, 0.265, 1.55] }}
        className="relative group w-full h-[350px] md:h-[400px]"
      >
        <div className="relative h-full bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-md rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 flex flex-col items-center justify-center">
          {/* Floating decorative elements */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute top-4 right-4 w-20 h-20 rounded-full bg-gradient-to-br from-primary/10 to-primary/5"
              animate={{ rotate: 360, scale: [1, 1.1, 1] }}
              transition={{ duration: 20, repeat: Infinity }}
            />
            <motion.div
              className="absolute bottom-6 left-6 w-16 h-16 rounded-full bg-gradient-to-br from-accent/10 to-accent/5"
              animate={{ rotate: -360, scale: [1, 0.9, 1] }}
              transition={{ duration: 25, repeat: Infinity }}
            />
          </div>

          {/* Pulsing location icon */}
          <motion.div
            className="w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mb-4 relative overflow-hidden"
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <MapPin className="w-8 h-8 text-primary" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: ['-100%', '200%'] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            />
          </motion.div>

          <motion.h3 
            className="text-lg font-semibold text-muted-foreground mb-2 text-center"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Wählen Sie ein Unternehmen
          </motion.h3>
          <p className="text-sm text-muted-foreground/80 text-center px-4">
            Klicken Sie auf der Karte oder auf ein POI, um Unternehmensinformationen anzuzeigen.
          </p>

          {/* Animated border */}
          <motion.div
            className="absolute inset-0 rounded-2xl border-2 border-primary/20"
            animate={{ borderColor: ['hsl(var(--primary) / 0.2)', 'hsl(var(--primary) / 0.4)', 'hsl(var(--primary) / 0.2)'] }}
            transition={{ duration: 3, repeat: Infinity }}
          />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.6, ease: [0.68, -0.55, 0.265, 1.55] }}
      className="relative group w-full h-[350px] md:h-[400px] cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-lg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={toggleFlip}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggleFlip();
        }
      }}
      tabIndex={0}
      role="button"
      aria-pressed={isFlipped}
    >
      <div className="relative h-full" style={{ perspective: '1000px' }}>
        <motion.div
          className="relative w-full h-full"
          style={{ transformStyle: 'preserve-3d' }}
          animate={{ rotateY: isFlipped ? 180 : 0 }}
          transition={{ duration: 0.7, ease: "easeInOut" }}
        >
          {/* Front Side */}
          <div className="absolute inset-0 w-full h-full" style={{ backfaceVisibility: 'hidden' }}>
            <div className="relative h-full bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-primary/30">
              {/* Accent gradient on hover */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                animate={isHovered ? { opacity: 0.1 } : { opacity: 0 }}
              />

              {/* Floating decorative elements */}
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <motion.div
                  className="absolute top-4 right-4 w-12 h-12 rounded-full bg-gradient-to-br from-primary/10 to-primary/5"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 15, repeat: Infinity }}
                />
                <motion.div
                  className="absolute bottom-4 left-4 w-8 h-8 rounded-full bg-gradient-to-br from-accent/10 to-accent/5"
                  animate={{ rotate: -360 }}
                  transition={{ duration: 20, repeat: Infinity }}
                />
              </div>

              <div className="relative p-6 h-full flex flex-col">
                {/* Header with business type indicator */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <motion.h2 
                      className="text-xl font-bold text-foreground leading-tight mb-2"
                      layoutId={`business-name-${selectedBusiness.place_id}`}
                    >
                      {selectedBusiness.name}
                    </motion.h2>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="w-4 h-4 mr-1 text-primary" />
                      <span className="line-clamp-2">{selectedBusiness.formatted_address}</span>
                    </div>
                  </div>
                  
                  {/* Business type badge */}
                  <motion.div
                    className="px-2 py-1 bg-primary/10 rounded-full text-xs font-medium text-primary"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Building className="w-3 h-3 inline mr-1" />
                    Business
                  </motion.div>
                </div>

                {/* Image section with enhanced styling */}
                {(("photos" in selectedBusiness && selectedBusiness.photos && selectedBusiness.photos.length > 0) ||
                  ("description" in selectedBusiness && selectedBusiness.description)) && (
                    <motion.div 
                      className="relative mb-4 flex justify-center"
                      whileHover={{ scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="relative w-32 h-32 rounded-xl overflow-hidden border-2 border-border/30 shadow-lg">
                        <Image
                          src={getPhotoUrl(selectedBusiness) || "https://via.placeholder.com/128"}
                          alt={selectedBusiness.name || "Business image"}
                          width={128}
                          height={128}
                          className="object-cover w-full h-full"
                          unoptimized={getPhotoUrl(selectedBusiness)?.startsWith("https://via.placeholder.com")}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>
                    </motion.div>
                  )}

                {/* Quick info indicators */}
                <div className="flex-1 flex flex-col justify-end">
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    {selectedBusiness.formatted_phone_number && (
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Phone className="w-3 h-3 mr-1 text-primary" />
                        <span>Telefon</span>
                      </div>
                    )}
                    {selectedBusiness.website && (
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Globe className="w-3 h-3 mr-1 text-primary" />
                        <span>Website</span>
                      </div>
                    )}
                    {"opening_hours" in selectedBusiness && selectedBusiness.opening_hours && (
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="w-3 h-3 mr-1 text-primary" />
                        <span>Öffnungszeiten</span>
                      </div>
                    )}
                    {"description" in selectedBusiness && selectedBusiness.description && (
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Info className="w-3 h-3 mr-1 text-primary" />
                        <span>Details</span>
                      </div>
                    )}
                  </div>

                  {/* Interaction hint */}
                  <motion.div 
                    className="text-center py-2 px-4 bg-primary/5 rounded-lg border border-primary/20"
                    animate={{ opacity: [0.7, 1, 0.7] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <span className="text-xs font-medium text-primary">
                      Klicken für Details →
                    </span>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>

          {/* Back Side */}
          <div className="absolute inset-0 w-full h-full" style={{ backfaceVisibility: 'hidden', transform: 'rotateY(180deg)' }}>
            <div className="relative h-full bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden">
              {/* Enhanced gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-50" />

              <ScrollArea className="h-full p-6">
                <div className="space-y-6">
                  {/* Header */}
                  <div className="text-center border-b border-border/30 pb-4">
                    <h3 className="text-lg font-bold text-foreground mb-1">
                      {selectedBusiness.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">Detailinformationen</p>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold text-foreground flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-primary" />
                      Kontakt
                    </h4>
                    
                    <div className="space-y-3 pl-6">
                      <div className="flex items-start">
                        <span className="text-xs font-medium text-muted-foreground w-16">Telefon:</span>
                        <span className="text-xs text-foreground">
                          {selectedBusiness.formatted_phone_number || "Nicht verfügbar"}
                        </span>
                      </div>
                      
                      <div className="flex items-start">
                        <span className="text-xs font-medium text-muted-foreground w-16">Website:</span>
                        {selectedBusiness.website ? (
                          <a
                            href={selectedBusiness.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-primary hover:text-primary/80 underline"
                            onClick={(e) => e.stopPropagation()}
                          >
                            Zur Website →
                          </a>
                        ) : (
                          <span className="text-xs text-muted-foreground">Nicht verfügbar</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Opening Hours */}
                  {"opening_hours" in selectedBusiness && selectedBusiness.opening_hours && (
                    <div className="space-y-4">
                      <h4 className="text-sm font-semibold text-foreground flex items-center">
                        <Clock className="w-4 h-4 mr-2 text-primary" />
                        Öffnungszeiten
                      </h4>
                      <div className="space-y-2 pl-6">
                        {(selectedBusiness.opening_hours.weekday_text && selectedBusiness.opening_hours.weekday_text.length > 0
                          ? selectedBusiness.opening_hours.weekday_text
                          : formatOpeningHours(selectedBusiness.opening_hours.periods ?? [])
                        ).map((day, index) => (
                          <div key={index} className="text-xs text-muted-foreground leading-relaxed">
                            {day}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Description */}
                  {"description" in selectedBusiness && selectedBusiness.description && (
                    <div className="space-y-4">
                      <h4 className="text-sm font-semibold text-foreground flex items-center">
                        <Info className="w-4 h-4 mr-2 text-primary" />
                        Beschreibung
                      </h4>
                      <p className="text-xs text-muted-foreground leading-relaxed pl-6">
                        {selectedBusiness.description}
                      </p>
                    </div>
                  )}

                  {/* Back button */}
                  <motion.div 
                    className="text-center py-2 px-4 bg-primary/5 rounded-lg border border-primary/20 mt-6"
                    animate={{ opacity: [0.7, 1, 0.7] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <span className="text-xs font-medium text-primary">
                      ← Zurück zur Übersicht
                    </span>
                  </motion.div>
                </div>
              </ScrollArea>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Hover border effect */}
      <motion.div
        className="absolute inset-0 rounded-2xl border-2 border-primary/0 pointer-events-none"
        animate={isHovered ? { borderColor: 'hsl(var(--primary) / 0.3)' } : { borderColor: 'hsl(var(--primary) / 0)' }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

export default BusinessCard;
