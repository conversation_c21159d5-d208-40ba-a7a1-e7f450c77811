{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport { FaGith<PERSON>, FaTwi<PERSON>, Fa<PERSON>inkedin, FaEnvelope } from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  const currentYear = new Date().getFullYear();\r\n\r\n  const footerLinks = {\r\n    company: [\r\n      { name: '<PERSON>ber uns', href: '/about' },\r\n      { name: 'Team', href: '/team' },\r\n      { name: '<PERSON><PERSON><PERSON>', href: '/careers' },\r\n      { name: '<PERSON><PERSON><PERSON>', href: '/contact' },\r\n    ],\r\n    legal: [\r\n      { name: 'Impressum', href: '/legal/impressum' },\r\n      { name: '<PERSON><PERSON>chu<PERSON>', href: '/legal/privacy' },\r\n      { name: 'AGB', href: '/legal/terms' },\r\n      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/legal/cookies' },\r\n    ],\r\n    resources: [\r\n      { name: 'Doku<PERSON>', href: '/docs' },\r\n      { name: 'Blog', href: '/blog' },\r\n      { name: '<PERSON><PERSON><PERSON> & <PERSON>', href: '/support' },\r\n      { name: '<PERSON>Q', href: '/faq' },\r\n    ],\r\n  };\r\n\r\n  const socialLinks = [\r\n    { icon: <FaGithub size={20} />, href: 'https://github.com/yourusername' },\r\n    { icon: <FaTwitter size={20} />, href: 'https://twitter.com/yourusername' },\r\n    { icon: <FaLinkedin size={20} />, href: 'https://linkedin.com/company/yourcompany' },\r\n    { icon: <FaEnvelope size={20} />, href: 'mailto:<EMAIL>' },\r\n  ];\r\n\r\n  return (\r\n    <footer className=\"bg-card border-t border-border\">\r\n      <div className=\"container mx-auto px-4 py-12\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {/* Company Info */}\r\n          <div className=\"space-y-4\">\r\n            <h3 className=\"text-lg font-semibold text-foreground\">OpuMap</h3>\r\n            <p className=\"text-muted-foreground text-sm\">\r\n              Ihre Plattform für interaktive Karten und Standortdienste.\r\n            </p>\r\n            <div className=\"flex space-x-4\">\r\n              {socialLinks.map((social, index) => (\r\n                <a\r\n                  key={index}\r\n                  href={social.href}\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-muted-foreground hover:text-foreground transition-colors\"\r\n                >\r\n                  {social.icon}\r\n                </a>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Company Links */}\r\n          <div>\r\n            <h3 className=\"text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n              Unternehmen\r\n            </h3>\r\n            <ul className=\"mt-4 space-y-2\">\r\n              {footerLinks.company.map((link) => (\r\n                <li key={link.name}>\r\n                  <Link\r\n                    href={link.href}\r\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\r\n                  >\r\n                    {link.name}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Legal Links */}\r\n          <div>\r\n            <h3 className=\"text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n              Rechtliches\r\n            </h3>\r\n            <ul className=\"mt-4 space-y-2\">\r\n              {footerLinks.legal.map((link) => (\r\n                <li key={link.name}>\r\n                  <Link\r\n                    href={link.href}\r\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\r\n                  >\r\n                    {link.name}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Resources */}\r\n          <div>\r\n            <h3 className=\"text-xs font-semibold text-muted-foreground uppercase tracking-wider\">\r\n              Ressourcen\r\n            </h3>\r\n            <ul className=\"mt-4 space-y-2\">\r\n              {footerLinks.resources.map((link) => (\r\n                <li key={link.name}>\r\n                  <Link\r\n                    href={link.href}\r\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\r\n                  >\r\n                    {link.name}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Copyright */}\r\n        <div className=\"mt-12 pt-8 border-t border-border\">\r\n          <p className=\"text-sm text-muted-foreground text-center\">\r\n            &copy; {currentYear} OpuMap. Alle Rechte vorbehalten.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAW;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;YAAmB;YAC9C;gBAAE,MAAM;gBAAe,MAAM;YAAiB;YAC9C;gBAAE,MAAM;gBAAO,MAAM;YAAe;YACpC;gBAAE,MAAM;gBAAqB,MAAM;YAAiB;SACrD;QACD,WAAW;YACT;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAmB,MAAM;YAAW;YAC5C;gBAAE,MAAM;gBAAO,MAAM;YAAO;SAC7B;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,oBAAM,8OAAC,8IAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;YAAQ,MAAM;QAAkC;QACxE;YAAE,oBAAM,8OAAC,8IAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YAAQ,MAAM;QAAmC;QAC1E;YAAE,oBAAM,8OAAC,8IAAA,CAAA,aAAU;gBAAC,MAAM;;;;;;YAAQ,MAAM;QAA2C;QACnF;YAAE,oBAAM,8OAAC,8IAAA,CAAA,aAAU;gBAAC,MAAM;;;;;;YAAQ,MAAM;QAAgC;KACzE;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAET,OAAO,IAAI;2CANP;;;;;;;;;;;;;;;;sCAab,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuE;;;;;;8CAGrF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuE;;;;;;8CAGrF,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuE;;;;;;8CAGrF,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA4C;4BAC/C;4BAAY;;;;;;;;;;;;;;;;;;;;;;;AAMhC;uCAEe", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthContext\",\n);\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8DACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"AuthContext\",\n);\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AuthContext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SessionExpiredModal.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SessionExpiredModal.tsx <module evaluation>\",\n    \"default\",\n);\nexport const setIntentionalLogout = registerClientReference(\n    function() { throw new Error(\"Attempted to call setIntentionalLogout() from the server but setIntentionalLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SessionExpiredModal.tsx <module evaluation>\",\n    \"setIntentionalLogout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,wEACA", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SessionExpiredModal.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SessionExpiredModal.tsx\",\n    \"default\",\n);\nexport const setIntentionalLogout = registerClientReference(\n    function() { throw new Error(\"Attempted to call setIntentionalLogout() from the server but setIntentionalLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SessionExpiredModal.tsx\",\n    \"setIntentionalLogout\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,oDACA", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/PageTransitionContainer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PageTransitionContainer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/PageTransitionContainer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PageTransitionContainer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/registry.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/registry.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/lib/registry.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/registry.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,kCACA", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file://c%3A/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\r\nimport { Inter } from \"next/font/google\"; // Or another font if preferred\r\nimport \"./globals.css\";\r\nimport Navbar from \"@/components/Navbar\"; // Import Navbar\r\nimport Footer from \"@/components/Footer\"; // Import Footer\r\nimport { AuthProvider } from \"@/contexts/AuthContext\"; // Import AuthProvider\r\nimport { ThemeProvider } from \"next-themes\";\r\nimport SessionExpiredModal from \"@/components/SessionExpiredModal\"; // Import SessionExpiredModal\r\nimport PageTransitionContainer from \"@/components/PageTransitionContainer\"; // Import PageTransitionContainer\r\nimport StyledComponentsRegistry from '@/lib/registry'; // Import the registry\r\n\r\nconst inter = Inter({ subsets: [\"latin\"] });\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"OpuMap Next\", // Update title\r\n  description: \"OpuMap application migrated to Next.js\", // Update description\r\n  icons: {\r\n    icon: [\r\n      {\r\n        url: '/weiss-ohne-bg-svg.svg?v=7',\r\n        sizes: 'any',\r\n        type: 'image/svg+xml',\r\n      },\r\n    ],\r\n    apple: [\r\n      {\r\n        url: '/weiss-ohne-bg-svg.svg?v=7',\r\n        sizes: '180x180',\r\n        type: 'image/svg+xml',\r\n      },\r\n    ],\r\n  },\r\n};\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  // NOTE: We cannot directly use hooks like useContext in Server Components (RootLayout is one by default).\r\n  // Authentication state needs to be handled differently:\r\n  // 1. Fetch auth state on the server (e.g., from cookies/session in middleware or layout).\r\n  // 2. Pass auth state down to client components OR use a Client Component wrapper here.\r\n  // 3. For simplicity now, we'll wrap everything in AuthProvider (a Client Component)\r\n  //    and let Navbar/Pages handle conditional rendering based on context.\r\n\r\n  return (\r\n    // Add suppressHydrationWarning to ignore extension-added attributes\r\n    <html lang=\"en\" suppressHydrationWarning>\r\n      <head>\r\n        <link rel=\"icon\" type=\"image/svg+xml\" sizes=\"any\" href=\"/weiss-ohne-bg-svg.svg?v=7\" />\r\n        <link rel=\"apple-touch-icon\" type=\"image/svg+xml\" sizes=\"180x180\" href=\"/weiss-ohne-bg-svg.svg?v=7\" />\r\n      </head>\r\n      <body className={`${inter.className} flex flex-col h-screen`}>\r\n        <StyledComponentsRegistry>\r\n          <AuthProvider>\r\n            <ThemeProvider attribute=\"class\" defaultTheme=\"light\">\r\n              <div className=\"fixed top-0 left-0 right-0 z-50\">\r\n                <Navbar />\r\n              </div>\r\n              <main className=\"flex-1 flex flex-col pt-20\">\r\n                <div className=\"flex-1\">\r\n                  <PageTransitionContainer>\r\n                    {children}\r\n                  </PageTransitionContainer>\r\n                </div>\r\n              </main>\r\n              <Footer />\r\n              <SessionExpiredModal />\r\n            </ThemeProvider>\r\n          </AuthProvider>\r\n        </StyledComponentsRegistry>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGA,0NAA0C,gBAAgB;AAC1D,0NAA0C,gBAAgB;AAC1D,gOAAuD,sBAAsB;AAC7E;AACA,oPAAoE,6BAA6B;AACjG,4PAA4E,iCAAiC;AAC7G,gNAAuD,sBAAsB;;;;;;;;;;;AAItE,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QACL,MAAM;YACJ;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;SACD;QACD,OAAO;YACL;gBACE,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;SACD;IACH;AACF;AACe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,0GAA0G;IAC1G,wDAAwD;IACxD,0FAA0F;IAC1F,uFAAuF;IACvF,oFAAoF;IACpF,yEAAyE;IAEzE,OACE,oEAAoE;kBACpE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAgB,OAAM;wBAAM,MAAK;;;;;;kCACvD,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;wBAAgB,OAAM;wBAAU,MAAK;;;;;;;;;;;;0BAEzE,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC;0BAC1D,cAAA,8OAAC,uHAAA,CAAA,UAAwB;8BACvB,cAAA,8OAAC,+HAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,gJAAA,CAAA,gBAAa;4BAAC,WAAU;4BAAQ,cAAa;;8CAC5C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;8CAET,8OAAC;oCAAK,WAAU;8CACd,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6IAAA,CAAA,UAAuB;sDACrB;;;;;;;;;;;;;;;;8CAIP,8OAAC,4HAAA,CAAA,UAAM;;;;;8CACP,8OAAC,yIAAA,CAAA,UAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC", "debugId": null}}]}