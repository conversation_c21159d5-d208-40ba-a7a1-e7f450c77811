'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';

// Import Hook
import { useOpulabStrategie } from './hooks/useOpulabStrategie';

// Import components
import {
  StrategieInfo,
  StrategieBriefing,
  StrategieAnalyse,
  StrategieList,
  RelevantAspects,
  SaveSettingsSection
} from './components';
import FloatingBackground from '../Landingpage/components/shared/FloatingBackground';
import LabCard from './components/LabCard';
import LabStatusIndicator from './components/LabStatusIndicator';
import AnalysisStatusIndicator from './components/ui/AnalysisStatusIndicator';
import LabOverlayElements from './components/LabOverlayElements';
import { Dialog, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import Loader from '@/components/ui/Loader';
import { Button } from '@/components/ui/button';
import { Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { TouchStartLaborButton } from './components/ui';
import { Rocket, FlaskConical, Atom, Microscope } from 'lucide-react';
import MarkdownEditor from '@/components/MarkdownEditor';

// Import styles
import {
  containerStyles,
  headerStyles,
  spacingStyles
} from './styles';

// Define the main component
export default function OpulabStrategiePage() {
  // Use the custom hook to manage state and logic
  const {
    // State
    strategies,
    selectedStrategy,
    isNewStrategy,
    isLoadingStrategies,
    isFormEnabled,
    companyInfo,
    personalGoal,
    shortTermTasks,
    longTermGoals,
    analysisResult,
    isSavingStrategy: isSaving,
    isAnalyzing,
    message,
    messageLocation,
    showMessageInCompanyInfo,
    showLabModal,
    analysisInput,
    // showResultButton, // Implicitly handled by analysisResult/selectedStrategy state
    priorisierungResult,
    // showPriorisierungResult, // Implicitly handled by priorisierungResult state
    // zielsetzungResult, // Not directly displayed, used in analysis
    // showZielsetzungResult, // Not directly displayed
    shortTermValue,
    longTermValue,
    aspects,
    analysisStepsStatus,
    labProgress,
    // Setters
    setCompanyInfo,
    setPersonalGoal,
    setShortTermTasks,
    setLongTermGoals,
    setAnalysisInput,
    setShowLabModal,
    setShortTermValue,
    setLongTermValue,
    // Handlers
    handleSelectStrategy,
    handleSaveCompanyInfo,
    handleStartAnalysis,
    handleStartPriorisierung,
    handleSaveAnalysis,
    handleAddNewStrategy,
    handleAspectChange,
    handleSaveSettings,
    handleDeleteStrategy,
  } = useOpulabStrategie();

  // No need for handleCreateAIStrategy in the hook, keep it here if needed
  const handleCreateAIStrategy = () => {
    console.log('Creating AI strategy - Placeholder');
    // Add API call logic here if functionality exists
  };

  // Determine if the main analysis button should be visible/active
  // This logic could also reside within the hook if preferred
  const canStartAnalysis = isFormEnabled && selectedStrategy && !isSaving && !isAnalyzing;

  // Effect to fix scroll issues when modal is closed
  useEffect(() => {
    // When modal is closed, restore scroll functionality
    if (!showLabModal) {
      // Small timeout to ensure DOM is updated
      const timer = setTimeout(() => {
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
      }, 100);

      return () => clearTimeout(timer);
    }
    // Leere Bereinigungsfunktion für den anderen Pfad
    return () => {};
  }, [showLabModal]);

  return (
    <>
    {/* Consistent Background */}
    <FloatingBackground />
    
    {/* Laboratory Scientific Overlay */}
    <LabOverlayElements />
    
    <div className="relative z-10 min-h-screen">
      <div className={`${containerStyles.wrapper} relative z-10`}>
        {/* Laboratory Header Section */}
        <motion.div 
          className={spacingStyles.section}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="flex flex-col md:flex-row md:items-start">
            <header className={`${headerStyles.container} md:text-left w-full`}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-col items-center md:items-start mb-6"
              >
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-2 flex flex-col md:flex-row items-center">
                  <span className="bg-gradient-to-r from-[#4A90E2] to-[#10b981] bg-clip-text text-transparent mr-3">Opulab</span>
                  <motion.div 
                    className="flex items-center gap-2"
                    animate={{ 
                      rotate: [0, 5, -5, 0],
                    }}
                    transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <FlaskConical className="w-8 h-8 md:w-10 md:h-10 text-[#4A90E2]" />
                  </motion.div>
                </h1>
                <div className="flex items-center gap-3 text-lg md:text-xl text-muted-foreground">
                  <Atom className="w-5 h-5 text-[#8b5cf6] animate-spin" style={{ animationDuration: '8s' }} />
                  <span className="font-light">Strategic Opportunity Laboratory</span>
                  <Microscope className="w-5 h-5 text-[#10b981]" />
                </div>
              </motion.div>
              <motion.h2 
                className="text-xl md:text-2xl font-semibold text-[#4A90E2] text-center md:text-left"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                Wissenschaftliche Strategieentwicklung
              </motion.h2>
            </header>
          </div>
        </motion.div>

        {/* Laboratory Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-[25%,35%,40%] gap-6 lg:gap-8">
          {/* Laboratory Control Panel */}
          <motion.div 
            className={`${containerStyles.column} order-1 lg:order-1`}
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {/* Strategy Research Samples */}
            <LabCard variant="elevated" delay={0.2}>
              <StrategieList
                strategies={strategies}
                selectedStrategy={selectedStrategy}
                onSelectStrategy={handleSelectStrategy}
                onAddNewStrategy={handleAddNewStrategy}
                onCreateAIStrategy={handleCreateAIStrategy}
                isLoading={isLoadingStrategies}
              />
            </LabCard>

            {/* Laboratory Specimen Data */}
            <LabCard 
              variant="primary" 
              delay={0.4}
              disabled={!isFormEnabled || isSaving}
            >
              <StrategieInfo
                companyInfo={companyInfo}
                onCompanyInfoChange={setCompanyInfo}
                onSave={handleSaveCompanyInfo}
                isSaving={isSaving}
                message={showMessageInCompanyInfo ? message : ''}
                showPersonalGoal={false}
                disabled={!isFormEnabled || isSaving}
                personalGoal={personalGoal}
                onPersonalGoalChange={setPersonalGoal}
              />
            </LabCard>

            {/* Laboratory Analysis Parameters */}
            <LabCard 
              variant="analysis" 
              delay={0.6}
              disabled={!isFormEnabled || isSaving}
            >
              <RelevantAspects
                aspects={aspects}
                onAspectChange={handleAspectChange}
                disabled={!isFormEnabled || isSaving}
              />
            </LabCard>
          </motion.div>

          {/* Laboratory Analysis Chamber */}
          <motion.div 
            className={`${containerStyles.column} order-2 lg:order-2`}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
          >
            <LabCard 
              variant="analysis" 
              delay={0.8}
              disabled={!isFormEnabled || isSaving}
            >
              <StrategieAnalyse
                result={priorisierungResult}
                onStartAnalysis={handleStartPriorisierung}
                showAnalysisResult={false}
                shortTermValue={shortTermValue}
                longTermValue={longTermValue}
                onShortTermChange={(value) => setShortTermValue(value[0])}
                onLongTermChange={(value) => setLongTermValue(value[0])}
              />
            </LabCard>
          </motion.div>

          {/* Laboratory Research Station */}
          <motion.div 
            className={`${containerStyles.column} order-3 lg:order-3`}
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            {/* Research Objectives & Strategy Synthesis */}
            <div className={`flex flex-col ${spacingStyles.cardGap}`}>
              {/* Strategic Research Goals */}
              <LabCard 
                variant="success" 
                delay={1.0}
                disabled={!isFormEnabled || isSaving}
                className="min-h-[400px]"
              >
                <StrategieInfo
                  personalGoal={personalGoal}
                  onPersonalGoalChange={setPersonalGoal}
                  onSave={handleSaveCompanyInfo}
                  isSaving={isSaving}
                  message={!showMessageInCompanyInfo ? message : ''}
                  showCompanyInfo={false}
                  disabled={!isFormEnabled || isSaving}
                  companyInfo={companyInfo}
                  onCompanyInfoChange={setCompanyInfo}
                />
              </LabCard>

              {/* Strategic Research Briefing */}
              <LabCard 
                variant="primary" 
                delay={1.2}
                disabled={!isFormEnabled || isSaving}
                className="min-h-[320px]"
              >
                <StrategieBriefing
                  shortTermTasks={shortTermTasks}
                  longTermGoals={longTermGoals}
                  onShortTermTasksChange={setShortTermTasks}
                  onLongTermGoalsChange={setLongTermGoals}
                />
              </LabCard>
            </div>

            {/* Laboratory Data Preservation */}
            <LabCard 
              variant="warning" 
              delay={1.4}
              disabled={!isFormEnabled || isSaving}
            >
              <SaveSettingsSection
                onSaveSettings={handleSaveSettings}
                onDeleteStrategy={handleDeleteStrategy}
                isSaving={isSaving}
                selectedStrategy={selectedStrategy?.strategy_name || ''}
                isNewStrategy={isNewStrategy}
                message={messageLocation === 'saveSettings' ? message : ''}
                disabled={!isFormEnabled || isSaving}
              />
            </LabCard>

            {/* Main Laboratory Analysis Engine */}
            <LabCard 
              variant="elevated" 
              delay={1.6}
              disabled={!isFormEnabled}
              className="relative overflow-hidden"
            >
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4A90E2] via-[#8b5cf6] to-[#10b981] opacity-60" />
              
              <CardHeader className="text-center items-center py-6">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="w-16 h-16 mb-4 bg-gradient-to-r from-[#4A90E2] to-[#10b981] rounded-full flex items-center justify-center"
                >
                  <FlaskConical className="w-8 h-8 text-white" />
                </motion.div>
                
                <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[#4A90E2] to-[#10b981] bg-clip-text text-transparent">
                  Strategic Analysis Engine
                </CardTitle>
                <CardDescription className="text-muted-foreground mb-4">
                  KI-gestützte Laboranalyse
                </CardDescription>
                
                {message && messageLocation === 'analysis' && (
                  <motion.p 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`text-sm px-4 py-2 rounded-lg ${message.startsWith('Fehler') ? 'bg-red-500/10 text-red-400 border border-red-500/20' : 'bg-green-500/10 text-green-400 border border-green-500/20'}`}
                  >
                    {message}
                  </motion.p>
                )}
                
                {/* Laboratory Status Indicators */}
                {isAnalyzing && (
                  <motion.div 
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="mt-4 w-full"
                  >
                    {labProgress ? (
                      <div className="w-full space-y-4">
                        <LabStatusIndicator 
                          status={labProgress} 
                          variant="dna"
                        />
                        <AnalysisStatusIndicator 
                          status={analysisStepsStatus}
                        />
                      </div>
                    ) : (
                      <Loader />
                    )}
                  </motion.div>
                )}
              </CardHeader>
              
              <CardContent className="flex flex-col items-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <TouchStartLaborButton 
                    onClick={handleStartAnalysis} 
                    disabled={!canStartAnalysis}
                    className="relative overflow-hidden bg-gradient-to-r from-[#4A90E2] to-[#10b981] hover:from-[#5B9BD5] hover:to-[#48BB78] text-white font-semibold py-4 px-8 rounded-xl shadow-lg transition-all duration-300"
                  >
                    <span className="relative z-10 flex items-center">
                      {isAnalyzing ? "Labor arbeitet..." : "🧪 Labor starten"} 
                      <Rocket className={`inline-block ml-2 ${isAnalyzing ? 'animate-pulse' : ''}`} />
                    </span>
                    {!isAnalyzing && (
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                        animate={{ x: ['-100%', '100%'] }}
                        transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                      />
                    )}
                  </TouchStartLaborButton>
                </motion.div>
                
                {selectedStrategy?.id && analysisResult && (
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-6 w-full flex flex-col items-center"
                  >
                    <span className="text-base font-semibold text-[#4A90E2] mb-3 flex items-center">
                      <Microscope className="w-5 h-5 mr-2" />
                      Laborergebnis verfügbar:
                    </span>
                    <Button 
                      variant="outline" 
                      onClick={() => { setAnalysisInput(analysisResult); setShowLabModal(true); }} 
                      disabled={isSaving}
                      className="bg-gradient-to-r from-[rgba(74,144,226,0.1)] to-[rgba(16,185,129,0.1)] border-[#4A90E2]/30 hover:border-[#4A90E2] hover:bg-[rgba(74,144,226,0.1)] text-[#4A90E2] font-medium"
                    >
                      📊 {selectedStrategy?.strategy_name} Analyse
                    </Button>
                  </motion.div>
                )}
              </CardContent>
            </LabCard>
          </motion.div>
        </div>
      </div>
    </div>

    {/* Laboratory Analysis Modal */}
    <Dialog
      open={showLabModal}
      onOpenChange={(open) => {
        setShowLabModal(open);
        // Immediately fix scroll when closing via the X button or clicking outside
        if (!open) {
          document.body.style.overflow = '';
          document.body.style.paddingRight = '';
        }
      }}
      modal={true}
    >
      <DialogContent className="max-w-screen-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            Analyse bearbeiten: {selectedStrategy?.strategy_name}
          </DialogTitle>
          <DialogDescription>
            Geben Sie Ihren Text ein oder bearbeiten Sie das Ergebnis:
          </DialogDescription>
        </DialogHeader>
        <div className="w-full mb-4">
          <MarkdownEditor
            value={analysisInput}
            onChange={(value) => setAnalysisInput(value || '')}
            height={500}
            preview="edit"
          />
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              setShowLabModal(false);
              // Fix scroll when using the button to close
              document.body.style.overflow = '';
              document.body.style.paddingRight = '';
            }}
            disabled={isSaving}
          >
            Abbrechen
          </Button>
          <Button 
            onClick={handleSaveAnalysis} 
            disabled={isSaving}
            aria-busy={isSaving}
          >
            {isSaving ? 'Speichern...' : 'Speichern'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}
