'use client';

import React, { useState } from "react";
import { motion } from 'framer-motion';
import { BusinessData } from "@/types";
import { formatAnalysisDate } from "@/utils";
import { Eye, Calendar, RefreshCw, ArrowRight } from 'lucide-react';

export interface AnalysisCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>;
  analysisData: string | null;
  analysisDate: string | null;
  isLoading: boolean;
  onCreateAnalysis: () => void;
  onViewAnalysis: () => void;
  selectedBusiness: BusinessData | null;
  accentColor: string;
  delay: number;
}

/**
 * Individual Analysis Card with morphing design
 */
const AnalysisCard: React.FC<AnalysisCardProps> = ({
  title,
  description,
  icon: Icon,
  analysisData,
  analysisDate,
  isLoading,
  onCreateAnalysis,
  onViewAnalysis,
  selectedBusiness,
  accentColor,
  delay
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const hasData = Boolean(analysisData);
  const isDisabled = !selectedBusiness || isLoading;

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateY: -15 }}
      animate={{ opacity: 1, y: 0, rotateY: 0 }}
      transition={{ duration: 0.8, delay, ease: [0.68, -0.55, 0.265, 1.55] }}
      className="group relative overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative bg-gradient-to-br from-card/70 to-card/30 backdrop-blur-xl rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60 h-full min-h-[340px] flex flex-col">
        {/* Accent Border & Background */}
        <motion.div 
          className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-2xl"
          style={{ background: `linear-gradient(135deg, ${accentColor}20, transparent)` }}
          animate={isHovered ? { opacity: 0.15 } : { opacity: 0 }}
        />
        
        {/* Floating decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            className="absolute top-4 right-4 w-12 h-12 rounded-full"
            style={{ backgroundColor: `${accentColor}10` }}
            animate={{ rotate: 360, scale: [1, 1.1, 1] }}
            transition={{ duration: 20, repeat: Infinity }}
          />
          <motion.div
            className="absolute bottom-6 left-6 w-8 h-8 rounded-full"
            style={{ backgroundColor: `${accentColor}05` }}
            animate={{ rotate: -360, scale: [1, 0.9, 1] }}
            transition={{ duration: 25, repeat: Infinity }}
          />
        </div>

        <div className="relative p-6 h-full flex flex-col">
          {/* Icon with special animation */}
          <motion.div 
            className="w-14 h-14 rounded-xl flex items-center justify-center mb-4 relative overflow-hidden flex-shrink-0"
            style={{ backgroundColor: `${accentColor}15` }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon 
              className="w-7 h-7 transition-colors duration-300" 
              style={{ color: accentColor }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: ['-100%', '200%'] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            />
          </motion.div>

          {/* Title & Description */}
          <h3 className="text-xl font-bold mb-3 text-foreground group-hover:text-opacity-90 transition-colors flex-shrink-0">
            {title}
          </h3>
          
          <p className="text-sm text-muted-foreground mb-4 leading-relaxed flex-shrink-0">
            {description}
          </p>

          {/* Status Section */}
          <div className="flex-1 flex flex-col justify-between">
            {/* Date Information */}
            <div className="mb-4">
              <div className="flex items-center text-xs text-muted-foreground mb-2">
                <Calendar className="w-3 h-3 mr-2" style={{ color: accentColor }} />
                <span>Letzte Analyse:</span>
              </div>
              <div className="text-sm font-medium text-foreground">
                {analysisDate ? formatAnalysisDate(analysisDate) : "Noch keine Analyse"}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              {/* Create New Analysis Button */}
              <motion.button
                onClick={onCreateAnalysis}
                disabled={isDisabled}
                aria-label={isLoading ? 'Analyse wird durchgeführt...' : isDisabled ? 'Analyse nicht verfügbar' : 'Neue Analyse starten'}
                className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium text-sm transition-all duration-300 ${
                  isDisabled 
                    ? "opacity-50 cursor-not-allowed bg-muted text-muted-foreground" 
                    : "text-white shadow-lg hover:shadow-xl hover:scale-[1.02]"
                }`}
                style={{ 
                  backgroundColor: isDisabled ? undefined : accentColor,
                  boxShadow: isDisabled ? undefined : `0 4px 20px ${accentColor}30`
                }}
                whileHover={!isDisabled ? { scale: 1.02 } : {}}
                whileTap={!isDisabled ? { scale: 0.98 } : {}}
              >
                {isLoading ? (
                  <>
                    <motion.div
                      className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full mr-2"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                    <span>Analysiere...</span>
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    <span>Neue Analyse</span>
                  </>
                )}
              </motion.button>

              {/* View Analysis Button */}
              {hasData && (
                <motion.button
                  onClick={onViewAnalysis}
                  disabled={isLoading}
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="w-full flex items-center justify-center px-4 py-2 rounded-lg border-2 font-medium text-sm transition-all duration-300 hover:scale-[1.02] disabled:opacity-50"
                  style={{ 
                    borderColor: accentColor,
                    color: accentColor,
                    backgroundColor: `${accentColor}10`
                  }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  <span>Analyse ansehen</span>
                  <ArrowRight className="w-3 h-3 ml-2" />
                </motion.button>
              )}
            </div>

            {/* Status Indicator */}
            <motion.div 
              className="mt-4 p-3 rounded-lg border"
              style={{ 
                borderColor: hasData ? `${accentColor}30` : '#d1d5db30',
                backgroundColor: hasData ? `${accentColor}05` : '#f9fafb05'
              }}
            >
              <div className="flex items-center">
                <motion.div
                  className="w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: hasData ? accentColor : '#9ca3af' }}
                  animate={{ scale: hasData ? [1, 1.2, 1] : 1 }}
                  transition={{ duration: 2, repeat: hasData ? Infinity : 0 }}
                />
                <span className="text-xs font-medium" style={{ color: hasData ? accentColor : '#9ca3af' }}>
                  {hasData ? "Daten verfügbar" : "Keine Daten"}
                </span>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Hover border effect */}
      <motion.div
        className="absolute inset-0 rounded-2xl border-2 pointer-events-none"
        style={{ borderColor: 'transparent' }}
        animate={isHovered ? { borderColor: `${accentColor}40` } : { borderColor: 'transparent' }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

export default AnalysisCard;
