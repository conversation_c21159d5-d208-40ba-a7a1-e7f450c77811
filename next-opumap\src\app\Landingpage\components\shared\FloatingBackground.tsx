'use client';

import React from 'react';
import styled from 'styled-components';

const FloatingBackground = () => {
  return (
    <StyledWrapper>
      <svg id="background_svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 800" fill="none" preserveAspectRatio="xMidYMid slice">
        <defs>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="var(--primary-dark)" />
            <stop offset="100%" stopColor="var(--primary-light)" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="100%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="var(--accent-dark)" />
            <stop offset="100%" stopColor="var(--accent-light)" />
          </linearGradient>
        </defs>

        {/* Dynamic Shapes inspired by loader.tsx geometric patterns */}
        <path
          className="shape float-animation"
          d="M 500 0 C 400 150, 600 250, 500 400 S 700 550, 800 400 S 900 250, 800 100 S 600 0, 500 0 Z"
          fill="url(#gradient1)"
          fillOpacity="0.1"
        />
        <circle
          className="shape float-animation-alt"
          cx="1000"
          cy="200"
          r="150"
          fill="url(#gradient2)"
          fillOpacity="0.1"
        />
        <path
          className="shape float-animation-delay"
          d="M 200 600 Q 300 750, 500 700 T 700 650 Q 600 500, 400 550 Z"
          fill="url(#gradient1)"
          fillOpacity="0.15"
        />
        <ellipse
          className="shape float-animation-longer"
          cx="300"
          cy="100"
          rx="100"
          ry="50"
          transform="rotate(45 300 100)"
          fill="url(#gradient2)"
          fillOpacity="0.1"
        />

        {/* Pulsating dots representing connection points */}
        <circle className="dot pulse-animation-1" cx="400" cy="200" r="8" fill="var(--primary)" />
        <circle className="dot pulse-animation-2" cx="700" cy="500" r="8" fill="var(--primary)" />
        <circle className="dot pulse-animation-3" cx="1100" cy="100" r="8" fill="var(--primary)" />

        {/* Connecting lines between dots */}
        <line
          className="connecting-line draw-animation-1"
          x1="400"
          y1="200"
          x2="700"
          y2="500"
          stroke="var(--primary)"
          strokeWidth="2"
          strokeDasharray="1000"
          strokeDashoffset="1000"
        />
        <line
          className="connecting-line draw-animation-2"
          x1="700"
          y1="500"
          x2="1100"
          y2="100"
          stroke="var(--primary)"
          strokeWidth="2"
          strokeDasharray="1000"
          strokeDashoffset="1000"
        />
      </svg>
    </StyledWrapper>
  );
};

const StyledWrapper = styled.div`
  position: fixed;
  inset: 0;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
  width: 100vw;
  height: 100vh;

  #background_svg {
    width: 100%;
    height: 100%;
  }

  .shape {
    transform-origin: center center;
  }

  /* Custom CSS Variables for gradients and colors, assuming these exist in the theme */
  /* If not, these would need to be defined or mapped to existing Tailwind colors */
  --primary-dark: hsl(var(--primary));
  --primary-light: hsl(var(--primary) / 0.7);
  --accent-dark: hsl(var(--accent));
  --accent-light: hsl(var(--accent) / 0.7);


  @keyframes floatAndBounce {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, -20px) rotate(5deg); }
    50% { transform: translate(-10px, 20px) rotate(-5deg); }
    75% { transform: translate(5px, -10px) rotate(2deg); }
  }

  @keyframes floatAndBounceAlt {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-15px, 10px) rotate(-3deg); }
    50% { transform: translate(15px, -10px) rotate(3deg); }
    75% { transform: translate(-5px, 5px) rotate(-1deg); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.2); opacity: 1; }
  }

  @keyframes drawLine {
    to {
      stroke-dashoffset: 0;
    }
  }

  .float-animation {
    animation: floatAndBounce 20s infinite ease-in-out;
  }

  .float-animation-alt {
    animation: floatAndBounceAlt 25s infinite ease-in-out;
  }

  .float-animation-delay {
    animation: floatAndBounce 22s infinite ease-in-out 2s; /* Add a delay */
  }

  .float-animation-longer {
    animation: floatAndBounceAlt 28s infinite ease-in-out 4s; /* Longer duration, more delay */
  }

  .pulse-animation-1 {
    animation: pulse 2s infinite ease-in-out;
  }

  .pulse-animation-2 {
    animation: pulse 2s infinite ease-in-out 0.5s;
  }

  .pulse-animation-3 {
    animation: pulse 2s infinite ease-in-out 1s;
  }

  .draw-animation-1 {
    animation: drawLine 3s ease-out forwards;
    animation-delay: 1s; /* Delay for sequential drawing */
    animation-fill-mode: forwards;
  }

  .draw-animation-2 {
    animation: drawLine 3s ease-out forwards;
    animation-delay: 3s; /* Delay for sequential drawing */
    animation-fill-mode: forwards;
  }
`;

export default FloatingBackground; 