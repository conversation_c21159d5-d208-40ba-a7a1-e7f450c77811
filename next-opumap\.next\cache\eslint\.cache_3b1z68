[{"/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-chancen-scan.ts": "1", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-gesamt-analyse.ts": "2", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-persoenliche-zielsetzung.ts": "3", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-priorisierung.ts": "4", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-strategie-briefing.ts": "5", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/relevante-aspekte-analyse.ts": "6", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/schnelle-analyse.ts": "7", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/tiefe-analyse.ts": "8", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/unternehmensanalyse.ts": "9", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/MapComponent.tsx": "10", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AdvancedMarker.tsx": "11", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisCard.tsx": "12", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisModal.tsx": "13", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisSection.tsx": "14", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/BusinessCard.tsx": "15", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/CustomSwitch.tsx": "16", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/FloatingBackgroundMap.tsx": "17", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/OpuScannerSection.tsx": "18", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/index.ts": "19", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/index.ts": "20", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useAnalysis.ts": "21", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useBusinessSelection.ts": "22", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useMapState.ts": "23", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useOpuScannerIntegration.ts": "24", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/index.tsx": "25", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/analysisStyles.ts": "26", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/businessCardStyles.ts": "27", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/index.ts": "28", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/mapStyles.ts": "29", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/opuScannerStyles.ts": "30", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPage.original.tsx": "31", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPage.tsx": "32", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPageRefactored.tsx": "33", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/Loader.tsx": "34", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/CTASection/index.tsx": "35", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/MorphingFeatureCard.tsx": "36", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/index.tsx": "37", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/HeroContent.tsx": "38", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/InteractiveMapDemo.tsx": "39", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/index.tsx": "40", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/ProcessStep.tsx": "41", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/index.tsx": "42", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/AnimatedButton.tsx": "43", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/FloatingBackground.tsx": "44", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/SectionHeader.tsx": "45", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/index.ts": "46", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/index.ts": "47", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useMapDemo.ts": "48", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useResponsiveCheck.ts": "49", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/index.tsx": "50", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/constants.ts": "51", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/index.ts": "52", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/deep/[placeId]/route.ts": "53", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/quick/[placeId]/route.ts": "54", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/route.ts": "55", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/callback/route.ts": "56", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/login/route.ts": "57", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/register/route.ts": "58", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/health/route.ts": "59", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/lab-files/route.ts": "60", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-gesamt-analyse/route.ts": "61", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-persoenliche-zielsetzung/route.ts": "62", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-priorisierung/route.ts": "63", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-strategie-briefing/route.ts": "64", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/profile/route.ts": "65", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/related-strategies/route.ts": "66", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/relevante-aspekte-analyse/route.ts": "67", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-chancen/route.ts": "68", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-results/route.ts": "69", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-status/route.ts": "70", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/[placeId]/route.ts": "71", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/all/[placeId]/route.ts": "72", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/route.ts": "73", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/selected-companies/route.ts": "74", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/strategies/route.ts": "75", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/strategy-scan-results/route.ts": "76", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/[placeId]/route.ts": "77", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/all/[placeId]/route.ts": "78", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/route.ts": "79", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/auth-code-error/page.tsx": "80", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/callback/route.ts": "81", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/confirm/page.tsx": "82", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/signout/route.ts": "83", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/forgot-password/page.tsx": "84", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/layout.tsx": "85", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/legal/impressum/page.tsx": "86", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/Login.tsx": "87", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/components/GoogleSignInButton.tsx": "88", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/index.tsx": "89", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/page.tsx": "90", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/OpulabStrategiePage.tsx": "91", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/InfoIconWithPopup.tsx": "92", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabCard.tsx": "93", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabOverlayElements.tsx": "94", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabStatusIndicator.tsx": "95", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/RelevantAspects.tsx": "96", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/SaveSettingsSection.tsx": "97", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieAnalyse.tsx": "98", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieBriefing.tsx": "99", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieInfo.tsx": "100", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieList.tsx": "101", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/index.ts": "102", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/AnalysisStatusIndicator.tsx": "103", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/Checkbox.tsx": "104", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/StartLaborButton.tsx": "105", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/TouchStartLaborButton.tsx": "106", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/index.ts": "107", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/index.ts": "108", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/opulabApi.ts": "109", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabStrategie.ts": "110", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabStrategieData.ts": "111", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabUI.ts": "112", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useStrategies.ts": "113", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/index.tsx": "114", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/page.tsx": "115", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/strategyService.ts": "116", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/styles/index.ts": "117", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/styles/opulabStyles.ts": "118", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/apiService.ts": "119", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/CompanyInfoEditor.tsx": "120", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/FooterScan.tsx": "121", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/GlitchButton.tsx": "122", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/RecognizedChancesSection.tsx": "123", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ScanButton.tsx": "124", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ScanResultModal.tsx": "125", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/SelectedCompaniesList.tsx": "126", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/StrategyForm.tsx": "127", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/StrategySelection.tsx": "128", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/index.ts": "129", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ui/ScannerStatusIndicator.tsx": "130", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useCompanyInfo.ts": "131", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useScanProcess.ts": "132", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useSelectedCompanies.ts": "133", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useStrategies.ts": "134", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/page.tsx": "135", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/types.ts": "136", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/utils/supabaseClient.ts": "137", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/utils/supabaseHelpers.ts": "138", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/page.tsx": "139", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/NotificationState.ts": "140", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/Profile.tsx": "141", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/actions/update-profile.ts": "142", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/index.tsx": "143", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/page.tsx": "144", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/reset-password/page.tsx": "145", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Footer.tsx": "146", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/MarkdownEditor.tsx": "147", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/MorphTransition.tsx": "148", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx": "149", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx": "150", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionWrapper.tsx": "151", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx": "152", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/AuthStatus.tsx": "153", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/LogoutButton.tsx": "154", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/ProtectedRoute.tsx": "155", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/DarkModeToggle.tsx": "156", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/Loader.tsx": "157", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/avatar.tsx": "158", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/badge.tsx": "159", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/button.tsx": "160", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/card.tsx": "161", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/checkbox.tsx": "162", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnly.tsx": "163", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyIcon.tsx": "164", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyImage.tsx": "165", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlySVG.tsx": "166", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/index.ts": "167", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only.tsx": "168", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/dialog.tsx": "169", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/input.tsx": "170", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/label.tsx": "171", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/notification.tsx": "172", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/progress.tsx": "173", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/radio-group.tsx": "174", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/scroll-area.tsx": "175", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/sheet.tsx": "176", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/slider.tsx": "177", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/switch.tsx": "178", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/tabs.tsx": "179", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/textarea.tsx": "180", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/tooltip.tsx": "181", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx": "182", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/apiClient.ts": "183", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/auth.ts": "184", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/db-utils.ts": "185", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/db.ts": "186", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/opulabAIClient.ts": "187", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx": "188", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/supabaseAuth.ts": "189", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/supabaseClient.ts": "190", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/utils.ts": "191", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/iconPaths.ts": "192", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/index.ts": "193", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/types/index.ts": "194", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/types/supabase.ts": "195", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/formatters.ts": "196", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/index.ts": "197", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/client.ts": "198", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/middleware.ts": "199", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/server.ts": "200", "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/userIdMapping.ts": "201"}, {"size": 2411, "mtime": 1747859739261, "results": "202", "hashOfConfig": "203"}, {"size": 1912, "mtime": 1747690904401, "results": "204", "hashOfConfig": "203"}, {"size": 2550, "mtime": 1747690904406, "results": "205", "hashOfConfig": "203"}, {"size": 4081, "mtime": 1747690904410, "results": "206", "hashOfConfig": "203"}, {"size": 4133, "mtime": 1747690904418, "results": "207", "hashOfConfig": "203"}, {"size": 6029, "mtime": 1747690904424, "results": "208", "hashOfConfig": "203"}, {"size": 9474, "mtime": 1747690908433, "results": "209", "hashOfConfig": "203"}, {"size": 8820, "mtime": 1747690908444, "results": "210", "hashOfConfig": "203"}, {"size": 3765, "mtime": 1747690908458, "results": "211", "hashOfConfig": "203"}, {"size": 7274, "mtime": 1750406272929, "results": "212", "hashOfConfig": "203"}, {"size": 8147, "mtime": 1750399679844, "results": "213", "hashOfConfig": "203"}, {"size": 8860, "mtime": 1750484653811, "results": "214", "hashOfConfig": "203"}, {"size": 2417, "mtime": 1750402051284, "results": "215", "hashOfConfig": "203"}, {"size": 4653, "mtime": 1750478448699, "results": "216", "hashOfConfig": "203"}, {"size": 16281, "mtime": 1750484653813, "results": "217", "hashOfConfig": "203"}, {"size": 11021, "mtime": 1750484653815, "results": "218", "hashOfConfig": "203"}, {"size": 9475, "mtime": 1750478448705, "results": "219", "hashOfConfig": "203"}, {"size": 12962, "mtime": 1750465331326, "results": "220", "hashOfConfig": "203"}, {"size": 301, "mtime": 1750406269814, "results": "221", "hashOfConfig": "203"}, {"size": 150, "mtime": 1744711264118, "results": "222", "hashOfConfig": "203"}, {"size": 7491, "mtime": 1747258466606, "results": "223", "hashOfConfig": "203"}, {"size": 15510, "mtime": 1747859739272, "results": "224", "hashOfConfig": "203"}, {"size": 4485, "mtime": 1750405019077, "results": "225", "hashOfConfig": "203"}, {"size": 7398, "mtime": 1747594435402, "results": "226", "hashOfConfig": "203"}, {"size": 43, "mtime": 1744711264121, "results": "227", "hashOfConfig": "203"}, {"size": 2381, "mtime": 1750402969917, "results": "228", "hashOfConfig": "203"}, {"size": 3918, "mtime": 1750402970067, "results": "229", "hashOfConfig": "203"}, {"size": 196, "mtime": 1750406269879, "results": "230", "hashOfConfig": "203"}, {"size": 4185, "mtime": 1750405016081, "results": "231", "hashOfConfig": "203"}, {"size": 1296, "mtime": 1750402970287, "results": "232", "hashOfConfig": "203"}, {"size": 52600, "mtime": 1750489737244, "results": "233", "hashOfConfig": "203"}, {"size": 50, "mtime": 1750489754781, "results": "234", "hashOfConfig": "203"}, {"size": 904, "mtime": 1750489728447, "results": "235", "hashOfConfig": "203"}, {"size": 24599, "mtime": 1750353764131, "results": "236", "hashOfConfig": "203"}, {"size": 4113, "mtime": 1750489672004, "results": "237", "hashOfConfig": "203"}, {"size": 5192, "mtime": 1750489589769, "results": "238", "hashOfConfig": "203"}, {"size": 1629, "mtime": 1750489602982, "results": "239", "hashOfConfig": "203"}, {"size": 4263, "mtime": 1750489537544, "results": "240", "hashOfConfig": "203"}, {"size": 51451, "mtime": 1750491534062, "results": "241", "hashOfConfig": "203"}, {"size": 1679, "mtime": 1750489551016, "results": "242", "hashOfConfig": "203"}, {"size": 1749, "mtime": 1750489627116, "results": "243", "hashOfConfig": "203"}, {"size": 1365, "mtime": 1750489638655, "results": "244", "hashOfConfig": "203"}, {"size": 1954, "mtime": 1750489397918, "results": "245", "hashOfConfig": "203"}, {"size": 5330, "mtime": 1750489698768, "results": "246", "hashOfConfig": "203"}, {"size": 2051, "mtime": 1750489411609, "results": "247", "hashOfConfig": "203"}, {"size": 171, "mtime": 1750489718966, "results": "248", "hashOfConfig": "203"}, {"size": 148, "mtime": 1750489353824, "results": "249", "hashOfConfig": "203"}, {"size": 1338, "mtime": 1750489346885, "results": "250", "hashOfConfig": "203"}, {"size": 348, "mtime": 1750489332074, "results": "251", "hashOfConfig": "203"}, {"size": 42, "mtime": 1744711264125, "results": "252", "hashOfConfig": "203"}, {"size": 3246, "mtime": 1750489874732, "results": "253", "hashOfConfig": "203"}, {"size": 28, "mtime": 1750489383090, "results": "254", "hashOfConfig": "203"}, {"size": 2836, "mtime": 1747593785825, "results": "255", "hashOfConfig": "203"}, {"size": 2767, "mtime": 1747593785829, "results": "256", "hashOfConfig": "203"}, {"size": 3710, "mtime": 1750464013216, "results": "257", "hashOfConfig": "203"}, {"size": 1273, "mtime": 1747859282133, "results": "258", "hashOfConfig": "203"}, {"size": 3561, "mtime": 1750464013219, "results": "259", "hashOfConfig": "203"}, {"size": 3147, "mtime": 1746371925995, "results": "260", "hashOfConfig": "203"}, {"size": 855, "mtime": 1746522714960, "results": "261", "hashOfConfig": "203"}, {"size": 4104, "mtime": 1746522714964, "results": "262", "hashOfConfig": "203"}, {"size": 4485, "mtime": 1750464013223, "results": "263", "hashOfConfig": "203"}, {"size": 3300, "mtime": 1750464013226, "results": "264", "hashOfConfig": "203"}, {"size": 3435, "mtime": 1750464013230, "results": "265", "hashOfConfig": "203"}, {"size": 3448, "mtime": 1750464013233, "results": "266", "hashOfConfig": "203"}, {"size": 5475, "mtime": 1746522714986, "results": "267", "hashOfConfig": "203"}, {"size": 2735, "mtime": 1750464013237, "results": "268", "hashOfConfig": "203"}, {"size": 3289, "mtime": 1750464013240, "results": "269", "hashOfConfig": "203"}, {"size": 19378, "mtime": 1750464013244, "results": "270", "hashOfConfig": "203"}, {"size": 3871, "mtime": 1746522714999, "results": "271", "hashOfConfig": "203"}, {"size": 6133, "mtime": 1747212853641, "results": "272", "hashOfConfig": "203"}, {"size": 2572, "mtime": 1746522715005, "results": "273", "hashOfConfig": "203"}, {"size": 2095, "mtime": 1750464013248, "results": "274", "hashOfConfig": "203"}, {"size": 3554, "mtime": 1744461072293, "results": "275", "hashOfConfig": "203"}, {"size": 7644, "mtime": 1747859739279, "results": "276", "hashOfConfig": "203"}, {"size": 9192, "mtime": 1750464013251, "results": "277", "hashOfConfig": "203"}, {"size": 3709, "mtime": 1750464013254, "results": "278", "hashOfConfig": "203"}, {"size": 2191, "mtime": 1746522715018, "results": "279", "hashOfConfig": "203"}, {"size": 2155, "mtime": 1750464013259, "results": "280", "hashOfConfig": "203"}, {"size": 3377, "mtime": 1744461117616, "results": "281", "hashOfConfig": "203"}, {"size": 2108, "mtime": 1750465512553, "results": "282", "hashOfConfig": "203"}, {"size": 4002, "mtime": 1747962077815, "results": "283", "hashOfConfig": "203"}, {"size": 4677, "mtime": 1747962077818, "results": "284", "hashOfConfig": "203"}, {"size": 599, "mtime": 1747212849652, "results": "285", "hashOfConfig": "203"}, {"size": 6656, "mtime": 1750373212688, "results": "286", "hashOfConfig": "203"}, {"size": 2965, "mtime": 1750405123545, "results": "287", "hashOfConfig": "203"}, {"size": 2404, "mtime": 1750349703521, "results": "288", "hashOfConfig": "203"}, {"size": 18522, "mtime": 1750490068850, "results": "289", "hashOfConfig": "203"}, {"size": 2646, "mtime": 1746546860738, "results": "290", "hashOfConfig": "203"}, {"size": 36, "mtime": 1744711264127, "results": "291", "hashOfConfig": "203"}, {"size": 210, "mtime": 1750478448716, "results": "292", "hashOfConfig": "203"}, {"size": 18912, "mtime": 1750490133042, "results": "293", "hashOfConfig": "203"}, {"size": 2497, "mtime": 1745960615721, "results": "294", "hashOfConfig": "203"}, {"size": 4463, "mtime": 1750465389648, "results": "295", "hashOfConfig": "203"}, {"size": 7302, "mtime": 1750464012269, "results": "296", "hashOfConfig": "203"}, {"size": 9148, "mtime": 1750484653820, "results": "297", "hashOfConfig": "203"}, {"size": 2000, "mtime": 1745364937635, "results": "298", "hashOfConfig": "203"}, {"size": 5332, "mtime": 1750444854691, "results": "299", "hashOfConfig": "203"}, {"size": 13683, "mtime": 1750444852234, "results": "300", "hashOfConfig": "203"}, {"size": 2543, "mtime": 1750444851939, "results": "301", "hashOfConfig": "203"}, {"size": 4625, "mtime": 1750444852045, "results": "302", "hashOfConfig": "203"}, {"size": 4813, "mtime": 1750444854608, "results": "303", "hashOfConfig": "203"}, {"size": 736, "mtime": 1750446457343, "results": "304", "hashOfConfig": "203"}, {"size": 1791, "mtime": 1745666809370, "results": "305", "hashOfConfig": "203"}, {"size": 9793, "mtime": 1745007963768, "results": "306", "hashOfConfig": "203"}, {"size": 4658, "mtime": 1745654446627, "results": "307", "hashOfConfig": "203"}, {"size": 5166, "mtime": 1750465446432, "results": "308", "hashOfConfig": "203"}, {"size": 276, "mtime": 1746546860745, "results": "309", "hashOfConfig": "203"}, {"size": 34, "mtime": 1745371178783, "results": "310", "hashOfConfig": "203"}, {"size": 7383, "mtime": 1750484653822, "results": "311", "hashOfConfig": "203"}, {"size": 23649, "mtime": 1750464013283, "results": "312", "hashOfConfig": "203"}, {"size": 10874, "mtime": 1747581724269, "results": "313", "hashOfConfig": "203"}, {"size": 2702, "mtime": 1747212849671, "results": "314", "hashOfConfig": "203"}, {"size": 7589, "mtime": 1747212853672, "results": "315", "hashOfConfig": "203"}, {"size": 50, "mtime": 1745007963771, "results": "316", "hashOfConfig": "203"}, {"size": 1302, "mtime": 1747212849674, "results": "317", "hashOfConfig": "203"}, {"size": 5361, "mtime": 1747212853676, "results": "318", "hashOfConfig": "203"}, {"size": 33, "mtime": 1744916734643, "results": "319", "hashOfConfig": "203"}, {"size": 4080, "mtime": 1750446433629, "results": "320", "hashOfConfig": "203"}, {"size": 13688, "mtime": 1747859739291, "results": "321", "hashOfConfig": "203"}, {"size": 2081, "mtime": 1750442358420, "results": "322", "hashOfConfig": "203"}, {"size": 2849, "mtime": 1750442574099, "results": "323", "hashOfConfig": "203"}, {"size": 10858, "mtime": 1744711264128, "results": "324", "hashOfConfig": "203"}, {"size": 4456, "mtime": 1750484653824, "results": "325", "hashOfConfig": "203"}, {"size": 1629, "mtime": 1750442623826, "results": "326", "hashOfConfig": "203"}, {"size": 3407, "mtime": 1750442662606, "results": "327", "hashOfConfig": "203"}, {"size": 2716, "mtime": 1750442422883, "results": "328", "hashOfConfig": "203"}, {"size": 2778, "mtime": 1744749720500, "results": "329", "hashOfConfig": "203"}, {"size": 8909, "mtime": 1750465241055, "results": "330", "hashOfConfig": "203"}, {"size": 365, "mtime": 1750441916402, "results": "331", "hashOfConfig": "203"}, {"size": 1871, "mtime": 1745873504524, "results": "332", "hashOfConfig": "203"}, {"size": 4279, "mtime": 1747212849683, "results": "333", "hashOfConfig": "203"}, {"size": 8929, "mtime": 1750464013293, "results": "334", "hashOfConfig": "203"}, {"size": 3735, "mtime": 1750406905730, "results": "335", "hashOfConfig": "203"}, {"size": 3789, "mtime": 1747859739304, "results": "336", "hashOfConfig": "203"}, {"size": 13177, "mtime": 1750490115188, "results": "337", "hashOfConfig": "203"}, {"size": 1929, "mtime": 1750442515374, "results": "338", "hashOfConfig": "203"}, {"size": 0, "mtime": 1746546860763, "results": "339", "hashOfConfig": "203"}, {"size": 0, "mtime": 1746546860764, "results": "340", "hashOfConfig": "203"}, {"size": 2088, "mtime": 1747859739311, "results": "341", "hashOfConfig": "203"}, {"size": 804, "mtime": 1745873504528, "results": "342", "hashOfConfig": "203"}, {"size": 18546, "mtime": 1750490088720, "results": "343", "hashOfConfig": "203"}, {"size": 2150, "mtime": 1747212853691, "results": "344", "hashOfConfig": "203"}, {"size": 38, "mtime": 1744711264133, "results": "345", "hashOfConfig": "203"}, {"size": 274, "mtime": 1750408289739, "results": "346", "hashOfConfig": "203"}, {"size": 24846, "mtime": 1750470423490, "results": "347", "hashOfConfig": "203"}, {"size": 4541, "mtime": 1750349703521, "results": "348", "hashOfConfig": "203"}, {"size": 1149, "mtime": 1750442346109, "results": "349", "hashOfConfig": "203"}, {"size": 4661, "mtime": 1747212853694, "results": "350", "hashOfConfig": "203"}, {"size": 13529, "mtime": 1750484669366, "results": "351", "hashOfConfig": "203"}, {"size": 880, "mtime": 1745364937639, "results": "352", "hashOfConfig": "203"}, {"size": 3402, "mtime": 1747212853698, "results": "353", "hashOfConfig": "203"}, {"size": 3584, "mtime": 1747212849699, "results": "354", "hashOfConfig": "203"}, {"size": 1576, "mtime": 1750373202301, "results": "355", "hashOfConfig": "203"}, {"size": 1162, "mtime": 1746522711095, "results": "356", "hashOfConfig": "203"}, {"size": 2065, "mtime": 1747859739321, "results": "357", "hashOfConfig": "203"}, {"size": 899, "mtime": 1750381803589, "results": "358", "hashOfConfig": "203"}, {"size": 5316, "mtime": 1747729572371, "results": "359", "hashOfConfig": "203"}, {"size": 1272, "mtime": 1747212849704, "results": "360", "hashOfConfig": "203"}, {"size": 1206, "mtime": 1745661468738, "results": "361", "hashOfConfig": "203"}, {"size": 2063, "mtime": 1750444851827, "results": "362", "hashOfConfig": "203"}, {"size": 1986, "mtime": 1744916734645, "results": "363", "hashOfConfig": "203"}, {"size": 1100, "mtime": 1744916734646, "results": "364", "hashOfConfig": "203"}, {"size": 801, "mtime": 1746522711103, "results": "365", "hashOfConfig": "203"}, {"size": 988, "mtime": 1746522711104, "results": "366", "hashOfConfig": "203"}, {"size": 1112, "mtime": 1747212849706, "results": "367", "hashOfConfig": "203"}, {"size": 723, "mtime": 1746522711104, "results": "368", "hashOfConfig": "203"}, {"size": 244, "mtime": 1747212849708, "results": "369", "hashOfConfig": "203"}, {"size": 1767, "mtime": 1746546860775, "results": "370", "hashOfConfig": "203"}, {"size": 4001, "mtime": 1745007963772, "results": "371", "hashOfConfig": "203"}, {"size": 850, "mtime": 1747212849711, "results": "372", "hashOfConfig": "203"}, {"size": 610, "mtime": 1744916734646, "results": "373", "hashOfConfig": "203"}, {"size": 1379, "mtime": 1750416611736, "results": "374", "hashOfConfig": "203"}, {"size": 771, "mtime": 1745873504531, "results": "375", "hashOfConfig": "203"}, {"size": 1508, "mtime": 1747212849712, "results": "376", "hashOfConfig": "203"}, {"size": 1645, "mtime": 1744496383422, "results": "377", "hashOfConfig": "203"}, {"size": 4137, "mtime": 1744642151252, "results": "378", "hashOfConfig": "203"}, {"size": 1144, "mtime": 1744916734647, "results": "379", "hashOfConfig": "203"}, {"size": 1182, "mtime": 1744701978591, "results": "380", "hashOfConfig": "203"}, {"size": 2035, "mtime": 1745873504531, "results": "381", "hashOfConfig": "203"}, {"size": 797, "mtime": 1747212849714, "results": "382", "hashOfConfig": "203"}, {"size": 1172, "mtime": 1745661705075, "results": "383", "hashOfConfig": "203"}, {"size": 18299, "mtime": 1750406991379, "results": "384", "hashOfConfig": "203"}, {"size": 1866, "mtime": 1747260083664, "results": "385", "hashOfConfig": "203"}, {"size": 5001, "mtime": 1750464013310, "results": "386", "hashOfConfig": "203"}, {"size": 898, "mtime": 1746522711109, "results": "387", "hashOfConfig": "203"}, {"size": 2982, "mtime": 1747212853720, "results": "388", "hashOfConfig": "203"}, {"size": 258, "mtime": 1746543589432, "results": "389", "hashOfConfig": "203"}, {"size": 883, "mtime": 1747212849722, "results": "390", "hashOfConfig": "203"}, {"size": 4210, "mtime": 1750464013313, "results": "391", "hashOfConfig": "203"}, {"size": 1307, "mtime": 1747260083664, "results": "392", "hashOfConfig": "203"}, {"size": 166, "mtime": 1744496379305, "results": "393", "hashOfConfig": "203"}, {"size": 456, "mtime": 1750402970489, "results": "394", "hashOfConfig": "203"}, {"size": 30, "mtime": 1744711264135, "results": "395", "hashOfConfig": "203"}, {"size": 2672, "mtime": 1750464012316, "results": "396", "hashOfConfig": "203"}, {"size": 2733, "mtime": 1747859735330, "results": "397", "hashOfConfig": "203"}, {"size": 2347, "mtime": 1747859735336, "results": "398", "hashOfConfig": "203"}, {"size": 31, "mtime": 1744711264136, "results": "399", "hashOfConfig": "203"}, {"size": 239, "mtime": 1747733502116, "results": "400", "hashOfConfig": "203"}, {"size": 3280, "mtime": 1750407010379, "results": "401", "hashOfConfig": "203"}, {"size": 825, "mtime": 1747962073837, "results": "402", "hashOfConfig": "203"}, {"size": 535, "mtime": 1746522711124, "results": "403", "hashOfConfig": "203"}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sgcy91", {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-chancen-scan.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-gesamt-analyse.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-persoenliche-zielsetzung.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-priorisierung.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/opulab-strategie-briefing.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/relevante-aspekte-analyse.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/schnelle-analyse.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/tiefe-analyse.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/api/unternehmensanalyse.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/MapComponent.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AdvancedMarker.tsx", [], ["1007"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisCard.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisModal.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/AnalysisSection.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/BusinessCard.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/CustomSwitch.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/FloatingBackgroundMap.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/OpuScannerSection.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/components/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useAnalysis.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useBusinessSelection.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useMapState.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/hooks/useOpuScannerIntegration.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/analysisStyles.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/businessCardStyles.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/mapStyles.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Homepage/styles/opuScannerStyles.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPage.original.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPage.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/LandingPageRefactored.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/Loader.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/CTASection/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/MorphingFeatureCard.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/FeaturesSection/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/HeroContent.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/InteractiveMapDemo.tsx", ["1008"], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/HeroSection/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/ProcessStep.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/ProcessSection/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/AnimatedButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/FloatingBackground.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/SectionHeader.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/components/shared/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useMapDemo.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/hooks/useResponsiveCheck.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/constants.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/Landingpage/utils/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/deep/[placeId]/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/quick/[placeId]/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/analyses/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/callback/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/login/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/auth/register/route.ts", [], ["1009"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/health/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/lab-files/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-gesamt-analyse/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-persoenliche-zielsetzung/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-priorisierung/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/opulab-strategie-briefing/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/profile/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/related-strategies/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/relevante-aspekte-analyse/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-chancen/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-results/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/scan-status/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/[placeId]/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/all/[placeId]/route.ts", [], ["1010"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/schnelle-analyse/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/selected-companies/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/strategies/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/strategy-scan-results/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/[placeId]/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/all/[placeId]/route.ts", [], ["1011"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/api/tiefe-analyse/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/auth-code-error/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/callback/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/confirm/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/auth/signout/route.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/forgot-password/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/layout.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/legal/impressum/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/Login.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/components/GoogleSignInButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/login/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/OpulabStrategiePage.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/InfoIconWithPopup.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabCard.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabOverlayElements.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/LabStatusIndicator.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/RelevantAspects.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/SaveSettingsSection.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieAnalyse.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieBriefing.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieInfo.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/StrategieList.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/AnalysisStatusIndicator.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/Checkbox.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/StartLaborButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/TouchStartLaborButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/components/ui/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/opulabApi.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabStrategie.ts", [], ["1012"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabStrategieData.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useOpulabUI.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/hooks/useStrategies.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/strategyService.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/styles/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opulab/styles/opulabStyles.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/apiService.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/CompanyInfoEditor.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/FooterScan.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/GlitchButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/RecognizedChancesSection.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ScanButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ScanResultModal.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/SelectedCompaniesList.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/StrategyForm.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/StrategySelection.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/components/ui/ScannerStatusIndicator.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useCompanyInfo.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useScanProcess.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useSelectedCompanies.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/hooks/useStrategies.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/types.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/utils/supabaseClient.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/opuscanner/utils/supabaseHelpers.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/NotificationState.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/Profile.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/actions/update-profile.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/index.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/profile/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/app/reset-password/page.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Footer.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/MarkdownEditor.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/MorphTransition.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/Navbar.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionContainer.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/PageTransitionWrapper.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/SessionExpiredModal.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/AuthStatus.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/LogoutButton.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/auth/ProtectedRoute.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/DarkModeToggle.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/Loader.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/avatar.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/badge.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/button.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/card.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/checkbox.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnly.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyIcon.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlyImage.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/ClientOnlySVG.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/client-only.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/dialog.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/input.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/label.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/notification.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/progress.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/radio-group.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/scroll-area.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/sheet.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/slider.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/switch.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/tabs.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/textarea.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/components/ui/tooltip.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/contexts/AuthContext.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/apiClient.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/auth.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/db-utils.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/db.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/opulabAIClient.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/registry.tsx", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/supabaseAuth.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/supabaseClient.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/lib/utils.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/iconPaths.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/styles/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/types/index.ts", [], ["1013"], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/types/supabase.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/formatters.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/index.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/client.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/middleware.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/supabase/server.ts", [], [], "/mnt/c/Users/<USER>/Documents/VS-Code/OpuMap-nextJS/next-opumap/src/utils/userIdMapping.ts", [], [], {"ruleId": "1014", "severity": 1, "message": "1015", "line": 103, "column": 8, "nodeType": "1016", "endLine": 103, "endColumn": 39, "suggestions": "1017", "suppressions": "1018"}, {"ruleId": "1019", "severity": 2, "message": "1020", "line": 1059, "column": 37, "nodeType": null, "messageId": "1021", "endLine": 1059, "endColumn": 42}, {"ruleId": "1019", "severity": 2, "message": "1022", "line": 17, "column": 50, "nodeType": null, "messageId": "1021", "endLine": 17, "endColumn": 62, "suppressions": "1023"}, {"ruleId": "1024", "severity": 2, "message": "1025", "line": 21, "column": 58, "nodeType": "1026", "messageId": "1027", "endLine": 21, "endColumn": 61, "suggestions": "1028", "suppressions": "1029"}, {"ruleId": "1024", "severity": 2, "message": "1025", "line": 21, "column": 58, "nodeType": "1026", "messageId": "1027", "endLine": 21, "endColumn": 61, "suggestions": "1030", "suppressions": "1031"}, {"ruleId": "1014", "severity": 1, "message": "1032", "line": 123, "column": 8, "nodeType": "1016", "endLine": 123, "endColumn": 10, "suggestions": "1033", "suppressions": "1034"}, {"ruleId": "1035", "severity": 2, "message": "1036", "line": 65, "column": 5, "nodeType": "1037", "messageId": "1038", "endLine": 69, "endColumn": 6, "suppressions": "1039"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'anchorAbove' and 'className'. Either include them or remove the dependency array.", "ArrayExpression", ["1040"], ["1041"], "@typescript-eslint/no-unused-vars", "'index' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "'passwordHash' is defined but never used. Allowed unused args must match /^_/u.", ["1042"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1043", "1044"], ["1045"], ["1046", "1047"], ["1048"], "React Hook useEffect has missing dependencies: 'loadStrategies' and 'showMessage'. Either include them or remove the dependency array.", ["1049"], ["1050"], "@typescript-eslint/no-namespace", "ES2015 module syntax is preferred over namespaces.", "TSModuleDeclaration", "moduleSyntaxIsPreferred", ["1051"], {"desc": "1052", "fix": "1053"}, {"kind": "1054", "justification": "1055"}, {"kind": "1054", "justification": "1055"}, {"messageId": "1056", "fix": "1057", "desc": "1058"}, {"messageId": "1059", "fix": "1060", "desc": "1061"}, {"kind": "1054", "justification": "1055"}, {"messageId": "1056", "fix": "1062", "desc": "1058"}, {"messageId": "1059", "fix": "1063", "desc": "1061"}, {"kind": "1054", "justification": "1055"}, {"desc": "1064", "fix": "1065"}, {"kind": "1054", "justification": "1055"}, {"kind": "1054", "justification": "1055"}, "Update the dependencies array to be: [anchorAbove, className, map, markerLibrary, numChilds]", {"range": "1066", "text": "1067"}, "directive", "", "suggestUnknown", {"range": "1068", "text": "1069"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1070", "text": "1071"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1072", "text": "1069"}, {"range": "1073", "text": "1071"}, "Update the dependencies array to be: [loadStrategies, showMessage]", {"range": "1074", "text": "1075"}, [3511, 3542], "[anchorAbove, className, map, markerLibrary, numChilds]", [765, 768], "unknown", [765, 768], "never", [762, 765], [762, 765], [4680, 4682], "[loadStrategies, showMessage]"]