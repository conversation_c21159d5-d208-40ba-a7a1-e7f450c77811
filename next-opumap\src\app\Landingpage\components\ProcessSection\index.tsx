'use client';

import React from 'react';
import { SectionHeader } from '../shared';
import { ProcessStep } from './ProcessStep';
import { PROCESS_STEPS } from '../../utils';

export const ProcessSection: React.FC = () => {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-32 relative">
      <div className="container mx-auto px-4">
        <SectionHeader
          title={
            <>
              Der OpuMap-Prozess: <span className="text-primary">Von der Strategie zur Partnerschaft</span>
            </>
          }
          maxWidth="max-w-3xl"
        />
        
        <div className="relative max-w-6xl mx-auto">
          {/* Connection Line */}
          <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-primary/20 via-primary to-primary/20 transform -translate-y-1/2 hidden lg:block"></div>
          
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {PROCESS_STEPS.map((step, index) => (
              <ProcessStep
                key={index}
                step={step.step}
                icon={step.icon}
                title={step.title}
                description={step.description}
                index={index}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};