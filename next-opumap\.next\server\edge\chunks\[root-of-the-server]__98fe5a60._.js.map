{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/utils/supabase/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { NextRequest, NextResponse } from 'next/server';\r\n\r\n// Cache the Supabase URL and key to avoid environment variable lookups\r\nconst SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;\r\nconst SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\r\n\r\nexport async function updateSession(request: NextRequest) {\r\n  let supabaseResponse = NextResponse.next({\r\n    request,\r\n  });\r\n\r\n  const supabase = createServerClient(\r\n    SUPABASE_URL,\r\n    SUPABASE_ANON_KEY,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return request.cookies.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));\r\n          supabaseResponse = NextResponse.next({\r\n            request,\r\n          });\r\n          cookiesToSet.forEach(({ name, value, options }) =>\r\n            supabaseResponse.cookies.set(name, value, options)\r\n          );\r\n        },\r\n      },\r\n    }\r\n  );\r\n\r\n  // IMPORTANT: Avoid writing any logic between createServerClient and\r\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\r\n  // issues with users being randomly logged out.\r\n\r\n  try {\r\n    console.log('Middleware: Refreshing session for:', request.nextUrl.pathname);\r\n    const {\r\n      data: { user },\r\n    } = await supabase.auth.getUser();\r\n    \r\n    if (user) {\r\n      console.log('Middleware: User session found:', user.id);\r\n    } else {\r\n      console.log('Middleware: No user session found');\r\n    }\r\n\r\n    // Protected routes that require authentication\r\n    const protectedRoutes = ['/profile', '/opuscanner', '/opulab'];\r\n    const isProtectedRoute = protectedRoutes.some(route => \r\n      request.nextUrl.pathname.startsWith(route)\r\n    );\r\n\r\n    // Routes that should be excluded from auth checks (public pages that need special handling)\r\n    const publicRoutes = ['/login', '/auth', '/reset-password', '/forgot-password'];\r\n    const isPublicRoute = publicRoutes.some(route => \r\n      request.nextUrl.pathname.startsWith(route)\r\n    );\r\n\r\n    // Only redirect to login for protected routes without user, but skip public routes entirely\r\n    if (\r\n      !user &&\r\n      isProtectedRoute &&\r\n      !isPublicRoute\r\n    ) {\r\n      console.log('Middleware: Redirecting to login for protected route');\r\n      const url = request.nextUrl.clone();\r\n      url.pathname = '/login';\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('Middleware: Error refreshing session:', error);\r\n  }\r\n\r\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\r\n  // If you're creating a new response object with NextResponse.next() make sure to:\r\n  // 1. Pass the request in it, like so:\r\n  //    const myNewResponse = NextResponse.next({ request })\r\n  // 2. Copy over the cookies, like so:\r\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\r\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\r\n  //    the cookies!\r\n  // 4. Finally:\r\n  //    return myNewResponse\r\n  // If this is not done, you may be causing the browser and server to go out\r\n  // of sync and terminate the user's session prematurely!\r\n\r\n  return supabaseResponse;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEA,uEAAuE;AACvE,MAAM;AACN,MAAM;AAEC,eAAe,cAAc,OAAoB;IACtD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,EAChC,cACA,mBACA;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBACpE,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,oEAAoE;IACpE,6EAA6E;IAC7E,+CAA+C;IAE/C,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC,QAAQ,OAAO,CAAC,QAAQ;QAC3E,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,MAAM;YACR,QAAQ,GAAG,CAAC,mCAAmC,KAAK,EAAE;QACxD,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,+CAA+C;QAC/C,MAAM,kBAAkB;YAAC;YAAY;YAAe;SAAU;QAC9D,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;QAGtC,4FAA4F;QAC5F,MAAM,eAAe;YAAC;YAAU;YAAS;YAAmB;SAAmB;QAC/E,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;QAGtC,4FAA4F;QAC5F,IACE,CAAC,QACD,oBACA,CAAC,eACD;YACA,QAAQ,GAAG,CAAC;YACZ,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;IACzD;IAEA,qEAAqE;IACrE,kFAAkF;IAClF,sCAAsC;IACtC,0DAA0D;IAC1D,qCAAqC;IACrC,qEAAqE;IACrE,2EAA2E;IAC3E,kBAAkB;IAClB,cAAc;IACd,0BAA0B;IAC1B,2EAA2E;IAC3E,wDAAwD;IAExD,OAAO;AACT"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { updateSession } from './src/utils/supabase/middleware';\r\n\r\nexport async function middleware(req: NextRequest) {\r\n  // Always run updateSession for proper cookie synchronization\r\n  // This is crucial for Supabase SSR to work correctly\r\n  console.log('Middleware: Processing request for:', req.nextUrl.pathname);\r\n  \r\n  try {\r\n    const result = await updateSession(req);\r\n    console.log('Middleware: updateSession completed successfully');\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Middleware: Error in updateSession:', error);\r\n    return NextResponse.next();\r\n  }\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     * - api routes (these handle their own auth)\r\n     * Feel free to modify this pattern to include more paths.\r\n     */\r\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\r\n  ]\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,eAAe,WAAW,GAAgB;IAC/C,6DAA6D;IAC7D,qDAAqD;IACrD,QAAQ,GAAG,CAAC,uCAAuC,IAAI,OAAO,CAAC,QAAQ;IAEvE,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;QACnC,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}