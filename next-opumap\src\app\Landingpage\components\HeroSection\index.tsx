'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { HeroContent } from './HeroContent';
import { InteractiveMapDemo } from './InteractiveMapDemo';

interface HeroSectionProps {
  onGetStarted: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ onGetStarted }) => {
  return (
    <section className="relative py-6 sm:py-8 md:py-10 lg:py-16 xl:py-20 overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center">
          {/* Left: Content */}
          <HeroContent onGetStarted={onGetStarted} />
          
          {/* Right: Interactive Demo */}
          <motion.div
            initial={{ opacity: 0, x: 50, rotateY: 15 }}
            animate={{ opacity: 1, x: 0, rotateY: 0 }}
            transition={{ delay: 0.6, duration: 1 }}
            className="relative order-first lg:order-last"
          >
            <InteractiveMapDemo />
            
            {/* Floating Stats */}
            <motion.div
              className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 bg-background/90 backdrop-blur-md rounded-xl sm:rounded-2xl p-2 sm:p-4 border border-border/30 shadow-lg"
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              <div className="text-lg sm:text-2xl font-bold text-primary">94%</div>
              <div className="text-xs text-muted-foreground">Erfolgsrate</div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};