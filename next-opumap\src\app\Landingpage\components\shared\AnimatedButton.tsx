'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

interface AnimatedButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
  showArrow?: boolean;
  className?: string;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  onClick,
  children,
  variant = 'primary',
  size = 'medium',
  showArrow = false,
  className = ''
}) => {
  const baseClasses = "group relative overflow-hidden transition-all duration-300 font-semibold rounded-xl";
  
  const variantClasses = {
    primary: "bg-primary text-primary-foreground hover:shadow-xl hover:shadow-primary/25 hover:scale-105",
    secondary: "border-2 border-border/50 bg-background/50 backdrop-blur-sm hover:bg-accent hover:border-primary/30 hover:scale-105"
  };

  const sizeClasses = {
    small: "px-4 py-2 text-sm",
    medium: "px-6 sm:px-8 py-3 sm:py-4 text-sm sm:text-base",
    large: "px-8 sm:px-10 py-4 sm:py-5 text-base sm:text-lg"
  };

  return (
    <button 
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      <span className="relative z-10 flex items-center justify-center gap-2">
        {children}
        {showArrow && (
          <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform" />
        )}
      </span>
      {variant === 'primary' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-primary via-primary/90 to-primary"
          animate={{ x: ['-100%', '100%'] }}
          transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}
          style={{ background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)' }}
        />
      )}
    </button>
  );
};