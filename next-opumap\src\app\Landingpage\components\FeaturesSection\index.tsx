'use client';

import React from 'react';
import { Eye } from 'lucide-react';
import { SectionHeader } from '../shared';
import { MorphingFeatureCard } from './MorphingFeatureCard';
import { UNIQUE_FEATURES } from '../../utils';

export const FeaturesSection: React.FC = () => {
  return (
    <section id="features" className="py-8 sm:py-12 md:py-16 lg:py-20 xl:py-32 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <SectionHeader
          badge={{
            icon: Eye,
            text: "Einzigartige Technologie"
          }}
          title={
            <>
              Warum OpuMap anders ist als <br className="hidden sm:block" />
              <span className="text-primary">alles andere auf dem Markt</span>
            </>
          }
          description="Während andere Tools oberflächliche Kontaktlisten bieten, schafft OpuMap echte strategische Intelligenz. Unsere KI versteht Ihr Geschäftsmodell und findet nicht nur Kontakte, sondern echte Geschäftschancen."
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto">
          {UNIQUE_FEATURES.map((feature, index) => (
            <MorphingFeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              details={feature.details}
              delay={feature.delay}
              accentColor={feature.accentColor}
            />
          ))}
        </div>
      </div>
    </section>
  );
};