'use client';
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, ArrowRight } from 'lucide-react';
import { DisplayScanResult } from '../types';
import { motion } from 'framer-motion';
import CustomSwitch from '@/app/Homepage/components/CustomSwitch';

interface RecognizedChancesSectionProps {
  scanResults: DisplayScanResult[];
  isLoading: boolean;
  onResultClick: (result: DisplayScanResult) => void;
  showAllCompanies: boolean;
  onToggleChange: (checked: boolean) => void;
}

export const RecognizedChancesSection: React.FC<RecognizedChancesSectionProps> = ({ 
  scanResults,
  isLoading, 
  onResultClick,
  showAllCompanies,
  onToggleChange
}) => {
  return (
    <motion.div 
      className="bg-card/60 backdrop-blur-md border border-border/30 rounded-2xl p-5 shadow-lg"
      layout
    >
      <CardHeader className="p-0 mb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl font-bold text-white">
            Scan Ergebnisse
          </CardTitle>
          <div className="flex items-center space-x-3">
            <label htmlFor="toggleView" className="text-sm text-muted-foreground transition-colors duration-300"
              style={{ color: showAllCompanies ? 'white' : '' }}
            >
              Alle Ergebnisse für Strategie
            </label>
            <CustomSwitch
              checked={showAllCompanies}
              onCheckedChange={onToggleChange}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 min-h-[150px] relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-card/20 rounded-lg">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
        {!isLoading && scanResults.length === 0 && (
          <div className="flex items-center justify-center h-full text-center text-muted-foreground p-4 border border-dashed border-border/30 rounded-lg">
            Keine Scan-Ergebnisse für diese Strategie gefunden.
            <br/>
            Starten Sie einen Scan, um Potenziale zu entdecken.
          </div>
        )}
        {!isLoading && scanResults.length > 0 && (
          <motion.div 
            className="space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.1 }}
          >
            {scanResults.map((result) => (
              <motion.button
                key={`result-${result.id}-${result.place_id}`}
                onClick={() => onResultClick(result)}
                className="w-full text-left p-4 border border-border/40 rounded-lg transition-all duration-300 hover:bg-white/10 hover:border-primary/50 group focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                whileHover={{ scale: 1.02 }}
                aria-label={`View details for ${result.title || 'opportunity'} at ${result.companyName || 'unknown company'}`}
                role="button"
                tabIndex={0}
              >
                <div className="flex justify-between items-center">
                    <div>
                        <Badge variant="secondary" className="bg-teal-400/20 text-teal-300 border-none mb-2">
                            {result.companyName || 'Unbekannt'}
                        </Badge>
                        <p className="font-semibold text-white truncate">
                          {result.title || 'Erkannte Chance'}
                        </p>
                    </div>
                    <ArrowRight className="w-5 h-5 text-muted-foreground transition-transform duration-300 group-hover:translate-x-1 group-hover:text-primary"/>
                </div>
                <p className="text-sm text-muted-foreground mt-2 truncate">
                  {result.description ? result.description.substring(0, 100) + '...' : 'Klicken um Details zu sehen...'}
                </p>
              </motion.button>
            ))}
          </motion.div>
        )}
      </CardContent>
    </motion.div>
  );
};
