'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { useResponsiveCheck } from '../../hooks';

interface MorphingFeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  details: string[];
  delay: number;
  accentColor: string;
}

export const MorphingFeatureCard: React.FC<MorphingFeatureCardProps> = ({ 
  icon: Icon, 
  title, 
  description, 
  details, 
  delay, 
  accentColor 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isTouchDevice } = useResponsiveCheck();

  const handleClick = () => {
    if (isTouchDevice) {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, rotateY: -15 }}
      whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, delay, ease: [0.68, -0.55, 0.265, 1.55] }}
      className="group relative overflow-hidden"
      onMouseEnter={() => !isTouchDevice && setIsExpanded(true)}
      onMouseLeave={() => !isTouchDevice && setIsExpanded(false)}
      onClick={handleClick}
    >
      <div className="relative bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-md rounded-xl sm:rounded-2xl border border-border/30 overflow-hidden transition-all duration-500 group-hover:border-opacity-60 h-full min-h-[320px] sm:min-h-[350px] md:min-h-[380px] flex flex-col">
        {/* Accent Border */}
        <div 
          className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl sm:rounded-2xl"
          style={{ background: `linear-gradient(135deg, ${accentColor}20, transparent)` }}
        />
        
        <div className="relative p-3 sm:p-4 md:p-6 lg:p-8 h-full flex flex-col overflow-hidden">
          {/* Icon */}
          <motion.div 
            className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 rounded-lg sm:rounded-xl md:rounded-2xl flex items-center justify-center mb-3 sm:mb-4 md:mb-6 relative overflow-hidden flex-shrink-0"
            style={{ backgroundColor: `${accentColor}15` }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon 
              className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 transition-colors duration-300" 
              style={{ color: accentColor }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{ x: ['-100%', '200%'] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            />
          </motion.div>

          {/* Title */}
          <h3 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 md:mb-4 text-foreground group-hover:text-opacity-90 transition-colors flex-shrink-0">
            {title}
          </h3>
          
          {/* Description */}
          <p className="text-sm sm:text-base text-muted-foreground mb-3 sm:mb-4 md:mb-6 leading-relaxed flex-shrink-0">
            {description}
          </p>

          {/* Expandable Details */}
          <div className="flex-1 min-h-0">
            <motion.div
              initial={false}
              animate={{ 
                height: isExpanded ? 'auto' : 0, 
                opacity: isExpanded ? 1 : 0 
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="space-y-1.5 sm:space-y-2 md:space-y-3 pt-2 border-t border-border/20">
                {details.map((detail, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: isExpanded ? 1 : 0, x: isExpanded ? 0 : -20 }}
                    transition={{ delay: i * 0.1 }}
                    className="flex items-start space-x-2 sm:space-x-3"
                  >
                    <div 
                      className="w-1 h-1 sm:w-1.5 sm:h-1.5 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"
                      style={{ backgroundColor: accentColor }}
                    />
                    <span className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                      {detail}
                    </span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Hover Indicator */}
          <motion.div 
            className="pt-2 sm:pt-3 md:pt-4 text-xs font-medium opacity-0 group-hover:opacity-60 transition-opacity flex-shrink-0"
            style={{ color: accentColor }}
          >
            <span className="hidden sm:inline">Mehr Details anzeigen</span>
            <span className="sm:hidden">Details anzeigen</span>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};